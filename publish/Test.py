# -*- coding: utf8 -*-
import CSGFile
import CSGString
import CSG<PERSON>CTools
import zipfile
import random
import os
import json
import gtools.util.file as util_file
# import plistlib


def unzip_file(zip_src, dst_dir):
    r = zipfile.is_zipfile(zip_src)
    if r:
        fz = zipfile.ZipFile(zip_src, 'r')
        for file in fz.namelist():
            fz.extract(file, dst_dir)
    else:
        print('This is not zip')

def getRandomImageNameFromLexicon():
    image_name = CSGString.getRandomStr().lower()
    if image_name is None:
        getRandomImageNameFromLexicon()
    return image_name

# MD5值修改
def fileAppend(filename):
    myfile = open(filename,'a')
    empty_count = random.randint(3, 15)
    time_str = ''
    for i in range(empty_count):
        time_str = time_str + '\n'
    myfile.write(time_str)
    myfile.close

# pro_path 工程主目录，   restore_config_path 还原配置文件路径，  is_restore 是否是还原模式
def modifyJsonConfigName(pro_path, restore_config_path, is_restore):
    json_config_data = util_file.read_file(restore_config_path)
    tmp_dic = json.loads(json_config_data)
    new_temp_dic = {}
    random_str = CSGString.generate_random_str(4)
    for key ,value in tmp_dic.items():
        current_random_str = getRandomImageNameFromLexicon()
        random_key = random_str + current_random_str
        new_temp_dic[key] = random_key
    
    files = []
    CSGFile.files_in_dir(files, pro_path, [".h", ".m", ".json"])
    for path in files:
        CSGXCTools.replace_code_in_file(path, new_temp_dic)
        
     


 
# pro_path 工程主目录，   restore_config_path 还原配置文件路径，  is_restore 是否是还原模式
def modify_bundle_files(sdk_name, restore_config_path, is_restore, new_obfuse_or_not):
    pro_path = '../Mock/%s' % sdk_name
    images = []
    sdk_code_path = pro_path + '/' + sdk_name + 'SDK'
    bundle_path = pro_path + '/' + sdk_name + 'SDKBundle'
    CSGFile.files_in_dir(images, bundle_path, [".png"])
    img_dic = {}
    
    if is_restore and new_obfuse_or_not == '0':
        bundle_config_data = util_file.read_file(restore_config_path)
        img_dic = json.loads(bundle_config_data)
        new_img_dic = {}
        for item in img_dic.keys():
            if item.endswith('.png'):
               new_img_dic[item] = img_dic.get(item) 
        img_dic = new_img_dic
         

    for path in images:
        fileAppend(path)
        tmp_name = CSGFile.get_filename(path)
        if tmp_name in img_dic.keys():
            random_str = img_dic[tmp_name]
        else:
            random_str = getRandomImageNameFromLexicon()

        if not random_str.endswith('.png'):
            if '@2x.png' in tmp_name:
                random_str = random_str + "@2x.png"
            elif '@3x.png' in tmp_name:
                random_str = random_str + "@3x.png"
            else:
                random_str = random_str + ".png"
                

        CSGFile.file_rename(path, random_str)
        img_dic[tmp_name] = random_str

    files = []
    CSGFile.files_in_dir(files, sdk_code_path, [".h", ".m"])
    
    for path in files:
        CSGXCTools.replace_code_in_file(path, img_dic)
    
    
    if is_restore and new_obfuse_or_not == '0':
        print("资源还原完成")
        return
    
    
    append_dic = {}
    append_dic.update(img_dic)
    
    
    # 写入图片配置json表到目标目录
    util_file.write_file(sdk_code_path + "/__image_config.json",
                             json.dumps(append_dic, indent=4, ensure_ascii=False))


# def read_plist_file(file_path):
#     with open(file_path, 'rb') as fp:
#         plist_data = plistlib.load(fp)
#     return plist_data

# def write_plist_file(file_path, data):
#     with open(file_path, 'wb') as fp:
#         plistlib.dump(data, fp)


# if __name__ == '__main__':
    # modify_bundle_files("./Images", "__image_config.json", 1, "")
    # modifyJsonConfigName("../JeniceGame", "../simplified.json", 1)

import os
def files_in_dir(result, dir_path, exts):
	for maindir, subdir, file_name_list in os.walk(dir_path):
		if 'BaseComponent' in maindir or 'Demo' in maindir:
			continue
		for filename in file_name_list:
			apath = os.path.join(maindir, filename)
			ext = get_file_ext(apath)
			if ext in exts:
				if apath not in result:
					result.append(apath)

		for dir_name in subdir:
			tmp_path = maindir+"\\"+dir_name
			files_in_dir(result, tmp_path, exts)
   
   
def get_files_in_dir(result, dir_path):
	for maindir, subdir, file_name_list in os.walk(dir_path):
		for filename in file_name_list:
			apath = os.path.join(maindir, filename)
			if apath not in result:
				result.append(apath)
    
		for dir_name in subdir:
			tmp_path = maindir+"\\"+dir_name
			get_files_in_dir(result, tmp_path)

def is_file(path):
	return os.path.isfile(path)

def is_dir(path):
	return os.path.isdir(path)

def get_file_ext(path):
	return os.path.splitext(path)[1]

def get_file_dir(path):
	return os.path.dirname(path)

def get_filename(path):
	return os.path.split(path)[-1]

def file_rename(s_path, new_name):
	t_dir = get_file_dir(s_path)
	os.rename(s_path, t_dir + "//"+new_name)


def remove_file(path):
	os.remove(path)

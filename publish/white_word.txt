deinitialize
queue
scene
username
interaction
callBackBlock
setCallBackBlock
completionBlock
loadProductWithParameters
scrollView
Firebase
complatedBlock
itemClick
imageBlock
setImageBlock
oauth20ConnectionDidFinishDeleteToken
oauth20Connection
oauth20ConnectionDidFinishRequestACTokenWithAuthCode
oauth20ConnectionDidFinishRequestACTokenWithRefreshToken
status
rewardVideoAdDidLoad
rewardVideoAdDidVisible
rewardVideoAdDidClick
rewardVideoAdDidClickSkip
rewardVideoAdDidClose
rewardVideoAd
rewardVideoAdDidPlayFinish
inPurchaseAndVerify
userDidLanch
priority
mfsBlock
urlString
mfsTermsPage
mfsArray
mfsOrder
hideHUDForView
didPayRevenueForAd
messaging
NSLog
MFSDarkGrayColor
MFSBundleImage
Macro
NSNumber
numberWithInt
numberWithInteger
numberWithFloat
NS_ASSUME_NONNULL_BEGIN
NS_ASSUME_NONNULL_END
dictionaryForKey
loadRequest
boolForKey
errorWithDomain
customRect
currentCalendar
authorizationController
didCompleteWithAuthorization
didCompleteWithError
presentationAnchorForAuthorizationController
bootTimeInSec
countryCode
language
memory
deviceName
carrierInfo
idfv
sysFileTime
disk
carrierInfo
cachedTarget
block
checkArea
title
quickMFSLogin
machine
image
label
goodResponse
model
selectedHandler
AppDelegate
window
dataDict
textColor
userInfo
delegate
style
supportedInterfaceOrientations
MARewardedAdDelegate
MAAdDelegate
didLoadAd
didFailToLoadAdForAdUnitIdentifier
didDisplayAd
didClickAd
didHideAd
didFailToDisplayAd
didRewardUserForAd
viewDidLayoutSubviews
GADFullScreenContentDelegate
 reportMFSAd
MFSCommentModel
mfsId
SceneDelegate
presentFromRootViewController
BaseView
GADFullScreenContentDelegate
adDidRecordImpression
adDidRecordClick
adWillPresentFullScreenContent
adWillDismissFullScreenContent
adDidDismissFullScreenContent
adDidPresentFullScreenContent
updateConstraints
picker
imagePickerController
imagePickerControllerDidCancel
observeValueForKeyPath
preferredScreenEdgesDeferringSystemGestures
supportsSecureCoding
targetView
textView
shouldInteractWithURL
placeHolderText
actionWithTitle
evaluatorObject
drawRect
initOptionView
writeToFile
mutableCopyWithZone
initTitleInputView
currentDevice
selected
amount
initDelegate
initIncrementalWithOptions
load
addObserver
dictionary
titleLabel
items
comment
objectForInfoDictionaryKey
openURL
resume
initMacrobe
canOpenURL
traitCollectionDidChange
systemVersion
state
animation
view
respondsToSelector
awakeFromNib
identifier
cornerRadius
removeFromSuperview
hitTest
removeFromSuperview
hitTesttle
presentViewController
dismissViewControllerAnimated
defaultManager
defaultManager
userNotificationCenter
amount
productViewControllerDidFinish
stretchedObject
allocWithZone
loadData
canPerformAction
systemName
timeZone
UUIDString
buttonWithType
collectionView
textFieldDidBeginEditing
textFieldShouldReturn
textFieldDidChangeSelection
numberOfItemsInSection
cellForItemAtIndexPath
withEvent
touchesEnded
didSelectItemAtIndexPath
coverImageView
error
bytes
mutableCopy
isEqual
cancel
compare
hash
array
isDirectory
lastObject
userContentController
copyWithZone
errorInvalidDataWithMessage
encodeWithCoder
textRectForBounds
editingRectForBounds
dealloc
color
description
string
under
query
request
action
viewDidAppear
scrollViewDidEndDecelerating
set
layoutSubviews
didReceiveMemoryWarning
token
webView
Method
contentView
productsRequest
didReceiveResponse
paymentQueue
updatedTransactions
connection
connectionDidFinishLoading
didReceiveData
didReceiveResponse
didFailWithError
extInfo
numberOfSectionsInTableView
numberOfRowsInSection
cellForRowAtIndexPath
didSelectRowAtIndexPath
heightForRowAtIndexPath
tableView
btnShowLoginClick
iapClick
aaaClick
testLoginCallback
imageView
Point
origin
durationWithSourceAtIndex
rootViewController
size
width
height
application
didFinishLaunchingWithOptions
applicationWillResignActive
applicationDidEnterBackground
applicationWillEnterForeground
supportedInterfaceOrientationsForWindow
applicationWillTerminate
applicationDidBecomeActive
touchesBegan
touchesMoved
viewDidLoad
viewWillAppear
end
viewWillDisappear
initWithFrame
SKProduct
resignFirstResponder
reloadInputViews
logout
shouldAutorotate
prefersStatusBarHidden
CGImage
objectAtIndex
resignFirstResponder
firstObject
reloadInputViews
manager
btnBgDefaultt
btnBgLong
CSGLogFramework
initConfig
CSGLogEngine
CSGLogEngineConfig
disnableLog
addLog
password
background
activeGame
SKProduct
resignFirstResponder
reloadInputViews
logout
serverID
CSGPopView
amount
images
CGImage
objectAtIndex
resignFirstResponder
firstObject
reloadInputViews
manager
ksd
CSHttpUtils
shared
start
stringForKey
defaultService
allKeys
synchronize
GCLangUtil
GCBase64Util
dataForKey
removeItemForKey
removeAllItems
objectForKeyedSubscript
allItems
disnableLog
addLog
password
background
sharedInstance
config
showLogin
logout
serverID
payWithProduct
amount
isDebug
activeGame
SKProduct
resignFirstResponder
reloadInputViews
activeNativeData
initialize
images
CGImage
objectAtIndex
resignFirstResponder
firstObject
reloadInputViews
manager
ksd
CSHttpUtils
CSGMobileGameSDK
shared
getIAPWithProduct
activeCurrentDevice
CSIAPCallBackInfo
invoke
setDictionary
setCustomRect
setBlock
setSelected
setTitleLabel
setImageView
setTextView
setCheckArea
setBorderMFSWhich
setHighlighted
setTimeoutInterval
setProgress
setTitleColor
setResponseSerializer
setPlaceholder
setCollectionView
setDx
setGearModel
setCurrentUIType
setExtInfo
setImage
setImage
setObject
setString
setData
didFailToLoadADWithPlacementID
didFinishLoadingADWithPlacementID
rewardedVideoDidClickForPlacementID
rewardedVideoDidCloseForPlacementID
rewardedVideoDidEndPlayingForPlacementID
rewardedVideoDidRewardSuccessForPlacemenID
rewardedVideoDidStartPlayingForPlacementID
rewardedVideoDidFailToPlayForPlacementID
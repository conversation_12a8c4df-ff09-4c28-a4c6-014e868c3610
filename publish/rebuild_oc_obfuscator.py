# -*- coding: utf8 -*-
from string import ascii_letters
import gtools.obfus.obfuscator as obfuscator
import gtools.obfus.obj_c as obj_c
import gtools.obfus.swift as swift

import gtools.obfus.obj_c_util as obj_c_util
import random
import re
import string
from pbxproj import *
import CSGString


class ObjCTemplateManager:
    @staticmethod
    def template_original():
        """原有的模板保持不变"""
        fun_text = ''
        
        # 添加基础变量声明
        fun_text += "\t// Initialize random values\n"
        fun_text += "\tNSInteger iterations = arc4random_uniform(100) + 50;\n"
        fun_text += "\tdouble baseValue = (double)(arc4random_uniform(1000)) / 100.0;\n"
        fun_text += "\tNSMutableArray *computedResults = [NSMutableArray array];\n"
        fun_text += "\tNSMutableDictionary *cache = [NSMutableDictionary dictionary];\n\n"
        
        # 添加数学计算
        fun_text += "\t// Perform mathematical calculations\n"
        fun_text += "\tdouble accumulator = baseValue;\n"
        fun_text += "\tfor (NSInteger i = 0; i < iterations; i++) {\n"
        fun_text += "\t\t// Generate complex mathematical expressions\n"
        fun_text += "\t\tdouble factor = sin(accumulator) * cos(baseValue);\n"
        fun_text += "\t\taccumulator = sqrt(pow(accumulator, 2) + pow(factor, 2));\n"
        fun_text += "\t\t[computedResults addObject:@(accumulator)];\n"
        fun_text += "\t\t[cache setObject:@(factor) forKey:@(i)];\n"
        fun_text += "\t}\n\n"
        
        # 添加数组操作和排序
        fun_text += "\t// Array manipulation and sorting\n"
        fun_text += "\tNSArray *sortedResults = [computedResults sortedArrayUsingComparator:^NSComparisonResult(NSNumber *obj1, NSNumber *obj2) {\n"
        fun_text += "\t\treturn [obj1 compare:obj2];\n"
        fun_text += "\t}];\n\n"
        
        # 添加字符串处理
        fun_text += "\t// String processing\n"
        fun_text += "\tNSMutableString *resultString = [NSMutableString string];\n"
        fun_text += "\tfor (NSNumber *num in sortedResults) {\n"
        fun_text += "\t\t[resultString appendFormat:@\"%.4f,\", num.doubleValue];\n"
        fun_text += "\t}\n\n"
        
        # 添加哈希计算
        fun_text += "\t// Hash computation\n"
        fun_text += "\tunsigned long hash = 5381;\n"
        fun_text += "\tfor (NSUInteger i = 0; i < resultString.length; i++) {\n"
        fun_text += "\t\thash = ((hash << 5) + hash) + [resultString characterAtIndex:i];\n"
        fun_text += "\t}\n\n"
        
        # 添加数据处理和过滤
        fun_text += "\t// Data processing and filtering\n"
        fun_text += "\tNSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL(NSNumber *evaluatedObject, NSDictionary *bindings) {\n"
        fun_text += "\t\treturn [evaluatedObject doubleValue] > baseValue;\n"
        fun_text += "\t}];\n"
        fun_text += "\tNSArray *filteredResults = [sortedResults filteredArrayUsingPredicate:predicate];\n\n"
        
        # 添加统计计算
        fun_text += "\t// Statistical calculations\n"
        fun_text += "\tdouble sum = 0.0, mean = 0.0, variance = 0.0;\n"
        fun_text += "\tfor (NSNumber *num in filteredResults) {\n"
        fun_text += "\t\tsum += [num doubleValue];\n"
        fun_text += "\t}\n"
        fun_text += "\tmean = sum / (filteredResults.count ?: 1);\n"
        fun_text += "\tfor (NSNumber *num in filteredResults) {\n"
        fun_text += "\t\tvariance += pow([num doubleValue] - mean, 2);\n"
        fun_text += "\t}\n"
        fun_text += "\tvariance /= (filteredResults.count ?: 1);\n\n"
        
        # 添加结果缓存
        fun_text += "\t// Cache final results\n"
        fun_text += "\t[cache setObject:@(sum) forKey:@\"sum\"];\n"
        fun_text += "\t[cache setObject:@(mean) forKey:@\"mean\"];\n"
        fun_text += "\t[cache setObject:@(variance) forKey:@\"variance\"];\n"
        fun_text += "\t[cache setObject:@(hash) forKey:@\"hash\"];\n"
        fun_text += "\t[cache setObject:resultString forKey:@\"string\"];\n\n"
        
        # 添加错误处理
        fun_text += "\t// Error handling and validation\n"
        fun_text += "\t@try {\n"
        fun_text += "\t\tif (variance < 0 || isnan(variance)) {\n"
        fun_text += "\t\t\t@throw [NSException exceptionWithName:@\"Error\"\n"
        fun_text += "\t\t\t\t\t\t\t\treason:@\"Invalid varutation\"\n"
        fun_text += "\t\t\t\t\t\tuserInfo:nil];\n"
        fun_text += "\t\t}\n"
        fun_text += "\t} @catch (NSException *exception) {\n"
        fun_text += "\t\tNSLog(@\"error: %@\", exception);\n"
        fun_text += "\t}\n"
        
        return (fun_text, 'void')

    @staticmethod
    def template_matrix_operations():
        """矩阵运算模板"""
        fun_text = ''
        
        # 初始化矩阵数据
        fun_text += "\t// Initialize matrix data\n"
        fun_text += "\tNSInteger matrixSize = arc4random_uniform(5) + 3;\n"
        fun_text += "\tNSMutableArray *matrixA = [NSMutableArray array];\n"
        fun_text += "\tNSMutableArray *matrixB = [NSMutableArray array];\n"
        fun_text += "\tNSMutableArray *resultMatrix = [NSMutableArray array];\n\n"
        
        # 生成矩阵数据
        fun_text += "\t// Generate matrix data\n"
        fun_text += "\tfor (NSInteger i = 0; i < matrixSize; i++) {\n"
        fun_text += "\t\tNSMutableArray *rowA = [NSMutableArray array];\n"
        fun_text += "\t\tNSMutableArray *rowB = [NSMutableArray array];\n"
        fun_text += "\t\tfor (NSInteger j = 0; j < matrixSize; j++) {\n"
        fun_text += "\t\t\t[rowA addObject:@(arc4random_uniform(10))];\n"
        fun_text += "\t\t\t[rowB addObject:@(arc4random_uniform(10))];\n"
        fun_text += "\t\t}\n"
        fun_text += "\t\t[matrixA addObject:rowA];\n"
        fun_text += "\t\t[matrixB addObject:rowB];\n"
        fun_text += "\t}\n\n"
        
        # 矩阵乘法
        fun_text += "\t// Perform matrix multiplication\n"
        fun_text += "\tfor (NSInteger i = 0; i < matrixSize; i++) {\n"
        fun_text += "\t\tNSMutableArray *resultRow = [NSMutableArray array];\n"
        fun_text += "\t\tfor (NSInteger j = 0; j < matrixSize; j++) {\n"
        fun_text += "\t\t\tdouble sum = 0;\n"
        fun_text += "\t\t\tfor (NSInteger k = 0; k < matrixSize; k++) {\n"
        fun_text += "\t\t\t\tsum += [matrixA[i][k] doubleValue] * [matrixB[k][j] doubleValue];\n"
        fun_text += "\t\t\t}\n"
        fun_text += "\t\t\t[resultRow addObject:@(sum)];\n"
        fun_text += "\t\t}\n"
        fun_text += "\t\t[resultMatrix addObject:resultRow];\n"
        fun_text += "\t}\n\n"
        
        # 计算行列式
        fun_text += "\t// Calculate determinant\n"
        fun_text += "\tdouble determinant = 1.0;\n"
        fun_text += "\tfor (NSInteger i = 0; i < matrixSize; i++) {\n"
        fun_text += "\t\tdeterminant *= [resultMatrix[i][i] doubleValue];\n"
        fun_text += "\t}\n\n"
        
        # 矩阵转置
        fun_text += "\t// Transpose result matrix\n"
        fun_text += "\tNSMutableArray *transposedMatrix = [NSMutableArray array];\n"
        fun_text += "\tfor (NSInteger i = 0; i < matrixSize; i++) {\n"
        fun_text += "\t\tNSMutableArray *transposedRow = [NSMutableArray array];\n"
        fun_text += "\t\tfor (NSInteger j = 0; j < matrixSize; j++) {\n"
        fun_text += "\t\t\t[transposedRow addObject:resultMatrix[j][i]];\n"
        fun_text += "\t\t}\n"
        fun_text += "\t\t[transposedMatrix addObject:transposedRow];\n"
        fun_text += "\t}\n\n"
        
        # 错误处理
        fun_text += "\t// Validate results\n"
        fun_text += "\t@try {\n"
        fun_text += "\t\tif (determinant == 0 || isnan(determinant)) {\n"
        fun_text += "\t\t\t@throw [NSException exceptionWithName:@\"Error\"\n"
        fun_text += "\t\t\t\t\t\t\t\treason:@\"Invalid reason\"\n"
        fun_text += "\t\t\t\t\t\tuserInfo:nil];\n"
        fun_text += "\t\t}\n"
        fun_text += "\t} @catch (NSException *exception) {\n"
        fun_text += "\t\tNSLog(@\"error desc: %@\", exception);\n"
        fun_text += "\t}\n"
        
        return (fun_text, 'void')

    @staticmethod
    def template_crypto_operations():
        """加密操作模板"""
        fun_text = ''
        
        # 初始化数据
        fun_text += "\t// Initialize crypto data\n"
        fun_text += "\tNSString *inputString = @\"\";\n"
        fun_text += "\tfor (NSInteger i = 0; i < 32; i++) {\n"
        fun_text += "\t\tchar randomChar = 'A' + arc4random_uniform(26);\n"
        fun_text += "\t\tinputString = [inputString stringByAppendingFormat:@\"%c\", randomChar];\n"
        fun_text += "\t}\n\n"
        
        # 使用 NSData 的内置方法计算哈希
        fun_text += "\t// Calculate hash\n"
        fun_text += "\tNSData *inputData = [inputString dataUsingEncoding:NSUTF8StringEncoding];\n"
        fun_text += "\tNSMutableString *hashString = [NSMutableString string];\n"
        fun_text += "\tunsigned char *bytes = (unsigned char *)[inputData bytes];\n"
        fun_text += "\tNSInteger length = [inputData length];\n"
        fun_text += "\tfor(NSInteger i = 0; i < length; i++) {\n"
        fun_text += "\t\t[hashString appendFormat:@\"%02x\", bytes[i]];\n"
        fun_text += "\t}\n\n"
        
        # Base64 编码解码
        fun_text += "\t// Perform Base64 encoding/decoding\n"
        fun_text += "\tNSString *base64Encoded = [inputData base64EncodedStringWithOptions:0];\n"
        fun_text += "\tNSData *base64Decoded = [[NSData alloc] initWithBase64EncodedString:base64Encoded options:0];\n\n"
        
        # 自定义加密
        fun_text += "\t// Custom encryption\n"
        fun_text += "\tNSMutableData *encryptedData = [NSMutableData dataWithLength:inputData.length];\n"
        fun_text += "\tuint8_t *inputBytes = (uint8_t *)inputData.bytes;\n"
        fun_text += "\tuint8_t *outputBytes = (uint8_t *)encryptedData.mutableBytes;\n"
        fun_text += "\tuint8_t key = arc4random_uniform(256);\n"
        fun_text += "\tfor (NSInteger i = 0; i < inputData.length; i++) {\n"
        fun_text += "\t\toutputBytes[i] = inputBytes[i] ^ key;\n"
        fun_text += "\t}\n\n"
        
        # 计算校验和
        fun_text += "\t// Calculate checksum\n"
        fun_text += "\tuint32_t checksum = 0;\n"
        fun_text += "\tfor (NSInteger i = 0; i < encryptedData.length; i++) {\n"
        fun_text += "\t\tchecksum = ((checksum << 5) + checksum) + outputBytes[i];\n"
        fun_text += "\t}\n\n"
        
        # 错误处理
        fun_text += "\t// Validate results\n"
        fun_text += "\t@try {\n"
        fun_text += "\t\tif (base64Decoded.length != inputData.length) {\n"
        fun_text += "\t\t\t@throw [NSException exceptionWithName:@\"Error\"\n"
        fun_text += "\t\t\t\t\t\t\t\treason:@\"Invalid operation code\"\n"
        fun_text += "\t\t\t\t\t\tuserInfo:nil];\n"
        fun_text += "\t\t}\n"
        fun_text += "\t} @catch (NSException *exception) {\n"
        fun_text += "\t\tNSLog(@\"error: %@\", exception);\n"
        fun_text += "\t}\n"
        
        return (fun_text, 'void')

    @staticmethod
    def get_random_template():
        """随机选择一个模板"""
        templates = [
            ObjCTemplateManager.template_original,
            ObjCTemplateManager.template_matrix_operations,
            ObjCTemplateManager.template_crypto_operations
        ]
        return random.choice(templates)()



# swift混淆
class SDK_MOD_SW(swift.Swift):
    def encrypt_str(self, str, type):
        return SDK_MOD_OC.encrypt_str(SDK_MOD_OC, str, type)
    

    def gen_fun(self, clz_name, fun_name=None, fun_type=None, mark=None, rand_rang=..., letters=string.ascii_letters, fun_temp=None):
        return SDK_MOD_OC.gen_fun(SDK_MOD_OC, clz_name, fun_name, fun_type, mark, rand_rang, letters, fun_temp)





class SDK_MOD_OC(obj_c.Obj_C):

    common_suffixes = [
        "ViewController",  # 用于视图控制器
        "View",            # 用于视图
        "Manager",         # 用于管理类
        "Service",         # 用于服务类
        "Helper",          # 用于辅助类
        "Delegate",        # 用于委托类
        "DataSource",      # 用于数据源类
        "Model",           # 用于数据模型
        "ViewModel",       # 用于视图模型（MVVM 模式）
        "Presenter",       # 用于表示层（MVP 模式）
        "Router",          # 用于路由（VIPER 模式）
        "Interactor",      # 用于交互层（VIPER 模式）
        "Entity",          # 用于实体类
        "Protocol",        # 用于协议
        "Factory",         # 用于工厂类
        "Builder",         # 用于构建器类
        "Adapter",         # 用于适配器类
        "Coordinator",     # 用于协调器
        "Utility",         # 用于工具类
        "Handler",         # 用于处理器类
        "Provider",        # 用于提供者类
        "Repository",      # 用于数据仓库
        "Store",           # 用于存储类
        "Cache",           # 用于缓存类
        "Network",         # 用于网络相关类
        "API",             # 用于 API 相关类
        "Request",         # 用于请求类
        "Response",        # 用于响应类
        "Parser",          # 用于解析器
        "Validator",       # 用于验证器
        "Animator",        # 用于动画类
        "Transition",      # 用于转场动画类
        "Layout",          # 用于布局类
        "Style",           # 用于样式类
        "Theme",           # 用于主题类
        "Configuration",   # 用于配置类
        "Settings",        # 用于设置类
        "Notification",    # 用于通知类
        "Observer",        # 用于观察者
        "Command",         # 用于命令模式
        "Strategy",        # 用于策略模式
        "State",           # 用于状态类
        "Component",       # 用于组件类
        "Plugin",          # 用于插件类
        "Extension",       # 用于扩展类
        "Wrapper",         # 用于包装器类
        "Proxy",           # 用于代理类
        "Middleware",      # 用于中间件
        "Logger",          # 用于日志类
        "Analytics",       # 用于分析类
        "Tracker",         # 用于追踪类
        "Scheduler",       # 用于调度器
        "Queue",           # 用于队列类
        "Stack",           # 用于堆栈类
        "Graph",           # 用于图类
        "Tree",            # 用于树类
        "List",            # 用于列表类
        "Set",             # 用于集合类
        "Map",             # 用于映射类
        "Dictionary",      # 用于字典类
        "Array",           # 用于数组类
        "Matrix",          # 用于矩阵类
        "Vector",          # 用于向量类
        "Point",           # 用于点类
        "Rect",            # 用于矩形类
        "Circle",          # 用于圆类
        "Polygon",         # 用于多边形类
        "Path",            # 用于路径类
        "Shape",           # 用于形状类
        "Color",           # 用于颜色类
        "Gradient",        # 用于渐变类
        "Texture",         # 用于纹理类
        "Material",        # 用于材质类
        "Light",           # 用于光源类
        "Camera",          # 用于相机类
        "Scene",           # 用于场景类
        "Node",            # 用于节点类
        "Layer",           # 用于图层类
        "Renderer",        # 用于渲染器
        "Shader",          # 用于着色器
        "Effect",          # 用于效果类
        "Filter",          # 用于滤镜类
        "Blend",           # 用于混合类
        "Transform",       # 用于变换类
        "Projection",      # 用于投影类
        "Clip",            # 用于裁剪类
        "Mask",            # 用于遮罩类
        "Composite",       # 用于复合类
        "Iterator",        # 用于迭代器
        "Generator",       # 用于生成器
        "Stream",          # 用于流类
        "Buffer",          # 用于缓冲类
        "Pool",            # 用于池类
        "Allocator",       # 用于分配器
        "Memory",          # 用于内存管理类
        "File",            # 用于文件类
        "Directory",       # 用于目录类
        "Archive",         # 用于归档类
        "Compressor",      # 用于压缩器
        "Decompressor",    # 用于解压缩器
        "Encryptor",       # 用于加密器
        "Decryptor",       # 用于解密器
        "Hasher",          # 用于哈希器
        "Serializer",      # 用于序列化器
        "Deserializer",    # 用于反序列化器
        "Converter",       # 用于转换器
        "Formatter",       # 用于格式化器
        "Printer",         # 用于打印类
        "Scanner",         # 用于扫描类
        "Reader",          # 用于读取类
        "Writer",          # 用于写入类
        "Editor",          # 用于编辑类
        "Designer",        # 用于设计类
        "Debugger",        # 用于调试类
        "Profiler",        # 用于性能分析类
        "Emulator",        # 用于仿真器
        "Virtualizer",     # 用于虚拟化类
        "Container",       # 用于容器类
        "Wrapper",         # 用于包装类
        "Facade",          # 用于外观类
        "Composite",       # 用于组合类
        "Decorator",       # 用于装饰器
        "Flyweight",       # 用于享元类
        "Proxy",           # 用于代理类
        "Chain",           # 用于责任链
        "Command",         # 用于命令类
        "Interpreter",     # 用于解释器
        "Iterator",        # 用于迭代器
        "Mediator",        # 用于中介者
        "Memento",         # 用于备忘录
        "Observer",        # 用于观察者
        "State",           # 用于状态类
        "Strategy",        # 用于策略类
        "Template",        # 用于模板类
        "Visitor",         # 用于访问者
        "Singleton",       # 用于单例类
        "Multiton",        # 用于多例类
        "Object",          # 用于对象类
        "Class",           # 用于类
        "Instance",        # 用于实例
        "Type",            # 用于类型
        "Kind",            # 用于种类
        "Category",        # 用于分类
        "Group",           # 用于组
        "Collection",      # 用于集合
        "Set",             # 用于集合
        "List",            # 用于列表
        "Queue",           # 用于队列
        "Stack",           # 用于堆栈
        "Map",             # 用于映射
        "Dictionary",      # 用于字典
        "Table",           # 用于表
        "Matrix",          # 用于矩阵
        "Graph",           # 用于图
        "Tree",            # 用于树
        "Node"]


    common_property_suffixes = [
        "View",           # 用于视图
        "Label",          # 用于标签
        "Button",         # 用于按钮
        "TextField",      # 用于文本输入框
        "TextView",       # 用于文本视图
        "ImageView",      # 用于图片视图
        "ScrollView",     # 用于滚动视图
        "TableView",      # 用于表格视图
        "CollectionView", # 用于集合视图
        "Cell",           # 用于单元格
        "Controller",     # 用于控制器
        "Delegate",       # 用于委托
        "DataSource",     # 用于数据源
        "Model",          # 用于数据模型
        "ViewModel",      # 用于视图模型
        "Manager",        # 用于管理类
        "Service",        # 用于服务类
        "Helper",         # 用于辅助类
        "Provider",       # 用于提供者类
        "Repository",     # 用于数据仓库
        "Store",          # 用于存储类
        "Cache",          # 用于缓存类
        "Network",        # 用于网络相关类
        "API",            # 用于 API 相关类
        "Request",        # 用于请求类
        "Response",       # 用于响应类
        "Parser",         # 用于解析器
        "Validator",      # 用于验证器
        "Animator",       # 用于动画类
        "Transition",     # 用于转场动画类
        "Layout",         # 用于布局类
        "Style",          # 用于样式类
        "Theme",          # 用于主题类
        "Configuration",  # 用于配置类
        "Settings",       # 用于设置类
        "Notification",   # 用于通知类
        "Observer",       # 用于观察者
        "Command",        # 用于命令模式
        "Strategy",       # 用于策略模式
        "State",          # 用于状态类
        "Component",      # 用于组件类
        "Plugin",         # 用于插件类
        "Extension",      # 用于扩展类
        "Wrapper",        # 用于包装器类
        "Proxy",          # 用于代理类
        "Middleware",     # 用于中间件
        "Logger",         # 用于日志类
        "Analytics",      # 用于分析类
        "Tracker",        # 用于追踪类
        "Scheduler",      # 用于调度器
        "Queue",          # 用于队列类
        "Stack",          # 用于堆栈类
        "Graph",          # 用于图类
        "Tree",           # 用于树类
        "List",           # 用于列表类
        "Set",            # 用于集合类
        "Map",            # 用于映射类
        "Dictionary",     # 用于字典类
        "Array",          # 用于数组类
        "Matrix",         # 用于矩阵类
        "Vector",         # 用于向量类
        "Point",          # 用于点类
        "Rect",           # 用于矩形类
        "Circle",         # 用于圆类
        "Polygon",        # 用于多边形类
        "Path",           # 用于路径类
        "Shape",          # 用于形状类
        "Color",          # 用于颜色类
        "Gradient",       # 用于渐变类
        "Texture",        # 用于纹理类
        "Material",       # 用于材质类
        "Light",          # 用于光源类
        "Camera",         # 用于相机类
        "Scene",          # 用于场景类
        "Node",           # 用于节点类
        "Layer",          # 用于图层类
        "Renderer",       # 用于渲染器
        "Shader",         # 用于着色器
        "Effect",         # 用于效果类
        "Filter",         # 用于滤镜类
        "Blend",          # 用于混合类
        "Transform",      # 用于变换类
        "Projection",     # 用于投影类
        "Clip",           # 用于裁剪类
        "Mask",           # 用于遮罩类
        "Composite",      # 用于复合类
        "Iterator",       # 用于迭代器
        "Generator",      # 用于生成器
        "Stream",         # 用于流类
        "Buffer",         # 用于缓冲类
        "Pool",           # 用于池类
        "Allocator",      # 用于分配器
        "Memory",         # 用于内存管理类
        "File",           # 用于文件类
        "Directory",      # 用于目录类
        "Archive",        # 用于归档类
        "Compressor",     # 用于压缩器
        "Decompressor",   # 用于解压缩器
        "Encryptor",      # 用于加密器
        "Decryptor",      # 用于解密器
        "Hasher",         # 用于哈希器
        "Serializer",     # 用于序列化器
        "Deserializer",   # 用于反序列化器
        "Converter",      # 用于转换器
        "Formatter",      # 用于格式化器
        "Printer",        # 用于打印类
        "Scanner",        # 用于扫描类
        "Reader",         # 用于读取类
        "Writer",         # 用于写入类
        "Editor",         # 用于编辑类
        "Designer",       # 用于设计类
        "Debugger",       # 用于调试类
        "Profiler",       # 用于性能分析类
        "Emulator",       # 用于仿真器
        "Virtualizer",    # 用于虚拟化类
        "Container",      # 用于容器类
        "Wrapper",        # 用于包装类
        "Facade",         # 用于外观类
        "Composite",      # 用于组合类
        "Decorator",      # 用于装饰器
        "Flyweight",      # 用于享元类
        "Proxy",          # 用于代理类
        "Chain",          # 用于责任链
        "Command",        # 用于命令类
        "Interpreter",    # 用于解释器
        "Iterator",       # 用于迭代器
        "Mediator",       # 用于中介者
        "Memento",        # 用于备忘录
        "Observer",       # 用于观察者
        "State",          # 用于状态类
        "Strategy",       # 用于策略类
        "Template",       # 用于模板类
        "Visitor",        # 用于访问者
        "Singleton",      # 用于单例类
        "Multiton",       # 用于多例类
        "Object",         # 用于对象类
        "Class",          # 用于类
        "Instance",       # 用于实例
        "Type",           # 用于类型
        "Kind",           # 用于种类
        "Category",       # 用于分类
        "Group",          # 用于组
        "Collection",     # 用于集合
        "Set",            # 用于集合
        "List",           # 用于列表
        "Queue",          # 用于队列
        "Stack",          # 用于堆栈
        "Map",            # 用于映射
        "Dictionary",     # 用于字典
        "Table",          # 用于表
        "Matrix",         # 用于矩阵
        "Graph",          # 用于图
        "Tree"]           # 用于树
    
    common_macro_prefixes = [
        "PROJECT_",  # 项目相关前缀（如 PROJECT_NAME）
        "APP_",      # 应用相关前缀（如 APP_VERSION）
        "LOG_",      # 日志相关前缀（如 LOG_INFO）
        "CONFIG_",   # 配置相关前缀（如 CONFIG_MAX_COUNT）
        "UTIL_",     # 工具类前缀（如 UTIL_SAFE_RELEASE）
        "UI_",       # UI 相关前缀（如 UI_SCREEN_WIDTH）
        "NET_",      # 网络相关前缀（如 NET_TIMEOUT）
        "DB_",       # 数据库相关前缀（如 DB_TABLE_NAME）
        "BOOL_",     # 布尔类型前缀（如 BOOL_IS_ENABLED）
        "INT_",      # 整数类型前缀（如 INT_MAX_VALUE）
        "STR_",      # 字符串类型前缀（如 STR_WELCOME_MESSAGE）
        "OS_",       # 操作系统相关前缀（如 OS_VERSION）
        "ARC_",      # 自动引用计数相关前缀（如 ARC_ENABLED）
        "FOUNDATION_EXPORT",  # 用于定义全局常量
        "TYPE_",     # 类型相关前缀（如 TYPE_INT）
        "FLAG_",     # 标志位相关前缀（如 FLAG_ENABLED）
        "ERROR_",    # 错误相关前缀（如 ERROR_CODE）
        "STATUS_",   # 状态相关前缀（如 STATUS_OK）
        "MODE_",     # 模式相关前缀（如 MODE_DEBUG）
        "LEVEL_",    # 等级相关前缀（如 LEVEL_INFO）
        "SIZE_",     # 大小相关前缀（如 SIZE_MAX）
        "TIME_",     # 时间相关前缀（如 TIME_INTERVAL）
        "COLOR_",    # 颜色相关前缀（如 COLOR_BACKGROUND）
        "FONT_",     # 字体相关前缀（如 FONT_TITLE）
        "IMAGE_",    # 图片相关前缀（如 IMAGE_LOGO）
        "PATH_",     # 路径相关前缀（如 PATH_DOCUMENTS）
        "URL_",      # URL 相关前缀（如 URL_BASE）
        "API_",      # API 相关前缀（如 API_ENDPOINT）
        "KEY_",      # 键相关前缀（如 KEY_USER_ID）
        "VALUE_",    # 值相关前缀（如 VALUE_DEFAULT）
        "FLAG_",     # 标志位相关前缀（如 FLAG_ENABLED）
        "MASK_",     # 掩码相关前缀（如 MASK_BITS）
        "OPTION_",   # 选项相关前缀（如 OPTION_ENABLED）
        "SETTING_",  # 设置相关前缀（如 SETTING_MAX_COUNT）
        "LIMIT_",    # 限制相关前缀（如 LIMIT_MAX）
        "THRESHOLD_",  # 阈值相关前缀（如 THRESHOLD_MIN）
        "RANGE_",    # 范围相关前缀（如 RANGE_VALUES）
        "COUNT_",    # 计数相关前缀（如 COUNT_ITEMS）
        "INDEX_",    # 索引相关前缀（如 INDEX_FIRST）
        "POSITION_", # 位置相关前缀（如 POSITION_CENTER）
        "DIRECTION_",  # 方向相关前缀（如 DIRECTION_UP）
        "ANGLE_",    # 角度相关前缀（如 ANGLE_90）
        "SCALE_",    # 缩放相关前缀（如 SCALE_FACTOR）
        "ROTATION_", # 旋转相关前缀（如 ROTATION_ANGLE）
        "TRANSLATION_",  # 平移相关前缀（如 TRANSLATION_X）
        "OPACITY_",  # 透明度相关前缀（如 OPACITY_HALF）
        "CURVE_",    # 曲线相关前缀（如 CURVE_EASE_IN）
        "DURATION_", # 持续时间相关前缀（如 DURATION_SHORT）
        "DELAY_",    # 延迟相关前缀（如 DELAY_SECONDS）
        "SPEED_",    # 速度相关前缀（如 SPEED_FAST）
        "ACCELERATION_",  # 加速度相关前缀（如 ACCELERATION_GRAVITY）
        "FORCE_",    # 力相关前缀（如 FORCE_GRAVITY）
        "MASS_",     # 质量相关前缀（如 MASS_DEFAULT）
        "WEIGHT_",   # 重量相关前缀（如 WEIGHT_LIGHT）
        "VOLUME_",   # 体积相关前缀（如 VOLUME_MAX）
        "PRESSURE_", # 压力相关前缀（如 PRESSURE_ATMOSPHERIC）
        "TEMPERATURE_",  # 温度相关前缀（如 TEMPERATURE_ROOM）
        "HUMIDITY_", # 湿度相关前缀（如 HUMIDITY_HIGH）
        "LIGHT_",    # 光线相关前缀（如 LIGHT_INTENSITY）
        "SOUND_",    # 声音相关前缀（如 SOUND_VOLUME）
        "COLOR_",    # 颜色相关前缀（如 COLOR_RED）
        "TEXTURE_",  # 纹理相关前缀（如 TEXTURE_WOOD）
        "MATERIAL_", # 材质相关前缀（如 MATERIAL_METAL）
        "SHADER_",   # 着色器相关前缀（如 SHADER_DEFAULT）
        "EFFECT_",   # 效果相关前缀（如 EFFECT_BLUR）
        "FILTER_",   # 滤镜相关前缀（如 FILTER_GRAYSCALE）
        "BLEND_",    # 混合相关前缀（如 BLEND_ADD）
        "TRANSFORM_",  # 变换相关前缀（如 TRANSFORM_ROTATE）
        "PROJECTION_",  # 投影相关前缀（如 PROJECTION_PERSPECTIVE）
        "CLIP_",     # 裁剪相关前缀（如 CLIP_RECT）
        "MASK_",     # 遮罩相关前缀（如 MASK_CIRCLE）
        "COMPOSITE_",  # 复合相关前缀（如 COMPOSITE_OVERLAY）
        "ITERATOR_", # 迭代器相关前缀（如 ITERATOR_NEXT）
        "GENERATOR_",  # 生成器相关前缀（如 GENERATOR_RANDOM）
        "STREAM_",   # 流相关前缀（如 STREAM_INPUT）
        "BUFFER_",   # 缓冲相关前缀（如 BUFFER_SIZE）
        "POOL_",     # 池相关前缀（如 POOL_OBJECTS）
        "ALLOCATOR_",  # 分配器相关前缀（如 ALLOCATOR_DEFAULT）
        "MEMORY_",   # 内存相关前缀（如 MEMORY_SIZE）
        "DIRECTORY_",  # 目录相关前缀（如 DIRECTORY_DOCUMENTS）
        "ARCHIVE_",  # 归档相关前缀（如 ARCHIVE_ZIP）
        "COMPRESSOR_",  # 压缩器相关前缀（如 COMPRESSOR_GZIP）
        "DECOMPRESSOR_",  # 解压缩器相关前缀（如 DECOMPRESSOR_GZIP）
        "ENCRYPTOR_",  # 加密器相关前缀（如 ENCRYPTOR_AES）
        "DECRYPTOR_",  # 解密器相关前缀（如 DECRYPTOR_AES）
        "HASHER_",   # 哈希器相关前缀（如 HASHER_MD5）
        "SERIALIZER_",  # 序列化器相关前缀（如 SERIALIZER_JSON）
        "DESERIALIZER_",  # 反序列化器相关前缀（如 DESERIALIZER_JSON）
        "CONVERTER_",  # 转换器相关前缀（如 CONVERTER_HEX）
        "FORMATTER_",  # 格式化器相关前缀（如 FORMATTER_DATE）
        "PRINTER_",  # 打印机相关前缀（如 PRINTER_DEFAULT）
        "SCANNER_",  # 扫描器相关前缀（如 SCANNER_BARCODE）
        "READER_",   # 读取器相关前缀（如 READER_FILE）
        "WRITER_",   # 写入器相关前缀（如 WRITER_FILE）
        "EDITOR_",   # 编辑器相关前缀（如 EDITOR_TEXT）
        "DESIGNER_", # 设计器相关前缀（如 DESIGNER_UI）
        "DEBUGGER_", # 调试器相关前缀（如 DEBUGGER_BREAKPOINT）
        "PROFILER_"]
    
    def encrypt_str(self, name, type):
        if type == "class":
            oc_class_name_ab = random.choice([0,1])
            class_str = ''
            if oc_class_name_ab:
                class_str = CSGString.getRandomStr().capitalize() + CSGString.getRandomStr().capitalize()
            else:
                class_str = CSGString.getRandomStr().capitalize() + random.choice(self.common_suffixes)
            return self.obfuse_class_prefix + class_str
            
        elif type == "fun":
            oc_func_name_ab = random.choice([0,1])
            fun_name_str = ''
            if oc_func_name_ab:
                verb = CSGString.getRandomVerb()
                middle = CSGString.getRandomStr().capitalize() + CSGString.getRandomStr().capitalize()
                fun_name_str = verb + middle
            else:
                verb = CSGString.getRandomVerb()
                middle = CSGString.getRandomStr().capitalize()
                fun_name_str = verb + middle
            return fun_name_str
                

        elif type == "attribute":
            oc_attr_name_ab = random.choice([0,1])
            attribute_str = ''
            if oc_attr_name_ab:
                attribute_str = CSGString.getRandomStr() + CSGString.getRandomStr().capitalize()
            else:
                attribute_str = CSGString.getRandomStr() + random.choice(self.common_property_suffixes)
            return attribute_str

        elif type == "const":
            return CSGString.getRandomStr().upper()
        elif type == "split_str":
            input_string = name
            characters = re.findall(r'"(.*?)"', input_string)
            if len(characters) > 0:
                # 使用更复杂的字符串拼接方式
                objc_array = [f'@"{char}"' for char in characters[0]]
                joined_string = ','.join(objc_array)
                
                # 根据数组长度生成正确数量的格式化占位符
                format_placeholders = '%@' * len(objc_array)
                format_string = f'[NSString stringWithFormat:@"{format_placeholders}",{joined_string}]'
                
                # 随机选择字符串组合方式
                string_formats = [
                    f'[@[{joined_string}] componentsJoinedByString:@""]',
                    f'[[NSArray arrayWithObjects:{joined_string},nil] componentsJoinedByString:@""]',
                    format_string
                ]
                return random.choice(string_formats)
            else:
                return name
        elif type == "define":
            return random.choice(self.common_macro_prefixes) + CSGString.getRandomStr().upper()
        else:
            return str


  

    def gen_fun(self, cls_name, fun_name = 3, fun_type = None, mark=None, rand_rang=[2, 4], letters=string.ascii_letters, fun_temp=None):
        
        def get_no_param_fun_name():
            
            oc_func_name_ab = random.choice([0,1])
            class_str = ""
            if oc_func_name_ab:
                first_word = CSGString.getRandomVerb()
                word_A = CSGString.getRandomStr().capitalize()
                word_B = CSGString.getRandomStr().capitalize()
                class_str = first_word + word_A + word_B
            else:    
                first_word = CSGString.getRandomVerb()
                random_str = CSGString.getRandomStr().capitalize()
                class_str = first_word + random_str
                
            if " " in class_str:
                class_str = class_str.replace(" ", "")
            
            first_word = CSGString.getRandomVerb()
            tem_fun_name = first_word + class_str.capitalize()
            return tem_fun_name
        
        def get_class_name():
            """生成类名"""
        
            oc_class_name_ab = random.choice([0,1])
            if oc_class_name_ab:
                class_str = CSGString.getRandomStr().capitalize() + CSGString.getRandomStr().capitalize()
            else:
                class_str = CSGString.getRandomStr().capitalize()
            return self.obfuse_class_prefix + class_str + random.choice(self.common_suffixes)
        
        
        cls_name = get_class_name()
        fun_name = get_no_param_fun_name()
        
        def get_no_param_func_body():
            return ObjCTemplateManager.get_random_template()
                

        return obj_c_util.gen_fun(cls_name, fun_name, fun_type, mark, rand_rang, letters, get_no_param_func_body)



if __name__ == "__main__":

    opts = obfuscator.argument_parser("simple obfuscator tool")
    opts.add_argument('-t', default="oc", help="obfuse type")
    opts.add_argument('-proj', default=None)
    opts.add_argument('-xcode', default=None)
    opts.add_argument('-root', default=None)
    opts.add_argument('-code_file_num', type=int)
    opts.add_argument('-code_num_min', type=int)
    opts.add_argument('-code_num_max', type=int)
    opts.add_argument('-code_file_fun_num', type=int)
    opts.add_argument('-code_rate', type=int)
    opts.add_argument('-code_file_target', default=None)
    opts.add_argument('-dev_team', default=None)
    opts.add_argument('-profile', default=None)
    opts.add_argument('-rand_img', default=None)
    opts.add_argument('-resource', default=None)
    opts.add_argument('-bundle_identifiler', default=None)
    opts.add_argument('-profile_specifier', default=None)
    opts.add_argument('-code_restore_mode', default='', type=str)
    opts.add_argument('-code_lexicon', default='', type=str)
    opts.add_argument('-element_key', default=None)
    opts.add_argument('-txt_code_fun_path', default=None)
    opts.add_argument('-code_restore_trash_config', default=None)
    opts.add_argument('-add_code_trash_config', default=None)
    opts.add_argument('-code_restore_obfuse_config', default=None)
    opts.add_argument('-restore_trash_file_path', default=None)
    opts.add_argument('-restore_or_obfuse', default='', type=str)
    opts.add_argument('-job_name', default=None)
    opts.add_argument('-sdk_ver', default=None)
    opts.add_argument('-config_params_dic', default=None)
    args = opts.parse_args()

    obfuscator.run(SDK_MOD_OC, args.p, args)
    # obfuscator.run(SDK_MOD_SW, args.p, args)
    print("end obfus")
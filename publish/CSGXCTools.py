import CSGFile
import os


def replace_code_in_file(path, dic):
    if os.path.exists(path):
        f = open(path, 'r')
        file_ext = CSGFile.get_file_ext(path)
        filename = CSGFile.get_filename(path)
        tmp_file_path = path + ".tmp" + file_ext
        f_new = open(tmp_file_path, 'w')
        if file_ext in ['.pbxproj', '.json', '.md']:
            for line in f:
                for key in dic.keys():
                    replace_str = key
                    target_str = dic[key]
                    if replace_str in line:
                        line = line.replace(replace_str, target_str)
                f_new.write(line)
        elif file_ext in [".m", ".mm", ".h"]:
            for line in f:
                for key in dic.keys():
                    if key.endswith('.png') or key.endswith('.json'):
                        replace_str = "\"%s\"" % key.split('.')[0]
                        target_str = "\"%s\"" % dic[key].split('.')[0]
                    else:
                        replace_str = key
                        target_str = dic[key]
                    
                    if '@3x' in replace_str or '@2x' in replace_str:
                        replace_str = replace_str.replace('@3x', '')
                        replace_str = replace_str.replace('@2x', '')
                    if replace_str in line:
                        line = line.replace(replace_str, target_str)
                f_new.write(line)
        f.close()
        f_new.close()
        CSGFile.remove_file(path)
        CSGFile.file_rename(tmp_file_path, filename)
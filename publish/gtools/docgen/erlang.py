# -*- coding: utf8 -*-

import os
import re

import gtools.util.file as file
import gtools.pec.compile as ecompile
import gtools.util.string2 as string2

def to_doc(erl, htm_doc):
        code = file.read_file(erl)

        module  = re.findall("\-module\(\w+\)\.", code)[0]
        pmodule = re.findall("\-extends\(\w+\)\.", code)

        if len(pmodule):
                pmodule = pmodule[0][9:-2]
        else:
                pmodule = None

        exports = re.findall("\-compile\(\[export_all\]\)\.", code)

        if len(exports):
                exports = None
        else:
                exports = re.findall("\-export\(\[.*\]\)\.", code)

                if len(exports):
                        exports = exports[0][9:-3].replace(" ", "").split(",")
                else:
                        exports = []

        code = code.replace("\\\"", "{{QUOT}}")
        code = code.replace("\\'", "{{quot}}")

        temp = {}

        count = 0

        for string3 in re.findall("\"[^\"]*\"", code):
                repl_ident = "{{repl_%d}}" % (count,)

                temp[repl_ident] = string3

                code = code.replace(string3, repl_ident)

                count += 1

        for atom in re.findall("'[^']*'", code):
                repl_ident = "{{repl_%d}}" % (count,)

                temp[repl_ident] = atom

                code = code.replace(atom, repl_ident)

                count += 1

        code = code.replace("->", "$")
        code = string2.str_repl("%.*", code, "")

        funs = re.findall("\.\s*\w+\([^\$]+\$", code)
        funs.extend(re.findall(";\s*\w+\([^\$]+\$", code))

        def restore_fun(fun, temp):
                fun = fun.replace("$", "->")

                for repl_ident in re.findall("\{\{repl_\d+\}\}", fun):
                        fun = fun.replace(repl_ident, temp[repl_ident])

                fun = fun.replace("{{quot}}", "\\'")
                fun = fun.replace("{{QUOT}}", "\\\"")

                return fun

        funs = [(code.find(fun), restore_fun(string2.str_repl("\s{2,}", fun[1:len(fun)].replace("\r", "").replace("\n", ""), " "), temp)) for fun in funs]

        funs.sort(key=lambda fun : fun[0])

        funs = [(ecompile.gen_fun_key(fun[1][0:-2])[0], fun[1] + " pass") for fun in funs]

        name = erl.split("/")[-1].split(".")[0]

        content = "<html><meta charset=\"utf-8\"/><title>%s</title><body style=\"font-size:18px\">" % (name,)
        content += "<a href=\"../index.html\">top</a><br/><font color=blue>%s</font><br/>" % (module,)

        if pmodule:
                content += "<font color=blue>-extends(<a href=\"%s.htm\">%s</a>).</font><br/>" % (pmodule, pmodule)

        fun_num = len(funs)

        for i in range(fun_num):
                key, fun = funs[i]

                if exports and (key not in exports):
                        continue

                if (not i) or (funs[i - 1][0] != key):
                        content += "<br/><font color=blue>%s:</font><br/>" % (key,)

                if i < fun_num - 1 and funs[i + 1][0] == key:
                        content += "<font color=green>%s;</font><br/>" % (fun,)
                else:
                        content += "<font color=green>%s.</font><br/>" % (fun,)

        content += "</body></html>"

        file.write_file("%s/%s.htm" % (htm_doc, name), content)

        return name

def to_docs(name, erl_dir, doc_dir):
        os.system("mkdir %s/%s" % (doc_dir, name))
        os.system("mkdir %s/%s/mods" % (doc_dir, name))

        index_html = "<html><meta charset=\"utf-8\"/><title>%s</title><body style=\"font-size:18px\">" % (name,)

        for root, dirs, files in os.walk(erl_dir):
                for file2 in files:
                        if re.findall("lre\.(?!o_.*)", file2[::-1]) != []:
                                file_path = root + "/" + file2

                                mod_name = to_doc(file_path, "%s/%s/mods" % (doc_dir, name))

                                index_html += "<a href=\"mods/%s.htm\">%s</a><br/>" % \
                                              (mod_name, file_path.replace(erl_dir, "").replace("/", ".")[1:-4])

        index_html += "</body></html>"

        file.write_file("%s/%s/index.html" % (doc_dir, name), index_html)

if __name__ == "__main__":
        to_doc("essdb.erl", ".")
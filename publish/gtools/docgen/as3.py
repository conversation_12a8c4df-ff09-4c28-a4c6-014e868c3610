# -*- coding: utf8 -*-

import os
import re

import gtools.util.file as file
import gtools.pec.compile as ecompile
import gtools.util.string2 as string2

def to_doc(as3, htm_doc, framework = "asgame"):
        code = file.read_file(as3)

        module = re.findall("package\s+[^\r\n\{]+", code)[0]
        module = string2.str_repl("\s+", module, " ")[8:].replace(" ", "")

        is_interface = False

        try:
                class2 = re.findall("class\s+[^\s\r\n]+", code)[0]
        except:
                class2 = re.findall("interface\s+[^\r\n]+", code)[0]

                is_interface = True

        class2 = string2.str_repl("\s+", class2, " ")[6:]

        try:
                pclass = re.findall("extends\s+\w+", code)[0]
                pclass = string2.str_repl("\s+", pclass, " ")[8:]

                if pclass != "Object":
                        pclass2 = re.findall("import\s+.+\.%s" % (pclass,), code)

                        if pclass2 == []:
                                pclass = "%s.%s" % (module, pclass)
                        else:
                                pclass = string2.str_repl("\s+", pclass2[0], " ")[7:]
        except:
                pclass = None

        static_consts = re.findall("public\s+static\s+const\s+[^;]+", code)
        static_consts = [string2.str_repl("\s+", static_const, " ") for static_const in static_consts]

        static_vars = re.findall("public\s+static\s+var\s+[^;]+", code)
        static_vars = [string2.str_repl("\s+", static_var, " ") for static_var in static_vars]

        static_funs = re.findall("public\s+static\s+function\s+[^\r\n\{]+", code)
        static_funs = [string2.str_repl("\s+", static_fun, " ") for static_fun in static_funs]

        member_vars = re.findall("public\s+var\s+[^;]+", code)
        member_vars = [string2.str_repl("\s+", member_var, " ") for member_var in member_vars]

        member_sets = re.findall("public\s+function\s+set\s+[^\r\n\{]+", code)
        member_sets = [string2.str_repl("\s+", member_set, " ") for member_set in member_sets]

        member_gets = re.findall("public\s+function\s+get\s+[^\r\n\{]+", code)
        member_gets = [string2.str_repl("\s+", member_get, " ") for member_get in member_gets]

        member_funs = re.findall("public\s+function\s+[^\r\n\{]+", code)
        member_funs.extend(re.findall("protected\s+function\s+[^\r\n\{]+", code))
        member_funs = [string2.str_repl("\s+", member_fun, " ") for member_fun in member_funs]

        member_funs2 = []

        for member_fun in member_funs:
                if member_fun in member_sets or member_fun in member_gets:
                        continue

                member_funs2.append(member_fun)

        member_funs = member_funs2

        if is_interface:
                interfaces = re.findall("function\s+[^\r\n\{]+", code)
                interfaces = [string2.str_repl("\s+", interface, " ") for interface in interfaces]

        content = "<html><meta charset=\"utf-8\"/><title>%s.%s</title><body style=\"font-size:18px\">" % (module, class2)

        if not is_interface:
                if pclass:
                        if framework in pclass:
                                pcontent = " extends <a href=\"%s.htm\">%s</a>" % (pclass, pclass)
                        else:
                                pcontent = " extends %s" % (pclass,)
                else:
                        pcontent = ""

                content += "<a href=\"../index.html\">top</a><br/><font color=blue>class %s%s</font><br/>" % (class2, pcontent)

                for static_const in static_consts:
                        content += "<font color=green>%s;</font><br/>" % (static_const,)
                for static_var in static_vars:
                        content += "<font color=green>%s;</font><br/>" % (static_var,)
                for static_fun in static_funs:
                        content += "<font color=green>%s;</font><br/>" % (static_fun,)
                for member_var in member_vars:
                        content += "<font color=green>%s;</font><br/>" % (member_var,)
                for member_set in member_sets:
                        content += "<font color=green>%s;</font><br/>" % (member_set,)
                for member_get in member_gets:
                        content += "<font color=green>%s;</font><br/>" % (member_get,)
                for member_fun in member_funs:
                        content += "<font color=green>%s;</font><br/>" % (member_fun,)
        else:
                content += "<a href=\"../index.html\">top</a><br/><font color=blue>interface %s</font><br/>" % (class2,)

                for interface in interfaces:
                        content += "<font color=green>%s;</font><br/>" % (interface,)

        content += "</body></html>"

        file.write_file("%s\\%s.%s.htm" % (htm_doc, module, class2), content)

        return "%s.%s" % (module, class2)

def to_docs(name, as3_dir, doc_dir, framework = "asgame"):
        os.system("md %s\\%s" % (doc_dir, name))
        os.system("md %s\\%s\\mods" % (doc_dir, name))

        index_html_file = "%s\\%s\\index.html" % (doc_dir, name)

        index_html = file.read_file(index_html_file)

        if index_html == "":
                index_html = "<html><meta charset=\"utf-8\"/><title>%s</title><body style=\"font-size:18px\">" % (name,)
        else:
                index_html = index_html[0:-14]

        for root, dirs, files in os.walk(as3_dir):
                for file2 in files:
                        file_path = root + "\\" + file2

                        mod_name = to_doc(file_path, "%s\\%s\\mods" % (doc_dir, name), framework)

                        content = "<a href=\"mods/%s.htm\">%s</a><br/>" % \
                                  (mod_name, file_path.replace(as3_dir, "").replace("\\", ".")[1:-3])

                        if content not in index_html:
                                index_html += content

        index_html += "</body></html>"

        file.write_file(index_html_file, index_html)

if __name__ == "__main__":
        to_doc("CA3dMgr.as", ".")
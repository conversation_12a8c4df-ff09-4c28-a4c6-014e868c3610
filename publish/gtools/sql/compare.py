# -*- coding: utf8 -*-

import gc

import os

import traceback

import re

import bsddb

import time

import gtools.util.file as file
import gtools.util.string2 as string2

BUFF_LEN = 512000000
WORK_BATCH = 200
SLEEP_INTV = 0.02

def parse_end_pos(content, end_char = ","):
        # content = string2.str_repl3("'[^']*'", content, "t", (1, 1))
        # content = string2.str_repl3("\"[^\"]*\"", content, "t", (1, 1))
        content = string2.str_repl("--.*", content, "")

        return len(content) - content[::-1].find(end_char) - 1

def restore_comments(content, comments):
        comments2 = re.findall("\{'COMMENT':\d+\}", content)

        if not len(comments2):
                return content

        for comment in comments2:
                content = content.replace(comment, comments[eval(comment)["COMMENT"]])

        return content

def read_sql(fp, sql, key_word):
        global BUFF_LEN

        slice_idx = sql.find(key_word)

        if slice_idx != -1:
                return False, sql, slice_idx

        sql2 = fp.read(BUFF_LEN)

        if sql2 == "":
                return True, sql, len(sql)

        sql += sql2

        slice_idx = sql.find(key_word)

        if slice_idx == -1:
                return read_sql(fp, sql, key_word)

        return False, sql, slice_idx

def normalize(sql):
        sql = string2.str_repl("/\*\*/;", sql, "")
        sql = string2.str_repl("/\*[^\*]*\*/;", sql, "")
        sql = string2.str_repl("/\*\*/", sql, "")
        sql = string2.str_repl("/\*[^\*]*\*/", sql, "")
        sql = string2.str_repl("\s*--[^\n]*\n", sql, "\n")
        sql = string2.str_repl("\s*#[^\n]*\n", sql, "\n")
        sql = string2.str_repl("\n\s*\r\n", sql, "\n")
        sql = string2.str_repl("\n\s*\n", sql, "\n")

        return sql

def parse_header(head_sql):
        database = None

        info = re.findall("Database: \w*\r{0,1}\n", head_sql)

        if len(info):
                info = string2.str_wipe_break(info[0])

                database = info[10:]

        head_sql = normalize(head_sql)

        #解析数据库名
        info = re.findall("USE [^;]*;", head_sql)

        if len(info):
                info = string2.str_wipe_space(info[0])

                database = info[4:-2]

        if not database:
                raise Exception("no database found.")

        return database

def parse_sql(sql_filename, deal_data = False, diff_tables = ()):
        fp = open(sql_filename, "r")

        content = {}

        print "parse %s..." % (sql_filename,)
        print "parse header..."

        eof, sql, slice_idx = read_sql(fp, "", "DROP TABLE")

        content["database"] = parse_header(sql[0:slice_idx])

        print "parse header ok."

        rest_sql = sql[slice_idx + 10:len(sql)]

        while True:
                eof, sql, slice_idx = read_sql(fp, rest_sql, "DROP TABLE")

                table_sql = sql[0:slice_idx]

                slice_idx2 = table_sql.find("INSERT INTO")

                if slice_idx2 != -1:
                        table_sql2 = table_sql[slice_idx2:len(table_sql)]
                        table_sql  = table_sql[0:slice_idx2]
                else:
                        table_sql2 = ""
                        table_sql  = table_sql

                comments = re.findall("COMMENT\s+'[^']*'", table_sql)

                comment_num = len(comments)

                for i in range(comment_num):
                        table_sql = table_sql.replace(comments[i], "{'COMMENT':%d}" % (i,))

                comments2 = re.findall("COMMENT\s*=\s*'[^']*'", table_sql)

                for i in range(len(comments2)):
                        table_sql = table_sql.replace(comments2[i], "{'COMMENT':%d}" % (comment_num + i,))

                comments.extend(comments2)

                table_sql = normalize(table_sql)

                table_info = re.findall("CREATE TABLE\s*[^;]*;", table_sql)
                if table_info:
                        table_info = table_info[0]
                else:
                        print "skip no create sql"
                        if eof:
                                break

                        rest_sql = sql[slice_idx + 10:len(sql)]
                        continue

                lines = table_info.split("\n")

                lines[0] = string2.str_wipe_break(lines[0])
                lines[0] = string2.str_wipe_space(lines[0])

                table_name = lines[0][12:-2]

                if deal_data and (table_name not in diff_tables):
                        print "skip table:%s." % (table_name,)

                        if eof:
                                break

                        rest_sql = sql[slice_idx + 10:len(sql)]

                        continue

                print "parse table:%s..." % (table_name,)

                primary = ""

                ident = ""

                indices = {}

                uniques = {}

                fields = {}

                ofields = []

                ukeys = []

                lines = lines[1:-1]

                line_num = len(lines)

                for index in range(line_num):
                        line = lines[index]

                        end_char = None

                        if index < line_num - 1:
                                end_char = ","

                        line = string2.str_wipe_break(line)
                        line = string2.str_wipe_border_space(line)

                        if line[0] == "`":
                                end_idx = line[1:len(line)].find("`") + 1

                                key = line[1:end_idx]

                                value = line[end_idx + 1:parse_end_pos(line)]

                                value = string2.str_wipe_border_space(value)

                                value = restore_comments(value, comments)

                                fields[key] = value

                                ofields.append(key)
                        elif line[0] == "P":
                                line = string2.str_wipe_space(line)

                                if not end_char:
                                        end_char = ")"
                                        offset = 1
                                else:
                                        offset = 0

                                primary = line[10:parse_end_pos(line, end_char) + offset]
                        elif line[0] == "K" or \
                             line[0] == "U":
                                line = string2.str_wipe_space(line)

                                if not end_char:
                                        end_char = ")"
                                        offset = 1
                                else:
                                        offset = 0

                                key = line[line.find("("):parse_end_pos(line, end_char) + offset]

                                value = line[(line.find("`") + 1):(line.find("(") - 1)]

                                if line[0] == "K":
                                        indices[key] = value
                                else:
                                        uniques[key] = value

                                ukeys.append(key)

                table_info = restore_comments(table_info, comments)

                if deal_data:
                        if primary == "":
                                if not len(ukeys):
                                        raise Exception("table %s ident key not set" % (table_name,))

                                ident = ukeys[0]
                        else:
                                ident = primary

                content[table_name] = {"fields":fields,
                                       "ofields":ofields,
                                       "primary":primary,
                                       "ident":ident,
                                       "indices":indices,
                                       "uniques":uniques,
                                       "source":table_info,
                                       "data":{}}

                print "parse table:%s structure ok." % (table_name,)

                if deal_data:
                        try:
                                data_infos = re.findall("INSERT INTO `%s` VALUES \(.*\);" % (table_name,), table_sql2)

                                for data_info in data_infos:
                                        table_name = re.findall("INSERT INTO `%s` VALUES" % (table_name,), data_info)[0][13:-8]

                                        ofields = content[table_name]["ofields"]
                                        ident   = eval(content[table_name]["ident"].replace("`", "'"))

                                        if type(ident) != tuple:
                                                ident = (ident,)

                                        data = content[table_name].get("data", {})

                                        records = re.findall("\(.*\)", data_info)[0].split("),(")

                                        for record in records:
                                                if record[0] != "(":
                                                        record = "(" + record

                                                if record[-1] != ")":
                                                        record = record + ")"

                                                record = eval(record)

                                                if type(record) != tuple:
                                                        record = (record,)

                                                data2 = {}

                                                for index in range(len(record)):
                                                        field = ofields[index]

                                                        value = record[index]

                                                        data2[field] = value

                                                ident2 = []

                                                for field in ident:
                                                        ident2.append(data2[field])

                                                data[tuple(ident2)] = data2

                                        content[table_name]["data"] = data
                        except:
                                traceback.print_exc()

                                del content[table_name]

                        print "parse table:%s data ok." % (table_name,)

                if eof:
                        break

                rest_sql = sql[slice_idx + 10:len(sql)]

        print "parse %s ok.\n" % (sql_filename,)

        fp.close()

        return content

def diff_sql(sql1, sql2, deal_data = False, update_callback = None):
        database = sql2["database"]

        if sql1["database"] != database:
                raise Exception("different database %s %s" % (sql1["database"], database))

        del sql1["database"]
        del sql2["database"]

        print "diff sql..."

        sql_code = ""

        for table, content2 in sql2.iteritems():
                print "diff table:%s..." % (table,)

                table_sql_code = ""

                if not sql1.has_key(table):
                        #新表
                        table_sql_code += "DROP TABLE IF EXISTS `%s`;\n" % (table,)
                        table_sql_code += content2["source"] + "\n"
                else:
                        content1 = sql1[table]

                        fields1  = content1["fields"]
                        ofields1 = content1["ofields"]
                        primary1 = content1["primary"]
                        ident1   = content1["ident"]
                        indices1 = content1["indices"]
                        uniques1 = content1["uniques"]

                        fields2  = content2["fields"]
                        ofields2 = content2["ofields"]
                        primary2 = content2["primary"]
                        ident2   = content2["ident"]
                        indices2 = content2["indices"]
                        uniques2 = content2["uniques"]

                        if deal_data and ident1 != ident2:
                                raise Exception("table:%s ident key not match.(%s, %s)" % (table, ident1, ident2))

                        primary_changed = False

                        #检查字段
                        for key, value in fields2.iteritems():
                                if not fields1.has_key(key):
                                        #增加字段
                                        if value.find("AUTO_INCREMENT") != -1:
                                                table_sql_code += "ALTER TABLE `%s` DROP PRIMARY KEY;\n" % (table,)
                                                table_sql_code += "ALTER TABLE `%s` ADD `%s` %s, ADD PRIMARY KEY (`%s`);\n" % (table, key, value, key)

                                                primary_changed = True
                                        else:
                                                table_sql_code += "ALTER TABLE `%s` ADD `%s` %s;\n" % (table, key, value)
                                elif value != fields1[key]:
                                        #更新字段
                                        table_sql_code += "ALTER TABLE `%s` CHANGE `%s` `%s` %s;\n" % (table, key, key, value)

                        #检查键值
                        if primary2 != primary1 and (not primary_changed):
                                #删除主键
                                table_sql_code += "ALTER TABLE `%s` DROP PRIMARY KEY;\n" % (table,)

                        for key, value in indices1.iteritems():
                                if not indices2.has_key(key):
                                        #删除索引键
                                        table_sql_code += "ALTER TABLE `%s` DROP INDEX `%s`;\n" % (table, value)

                        for key, value in uniques1.iteritems():
                                if not uniques2.has_key(key):
                                        #删除唯一键
                                        table_sql_code += "ALTER TABLE `%s` DROP INDEX `%s`;\n" % (table, value)

                        for key in fields1.iterkeys():
                                if not fields2.has_key(key):
                                        #删除字段
                                        table_sql_code += "ALTER TABLE `%s` DROP `%s`;\n" % (table, key)

                        #增加主键
                        if primary2 != primary1 and (not primary_changed) and primary2 != "":
                                table_sql_code += "ALTER TABLE `%s` ADD PRIMARY KEY %s;\n" % (table, primary2)

                                primary_changed = True

                        #增加索引键
                        for key in indices2.iterkeys():
                                if not indices1.has_key(key):
                                        #增加索引
                                        table_sql_code += "ALTER TABLE `%s` ADD INDEX %s;\n" % (table, key)
                                else:
                                        pass

                        #增加唯一键
                        for key in uniques2.iterkeys():
                                if not uniques1.has_key(key):
                                        #增加唯一值
                                        table_sql_code += "ALTER TABLE `%s` ADD UNIQUE %s;\n" % (table, key)
                                else:
                                        pass

                print "diff table:%s structure ok." % (table,)

                if deal_data:
                        #检查数据
                        data1 = sql1.get(table, {"data":{}})["data"]
                        data2 = content2["data"]

                        first = True

                        for key, values in data2.iteritems():
                                data_sql_code = ""

                                if not data1.has_key(key):
                                        #新数据
                                        data_sql_code += "INSERT TABLE `%s` (`%s`) VALUES " % (table, "`,`".join(ofields2))

                                        field_num = len(ofields2)

                                        for index in range(field_num):
                                                field = ofields2[index]

                                                value = values[field]

                                                if index == 0:
                                                        data_sql_code += "("
                                                else:
                                                        data_sql_code += ","

                                                if type(value) == str:
                                                        data_sql_code += "`%s`" % (value)
                                                else:
                                                        data_sql_code += str(value)

                                        data_sql_code += ");\n"

                                        if update_callback:
                                                update_callback(table, ofields2, values)
                                else:
                                        values1 = data1[key]

                                        update = False

                                        if len(values1) != len(values):
                                                update = True
                                        else:
                                                for k, v in values.iteritems():
                                                        if (not values1.has_key(k)) or \
                                                           values1[k] != v:
                                                                update = True

                                                                break

                                        if update:
                                                #更新数据
                                                data_sql_code += "UPDATE TABLE `%s` SET " % (table,)

                                                ident = eval(ident2.replace("`", "'"))

                                                if type(ident) != tuple:
                                                        ident = (ident,)

                                                for field, value in values.iteritems():
                                                        if field in ident:
                                                                continue

                                                        data_sql_code += "`%s`=" % (field)

                                                        if type(value) == str:
                                                                data_sql_code += "`%s`," % (value)
                                                        else:
                                                                data_sql_code += str(value) + ","

                                                data_sql_code = data_sql_code[0:-1] + " where "

                                                for field in ident:
                                                        value = values[field]

                                                        data_sql_code += "`%s`=" % (field,)

                                                        if type(value) == str:
                                                                data_sql_code += "`%s`" % (value,)
                                                        else:
                                                                data_sql_code += str(value)

                                                        data_sql_code += " and "

                                                data_sql_code = data_sql_code[0:-5] + ";\n"

                                                if update_callback:
                                                        update_callback(table, ofields2, values)

                                if data_sql_code != "" and first:
                                        data_sql_code = "\n" + data_sql_code

                                        first = False

                                table_sql_code += data_sql_code

                        print "diff table:%s data ok." % (table,)

                if table_sql_code != "":
                        sql_code += table_sql_code + "\n"

        print "diff sql ok."

        if sql_code == "":
                return ""

        return "USE `%s`;\n\n" % (database,) + sql_code

def mk_patch(sql1, sql2):
        sql1 = parse_sql(sql1)
        sql2 = parse_sql(sql2)

        return diff_sql(sql1, sql2)

def split_sql(sql_filename, diff_tables = ()):
        global SLEEP_INTV

        fp = open(sql_filename, "r")

        print "split %s..." % (sql_filename,)

        _, sql, slice_idx = read_sql(fp, "", "DROP TABLE")

        database = parse_header(sql[0:slice_idx])

        print "split database %s..." % (database,)

        rest_sql = sql[slice_idx + 10:len(sql)]

        while True:
                eof, sql, slice_idx = read_sql(fp, rest_sql, "DROP TABLE")

                table_sql = sql[0:slice_idx]

                table_info = re.findall("CREATE TABLE\s*[^;]*;", table_sql)[0]

                lines = table_info.split("\n")

                lines[0] = string2.str_wipe_break(lines[0])
                lines[0] = string2.str_wipe_space(lines[0])

                table_name = lines[0][12:-2]

                if table_name in diff_tables:
                        print "split table %s..." % (table_name,)

                        fp2 = open("tmp/%s_%s" % (sql_filename.split("/")[-1], table_name), "w")
                        fp2.write(table_sql)
                        fp2.close()

                        print "ok."
                else:
                        print "skip table %s." % (table_name,)

                if eof:
                        break

                rest_sql = sql[slice_idx + 10:len(sql)]

                time.sleep(SLEEP_INTV)

        print "ok."
        print "split %s ok.\n" % (sql_filename,)

        fp.close()

        return database

def parse_tsql(sql_filename, table_name):
        global WORK_BATCH, SLEEP_INTV

        tsql_filename = "tmp/%s_%s" % (sql_filename.split("/")[-1], table_name)

        table_sql = file.read_file(tsql_filename)

        if table_sql == "":
                return {}, bsddb.btopen("%s.db" % (tsql_filename,), "w")

        print "parse table:%s(%s)..." % (table_name, sql_filename)

        slice_idx2 = table_sql.find("INSERT INTO")

        if slice_idx2 != -1:
                table_sql2 = table_sql[slice_idx2:len(table_sql)]
                table_sql  = table_sql[0:slice_idx2]
        else:
                table_sql2 = ""
                table_sql  = table_sql

        comments = re.findall("COMMENT\s+'[^']*'", table_sql)

        comment_num = len(comments)

        for i in range(comment_num):
                table_sql = table_sql.replace(comments[i], "{'COMMENT':%d}" % (i,))

        comments2 = re.findall("COMMENT\s*=\s*'[^']*'", table_sql)

        for i in range(len(comments2)):
                table_sql = table_sql.replace(comments2[i], "{'COMMENT':%d}" % (comment_num + i,))

        comments.extend(comments2)

        table_sql = normalize(table_sql)

        table_info = re.findall("CREATE TABLE\s*[^;]*;", table_sql)[0]

        lines = table_info.split("\n")

        primary = ""

        ident = ""

        indices = {}

        uniques = {}

        fields = {}

        ofields = []

        ukeys = []

        lines = lines[1:-1]

        line_num = len(lines)

        for index in range(line_num):
                line = lines[index]

                end_char = None

                if index < line_num - 1:
                        end_char = ","

                line = string2.str_wipe_break(line)
                line = string2.str_wipe_border_space(line)

                if line[0] == "`":
                        end_idx = line[1:len(line)].find("`") + 1

                        key = line[1:end_idx]

                        value = line[end_idx + 1:parse_end_pos(line)]

                        value = string2.str_wipe_border_space(value)

                        value = restore_comments(value, comments)

                        fields[key] = value

                        ofields.append(key)
                elif line[0] == "P":
                        line = string2.str_wipe_space(line)

                        if not end_char:
                                end_char = ")"
                                offset = 1
                        else:
                                offset = 0

                        primary = line[10:parse_end_pos(line, end_char) + offset]
                elif line[0] == "K" or \
                     line[0] == "U":
                        line = string2.str_wipe_space(line)

                        if not end_char:
                                end_char = ")"
                                offset = 1
                        else:
                                offset = 0

                        key = line[line.find("("):parse_end_pos(line, end_char) + offset]

                        value = line[(line.find("`") + 1):(line.find("(") - 1)]

                        if line[0] == "K":
                                indices[key] = value
                        else:
                                uniques[key] = value

                        ukeys.append(key)

        table_info = restore_comments(table_info, comments)

        if primary == "":
                if not len(ukeys):
                        raise Exception("table %s ident key not set" % (table_name,))

                ident = ukeys[0]
        else:
                ident = primary

        content = {"fields":fields,
                   "ofields":ofields,
                   "primary":primary,
                   "ident":ident,
                   "indices":indices,
                   "uniques":uniques,
                   "source":table_info}

        print "parse table:%s structure ok." % (table_name,)

        data = bsddb.btopen("%s.db" % (tsql_filename,), "w")

        try:
                batch_count = 0

                data_infos = re.findall("INSERT INTO `%s` VALUES \(.*\);" % (table_name,), table_sql2)

                for data_info in data_infos:
                        ofields = content["ofields"]
                        ident   = eval(content["ident"].replace("`", "'"))

                        if type(ident) != tuple:
                                ident = (ident,)

                        records = re.findall("\(.*\)", data_info)[0].split("),(")

                        for record in records:
                                if record[0] != "(":
                                        record = "(" + record

                                if record[-1] != ")":
                                        record = record + ")"

                                record = eval(record)

                                if type(record) != tuple:
                                        record = (record,)

                                data2 = {}

                                for index in range(len(record)):
                                        field = ofields[index]

                                        value = record[index]

                                        data2[field] = value

                                ident2 = []

                                for field in ident:
                                        ident2.append(data2[field])

                                data[repr(tuple(ident2))] = repr(data2)

                                batch_count += 1

                                if batch_count == WORK_BATCH:
                                        batch_count = 0

                                        data.sync()

                                        time.sleep(SLEEP_INTV)
        except:
                traceback.print_exc()

        data.sync()

        print "parse table:%s data ok." % (table_name,)

        return content, data

def diff_tsql(table, content1, data1, content2, data2, update_callback = None):
        global WORK_BATCH, SLEEP_INTV

        print "diff table:%s..." % (table,)

        table_sql_code = ""

        if len(content1) == 0:
                #新表
                table_sql_code += "DROP TABLE IF EXISTS `%s`;\n" % (table,)
                table_sql_code += content2["source"] + "\n"
        else:
                fields1  = content1["fields"]
                ofields1 = content1["ofields"]
                primary1 = content1["primary"]
                ident1   = content1["ident"]
                indices1 = content1["indices"]
                uniques1 = content1["uniques"]

                fields2  = content2["fields"]
                ofields2 = content2["ofields"]
                primary2 = content2["primary"]
                ident2   = content2["ident"]
                indices2 = content2["indices"]
                uniques2 = content2["uniques"]

                if ident1 != ident2:
                        raise Exception("table:%s ident key not match.(%s, %s)" % (table, ident1, ident2))

                primary_changed = False

                #检查字段
                for key, value in fields2.iteritems():
                        if not fields1.has_key(key):
                                #增加字段
                                if value.find("AUTO_INCREMENT") != -1:
                                        table_sql_code += "ALTER TABLE `%s` DROP PRIMARY KEY;\n" % (table,)
                                        table_sql_code += "ALTER TABLE `%s` ADD `%s` %s, ADD PRIMARY KEY (`%s`);\n" % (table, key, value, key)

                                        primary_changed = True
                                else:
                                        table_sql_code += "ALTER TABLE `%s` ADD `%s` %s;\n" % (table, key, value)
                        elif value != fields1[key]:
                                #更新字段
                                table_sql_code += "ALTER TABLE `%s` CHANGE `%s` `%s` %s;\n" % (table, key, key, value)

                #检查键值
                if primary2 != primary1 and (not primary_changed):
                        #删除主键
                        table_sql_code += "ALTER TABLE `%s` DROP PRIMARY KEY;\n" % (table,)

                for key, value in indices1.iteritems():
                        if not indices2.has_key(key):
                                #删除索引键
                                table_sql_code += "ALTER TABLE `%s` DROP INDEX `%s`;\n" % (table, value)

                for key, value in uniques1.iteritems():
                        if not uniques2.has_key(key):
                                #删除唯一键
                                table_sql_code += "ALTER TABLE `%s` DROP UNIQUE `%s`;\n" % (table, value)

                for key in fields1.iterkeys():
                        if not fields2.has_key(key):
                                #删除字段
                                table_sql_code += "ALTER TABLE `%s` DROP `%s`;\n" % (table, key)

                #增加主键
                if primary2 != primary1 and (not primary_changed) and primary2 != "":
                        table_sql_code += "ALTER TABLE `%s` ADD PRIMARY KEY %s;\n" % (table, primary2)

                        primary_changed = True

                #增加索引键
                for key in indices2.iterkeys():
                        if not indices1.has_key(key):
                                #增加索引
                                table_sql_code += "ALTER TABLE `%s` ADD INDEX %s;\n" % (table, key)
                        else:
                                pass

                #增加唯一键
                for key in uniques2.iterkeys():
                        if not uniques1.has_key(key):
                                #增加唯一值
                                table_sql_code += "ALTER TABLE `%s` ADD UNIQUE %s;\n" % (table, key)
                        else:
                                pass

        print "diff table:%s structure ok." % (table,)

        change_count = 0

        batch_count = 0

        #检查数据
        for key, values in data2.iteritems():
                values = eval(values)

                if not data1.has_key(key):
                        #新数据
                        field_num = len(ofields2)

                        for index in range(field_num):
                                field = ofields2[index]

                                value = values[field]

                        change_count += 1

                        if update_callback:
                                update_callback(table, ofields2, values)
                else:
                        values1 = eval(data1[key])

                        update = False

                        if len(values1) != len(values):
                                update = True
                        else:
                                for k, v in values.iteritems():
                                        if (not values1.has_key(k)) or \
                                           values1[k] != v:
                                                update = True

                                                break

                        if update:
                                change_count += 1

                                if update_callback:
                                        update_callback(table, ofields2, values)

                batch_count += 1

                if batch_count == WORK_BATCH:
                        batch_count = 0

                        time.sleep(SLEEP_INTV)

        print "%d changed." % (change_count,)
        print "diff table:%s data ok." % (table,)

        return table_sql_code

def mk_diff(sql1, sql2, update_callback = None, diff_tables = ()):
        gc.disable()

        def defer():
                os.system("rm -rf tmp")

                gc.enable()
                gc.collect()

        if os.path.exists("tmp"):
                os.system("rm -rf tmp")

        os.system("mkdir tmp")

        try:
                database1 = split_sql(sql1, diff_tables)
                database2 = split_sql(sql2, diff_tables)

                if database1 != database2:
                        raise Exception("different database %s %s" % (database1, database2))

                for diff_table in diff_tables:
                        print "diff table %s..." % (diff_table,)

                        content1, data1 = parse_tsql(sql1, diff_table)
                        content2, data2 = parse_tsql(sql2, diff_table)

                        diff_tsql(diff_table, content1, data1, content2, data2, update_callback)

                        os.system("rm tmp/*.db")

                        print "ok."

                defer()
        except:
                traceback.print_exc()

                defer()

                raise Exception("error")

#配置
#buff为读取文件数据的缓冲区大小
#batch是单帧处理的任务数量
#fps是逻辑帧率

def config(buff, batch, fps):
        global BUFF_LEN, WORK_BATCH, SLEEP_INTV

        BUFF_LEN = buff
        WORK_BATCH = batch
        SLEEP_INTV = 1 / fps

if __name__ == "__main__":
        #diff_tables = ("partner",)
        #mk_diff("sagme1.sql", "sgame2.sql", None, diff_tables)
        print mk_patch("sgame31.sql", "sgame32.sql")
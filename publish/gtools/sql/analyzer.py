# -*- coding: utf8 -*-

import re

import gtools.util.string2 as string2

def loads(data):
        tables = {}

        for comment in re.findall("COMMENT '[^']*'|COMMENT='[^']*'", data):
                if comment.find(";") != -1:
                        data = data.replace(comment, comment.replace(";", "%3B"))

        tables2 = re.findall("CREATE TABLE[^;]+;", data)

        for table in tables2:
                info = re.findall("`[^`]+`", table)

                name = info[0][1:-1]
                tables[name] = {"sql":table, "fields":[], "rows":[], "uniques":{}}

                for field in info[1:]:
                        field = field[1:-1]
                        if field not in tables[name]["fields"]:
                                tables[name]["fields"].append(field)

                for field in re.findall("\(`[^`]+`\)", table):
                        tables[name]["uniques"][field[2:-2]] = {}

                field_num = len(tables[name]["fields"])

                prefix = "INSERT INTO `%s` VALUES" % (name,)

                for values in re.findall("%s [^;]+;" % (prefix,), data):
                        for values in eval("[" + values[len(prefix):-1] + "]"):
                                row = {}

                                for i in range(field_num):
                                        row[tables[name]["fields"][i]] = values[i]

                                tables[name]["rows"].append(row)

                                for field, info in tables[name]["uniques"].iteritems():
                                        info[row[field]] = row

        return tables

def dumps(content):
        sql = ""

        for table, info in content.iteritems():
                sql += "DROP TABLE IF EXISTS `%s`;\n" % (table,) + info["sql"] + "\n"

                if len(info["rows"]):
                        sql += "LOCK TABLES `%s` WRITE;\n" % (table,)

                        for row in info["rows"]:
                                sql += "INSERT INTO `%s` VALUES (" % (table,) + ",".join(["'%s'"%row[field] for field in info["fields"]]) + ");\n"

                        sql += "UNLOCK TABLES;\n"

        return sql

def clear(content):
        for value in content.itervalues():
                value["rows"] = []
                for key in value["uniques"].iterkeys():
                        value["uniques"][key] = {}

def cmp_sql(sql1, sql2):
        sql1 = string2.str_wipe_space(sql1).replace("'", "\"").replace("\r\n", "\n")
        sql2 = string2.str_wipe_space(sql2).replace("'", "\"").replace("\r\n", "\n")

        sql1 = sql1.split("\n")
        sql1.sort()

        sql2 = sql2.split("\n")
        sql2.sort()

        return tuple(sql1) == tuple(sql2)

def merge(flag, data1, data2, funs1, funs2, base_table):
        tables = data1.keys()
        tables.remove(base_table)
        tables.insert(0, base_table)

        for table in tables:
                print "merge table:%s ..." % (table,)

                info = data1[table]

                if not data2.has_key(table):
                        raise Exception("sql not match, table:%s" % (table,))

                if not cmp_sql(data2[table]["sql"], info["sql"]):
                        raise Exception("sql not match, table:%s" % (table,))

                for row in data2[table]["rows"]:
                        for field in row:
                                if funs2.has_key((table, field)):
                                        row[field] = funs2[(table, field)](data1[base_table]["uniques"], flag, row[field])

                        if funs2.has_key(table):
                                merge, unique = funs2[table](data1[base_table]["uniques"], info["uniques"], row)
                        else:
                                merge = True
                                unique = None

                        if unique is not None:
                                k, v, fields = unique

                                if type(fields) == str:
                                        fields = (fields,)

                                for field in fields:
                                        if funs1.has_key((table, field)):
                                                ret = funs1[(table, field)](data1[base_table]["uniques"], info["uniques"][k][v][field], row[field])

                                                if ret is None:
                                                        info["rows"].remove(info["uniques"][k][v])

                                                        del info["uniques"][k][v]
                                                else:
                                                        info["uniques"][k][v][field] = ret
                        if merge:
                                for field in row:
                                        if info["uniques"].has_key(field):
                                                info["uniques"][field][row[field]] = row

                                info["rows"].append(row)

                print "ok."
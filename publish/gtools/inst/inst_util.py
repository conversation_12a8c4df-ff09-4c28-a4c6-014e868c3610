# -*- coding: utf8 -*-

import os

import traceback

import gtools.util.file as file
import gtools.util.scp  as scp

import config

#检查是否更新或重新安装
#return 0不通过 1更新版本 2重新安装
def ask_if_override(app_dir, override):
        if os.path.exists(app_dir):
                if override:
                        Question = "Are you sure to force installing? \nWarnning: This operation will clear all data!!!! (y/n)\n"

                        ret = raw_input(Question)

                        if ret == "y" or ret == "Y":
                                ret = raw_input("Again!\n" + Question)

                                if ret == "y" or ret == "Y":
                                        os.system("rm -rf %s" % (app_dir,))
                                        return 2

                        return 0
                else:
                    return 1
        return 2



def check_version(version_file, new_version):
        try:
                old_version = config.parse_version(file.read_file(version_file))
        except:
                old_version = new_version

        if new_version[0] != old_version[0] or \
           new_version[1] != old_version[1] or \
           new_version[2] <  old_version[2] or \
           new_version[3] <= old_version[3]:
                print "version set may be invalid, please check. (%s ==> %s)" % (".".join([str(i) for i in old_version]),
                                                                                 ".".join([str(i) for i in new_version]))

                return False, old_version

        return True, old_version

def download(alias, version, publish_dir, local_dir, type = "tar.gz", password = None):
        cwd = os.getcwd()

        tar_gz = "%s-%s.%s" % (alias, version, type)

        local_tar_gz = "%s/%s" % (local_dir, tar_gz)

        local_exists = False

        if not os.path.exists(local_tar_gz):
                try:
                        scp.download(publish_dir + "/" + tar_gz, local_dir, password = password)
                except:
                        traceback.print_exc()

                        return False

                local_exists = os.path.exists(local_tar_gz)
        else:
                local_exists = True

        if not local_exists:
                print "version not exists, please check. (%s)" % (version,)

                return False

        os.chdir(local_dir)
        if type == "tar.gz":
                os.system("tar -zxvf %s" % (tar_gz,))
        elif type == "zip":
                os.system("unzip %s" % (tar_gz,))
        os.chdir(cwd)

        tmp_src_dir = "%s/%s-%s" % (local_dir, alias, version)

        if not os.path.exists(tmp_src_dir):
                print "version has error, please check. (%s)" % (version,)

                return False

        return True

def export_src(src_dir, unit_src_dir, unit_out_dir):
        if unit_src_dir == "":
                return

        if unit_src_dir == "*":
                os.system("cp -R %s/* %s/" % (src_dir, unit_out_dir))

                return

        full_src_dir = src_dir + "/" + unit_src_dir

        if not os.path.exists(full_src_dir):
                return

        if not os.path.exists(unit_out_dir):
                os.system("mkdir " + unit_out_dir)

        if os.listdir(full_src_dir):
                os.system("cp -R %s/%s/* %s/" % (src_dir, unit_src_dir, unit_out_dir))

def restore(app_dir):
        app_bak_dir = "%s_bak" % (app_dir,)

        if os.path.exists(app_bak_dir):
                os.system("cp -R %s/* %s/" % (app_bak_dir, app_dir))
                os.system("rm -rf %s" % (app_bak_dir,))
        else:
                os.system("rm -rf %s" % (app_dir,))
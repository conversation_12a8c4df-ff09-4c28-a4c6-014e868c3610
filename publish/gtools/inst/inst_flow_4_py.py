# -*- coding: utf8 -*-

import os

import traceback

import re

import gtools.inst.config      as config

import gtools.util.file        as file
import gtools.util.string2     as string2
import gtools.util.os2         as os2
import gtools.util.scp         as scp
import gtools.util.lock        as lock

import gtools.inst.inst_util  as inst_util


class CInstFlow:
        def __init__(self, config):
                self._config = config
                self._dict_tmp_sql_dir = {}

        def install_app(self, ident, override = False):
                detail = self._config.APP_DEPLOY.get(ident, None)

                if not detail:
                        print "ident invalid."

                        return False

                cwd = os.getcwd()

                app_dir = "%s/%s_%d" % (config.APP_ROOT_DIR, self._config.ALIAS[1], ident)

                app_lck = lock.lock("%s.lck" % (app_dir,))


                #判断是否从新安装或者更新
                ask_result = inst_util.ask_if_override(app_dir, override)
                if ask_result == 1:
                        do_update = True;
                elif ask_result == 2:
                        do_update = False;
                else:
                        lock.unlock(app_lck)
                        return False

                out_conf_dir    = app_dir + "/conf"
                out_depend_dir  = app_dir + "/depend"
                out_script_dir  = app_dir + "/script"
                out_sbin_dir    = app_dir + "/sbin"
                out_log_dir     = app_dir + "/log"
                out_dist_dir    = app_dir + "/dist"
                out_var_dir     = app_dir + "/var"
                out_src_dir     = app_dir + "/src"
                out_sql_dir     = app_dir + "/sql"

                version_file = app_dir + "/version.dat"

                tmp_log_dir = config.APP_TEMP_DIR + "/log_" + self._config.ALIAS[1] + "_" + str(ident)
                tmp_var_dir = config.APP_TEMP_DIR + "/var_" + self._config.ALIAS[1] + "_" + str(ident)
                tmp_sql_dir = self.get_tmp_sql_dir(ident)



                new_version = detail["version"]

                old_version = new_version

                if do_update:
                        version_ok, old_version = inst_util.check_version(version_file, new_version)
                        if not version_ok:
                                lock.unlock(app_lck)
                                return False

                        #copy当前项目的sql，用作与新版本sql做对比
                        if os.path.exists(out_sql_dir):
                                os2.system_cp2(out_sql_dir, tmp_sql_dir)

                        os2.system_cp2(app_dir, "%s_bak" % (app_dir,), (out_log_dir, out_dist_dir))

                        os2.clear_dir(app_dir, (out_log_dir, out_var_dir, out_dist_dir))

                else:
                        os.system("rm -rf %s" % (app_dir,))
                        os.system("mkdir %s" % (app_dir,))

                tmp_lck = lock.lock("%s/%s-%s.lck" % (config.APP_TEMP_DIR, self._config.ALIAS[1], detail["str_version"]))

                alias = "%s%s"%(self._config.ALIAS[0],self._config.ALIAS[1])
                if not inst_util.download(alias, detail["str_version"], self._config.PUBLISH_DIR, config.APP_TEMP_DIR):
                        inst_util.restore(app_dir)

                        lock.unlock(tmp_lck)
                        lock.unlock(app_lck)

                        return False

                tmp_src_dir = "%s/%s-%s" % (config.APP_TEMP_DIR, alias, detail["str_version"])

                inst_util.export_src(tmp_src_dir, "conf",   out_conf_dir)
                inst_util.export_src(tmp_src_dir, "depend", out_depend_dir)
                inst_util.export_src(tmp_src_dir, "script", out_script_dir)
                inst_util.export_src(tmp_src_dir, "sbin",   out_sbin_dir)
                inst_util.export_src(tmp_src_dir, "src",    out_src_dir)

                inst_util.export_src(tmp_src_dir, "dist",   out_dist_dir)
                inst_util.export_src(tmp_src_dir, "log",    out_log_dir)
                inst_util.export_src(tmp_src_dir, "var",    out_var_dir)
                inst_util.export_src(tmp_src_dir, "sql",    out_sql_dir)


                os.system("rm -rf %s" % (tmp_src_dir))

                lock.unlock(tmp_lck)

                success = True

                try:
                        alter_db = False

                        #更改配置表
                        os.chdir(out_conf_dir)
                        self.modify_conf(ident, detail)
                        os.chdir(cwd)

                        #安装依赖库
                        if os.path.exists(out_depend_dir):
                            os.chdir(out_depend_dir)
                            self.install_dep(ident, detail, do_update)
                            os.chdir(cwd)

                        os.system("chmod a+x -R " + out_sbin_dir)

                        #自动对比新旧sql，生成alter语句
                        if out_sql_dir != "" and os.path.exists(out_sql_dir):
                                os.chdir(out_sql_dir)
                                alter_db = self.modify_sql(ident, detail, do_update, old_version, new_version)
                                os.chdir(cwd)

                        #导入sql
                        if not do_update:
                                if out_sql_dir != "" and os.path.exists(out_sql_dir):
                                        #导入数据库
                                        os.chdir(out_sql_dir)
                                        self.import_db(detail)
                                        os.chdir(cwd)
                        else:
                                #维护数据库
                                if alter_db:
                                        os.chdir(tmp_sql_dir)
                                        self.backup_db(ident, detail, old_version)
                                        os.chdir(cwd)

                                        os.chdir(out_sql_dir)
                                        try:
                                                self.alter_db(old_version,
                                                              new_version,
                                                              detail)
                                        except:
                                                os.chdir(cwd)
                                                os.chdir(tmp_sql_dir)
                                                self.restore_db(ident, detail, old_version)

                                                traceback.print_exc()

                                                raise Exception("AlterDbError.")

                                        os.chdir(cwd)

                        #写版本号
                        file.write_file(version_file, detail["str_version"])

                        print "install app %s_%s success." % (alias, ident)
                except:
                        success = False

                        traceback.print_exc()

                        os.chdir(cwd)

                        #还原备份
                        inst_util.restore(app_dir)

                #清空目录
                if do_update:
                        if os.path.exists(tmp_sql_dir):
                                os.system("rm -rf %s" % (tmp_sql_dir,))

                        os.system("rm -rf %s_bak" % (app_dir,))

                lock.unlock(app_lck)

                return success

        def modify_conf(self, ident, detail):
                pass

        def install_dep(self, ident, detail, do_update):
                pass

        def get_uversion(self, version):
                return "%d.%d.%d" % (version[1], version[2], version[3])

        def db_name(self):
                return ""

        def get_tmp_sql_dir(self, ident):
                if self._dict_tmp_sql_dir.has_key(ident):
                        return self._dict_tmp_sql_dir[ident]

                tmp_sql_dir = os.path.abspath(config.APP_TEMP_DIR + "/sql_" + self._config.ALIAS[1] + "_" + str(ident))

                self._dict_tmp_sql_dir[ident] = tmp_sql_dir

                return tmp_sql_dir


        def db_bak_filename(self, ident, version):
                return "%s_%s_%s-%d.%d.%d.bak" % (self.db_name(), ident,
                                                  version[0], version[1], version[2], version[3])

        def backup_db(self, ident, detail, version):
                db_name = self.db_name() + "_" + str(ident)

                db_bak_filename = self.db_bak_filename(ident, version)

                os2.system("mysqldump -h%s -P%s -u%s -p%s --skip-lock-tables %s > %s" % \
                           (detail["db_host"], detail["db_port"],
                            detail["db_username"], detail["db_password"],
                            db_name, db_bak_filename))

                content = file.read_file(db_bak_filename)

                content = "USE `%s`;\n\n%s" % (db_name, content)

                file.write_file(db_bak_filename, content)

                print "backup db ok."

        def restore_db(self, ident, detail, version):
                db_bak_filename = self.db_bak_filename(ident, version)

                os2.system("mysql -h%s -P%s -u%s -p%s < %s" % \
                           (detail["db_host"], detail["db_port"],
                            detail["db_username"], detail["db_password"],
                            db_bak_filename))

                print "restore db ok."

        def modify_sql(self, ident, detail,
                             do_update,
                             old_version, new_version):
                return False


        def import_db(self, detail):
                pass

        def alter_db(self, old_version, new_version, detail):
                pass
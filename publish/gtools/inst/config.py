# -*- coding: utf8 -*-

import re
import traceback

APP_ROOT_DIR = "../app"
APP_TEMP_DIR = "../dist"

def parse_version(str_version):
        version = [i for i in str_version.rsplit("-", 1)]
        version.extend([int(i) for i in version[1].split(".")])
        version.remove(version[1])
        return version

def check_cfg(config):
        for ident, detail in config.APP_DEPLOY.iteritems():
                try:
                        if type(ident) != int:
                                raise Exception("Ident must be integer")

                        if not detail.has_key("version"):
                                raise Exception("App %s must set version." % (ident,))

                        version = detail["version"]

                        if type(version) != type(""):
                                raise Exception("App %s version must be string." % (ident,))

                        detail["str_version"] = re.findall("\S+-\d\.\d\.\d+", detail["version"])[0]
                        detail["version"]     = parse_version(detail["str_version"])
                except :
                        traceback.print_exc()
                        raise Exception("Error in config=%s,ident=%s\n"%(config.__name__, ident))
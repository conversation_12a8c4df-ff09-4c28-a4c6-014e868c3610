# -*- coding: utf8 -*-

import os

import traceback

import re

import config

import gtools.util.file        as file
import gtools.util.string2     as string2
import gtools.util.os2         as os2
import gtools.util.scp         as scp
import gtools.util.lock        as lock

import inst_util

class CInstFlow:
        def __init__(self, config):
                self._config = config

        def install_app(self, ident, override = False):
                detail = self._config.APP_DEPLOY.get(ident, None)

                if not detail:
                        print("ident invalid.")

                        return False

                cwd = os.getcwd()

                app_dir = "%s/%s_%d" % (config.APP_ROOT_DIR, self._config.ALIAS[1], ident)

                app_lck = lock.lock("%s.lck" % (app_dir,))

                #判断是否从新安装或者更新
                ask_result = inst_util.ask_if_override(app_dir, override)
                if ask_result == 1:
                        do_update = True;
                elif ask_result == 2:
                        do_update = False;
                else:
                        lock.unlock(app_lck)
                        return False

                out_conf_dir   = app_dir + "/conf"
                out_depend_dir = app_dir + "/depend"
                out_bin_dir    = app_dir + "/bin"
                out_sbin_dir   = app_dir + "/sbin"
                out_log_dir    = app_dir + "/log"
                out_var_dir    = app_dir + "/var"

                version_file = app_dir + "/version.dat"

                tmp_log_dir = config.APP_TEMP_DIR + "/log_" + self._config.ALIAS[1] + "_" + str(ident)
                tmp_var_dir = config.APP_TEMP_DIR + "/var_" + self._config.ALIAS[1] + "_" + str(ident)

                new_version = detail["version"]
                old_version = new_version

                if do_update:
                        version_ok, old_version = inst_util.check_version(version_file, new_version)
                        if not version_ok:
                                lock.unlock(app_lck)

                                return False

                        os2.system_cp2(app_dir, "%s_bak" % (app_dir,), (out_log_dir, out_var_dir))
                else:
                        os.system("mkdir %s" % (app_dir,))

                alias = "".join(self._config.ALIAS)

                tmp_lck = lock.lock("%s/%s-%s.lck" % (config.APP_TEMP_DIR, alias, detail["str_version"]))

                if not inst_util.download(alias, detail["str_version"], self._config.PUBLISH_DIR, config.APP_TEMP_DIR):
                        inst_util.restore(app_dir)

                        lock.unlock(tmp_lck)
                        lock.unlock(app_lck)

                        return False

                tmp_src_dir = "%s/%s-%s" % (config.APP_TEMP_DIR, alias, detail["str_version"])

                inst_util.export_src(tmp_src_dir, "conf",   out_conf_dir)
                inst_util.export_src(tmp_src_dir, "depend", out_depend_dir)
                inst_util.export_src(tmp_src_dir, "bin",    out_bin_dir)
                inst_util.export_src(tmp_src_dir, "sbin",   out_sbin_dir)
                inst_util.export_src(tmp_src_dir, "log",    out_log_dir)
                inst_util.export_src(tmp_src_dir, "var",    out_var_dir)

                os.system("rm -rf %s" % (tmp_src_dir))

                lock.unlock(tmp_lck)

                success = True

                try:
                        os.chdir(out_conf_dir)
                        self.modify_conf(ident, detail)
                        os.chdir(cwd)

                        os.chdir(out_depend_dir)
                        self.install_dep(ident, detail, do_update)
                        os.chdir(cwd)

                        if not do_update:
                                os.chdir(out_log_dir)
                                self.link_log(ident, detail)
                                os.chdir(cwd)

                        os.system("chmod a+x -R " + out_sbin_dir)

                        #写版本号
                        file.write_file(version_file, detail["str_version"])

                        print("install app %s_%s success." % (alias, ident))
                except:
                        success = False

                        traceback.print_exc()

                        os.chdir(cwd)

                        #还原备份
                        inst_util.restore(app_dir)

                if do_update:
                        os.system("rm -rf %s_bak" % (app_dir,))

                lock.unlock(app_lck)

                return success

        def modify_conf(self, ident, detail):
                pass

        def install_dep(self, ident, detail, do_update):
                pass

        def link_log(self, ident, detail):
                pass
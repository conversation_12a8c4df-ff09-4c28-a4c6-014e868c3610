# -*- coding: utf8 -*-

import os
import sys

import traceback

import re

import gtools.inst.config      as config

import gtools.util.file        as file
import gtools.util.string2     as string2
import gtools.util.os2         as os2
import gtools.util.scp         as scp
import gtools.util.lock        as lock

class CInstFlow:
        def __init__(self, config):
                self._config = config

                self._dict_tmp_sql_dir = {}

                self._dict_tmp_ebin_dir = {}

        def skip_break(self):
                command = " ".join(sys.argv)

                return re.findall("\s+--skip_break\W*.*$", command) != []

        def skip_override(self):
                command = " ".join(sys.argv)

                return re.findall("\s+--skip_override\W*.*$", command) != []

        def get_app_dir(self, ident):
                return "%s/%s_%d" % (config.APP_ROOT_DIR, self._config.ALIAS[1], ident)

        def get_log_dir(self, ident):
                return "%s/log" % (self.get_app_dir(ident),)

        def get_www_dir(self, ident):
                return "%s/www" % (self.get_app_dir(ident),)

        def get_uversion(self, version):
                return "%d.%d.%d" % (version[1], version[2], version[3])

        def get_log_run_path(self):
                return "../log/"

        def get_web_run_path(self):
                return "../www/"

        def get_app_src_dir(self):
                return "program/app"

        def get_ebin_src_dir(self):
                return "program/ebin"

        def get_sql_src_dir(self):
                return "sql"

        def get_sbin_src_dir(self):
                return "sbin"

        def get_log_src_dir(self):
                return "log"

        def get_www_src_dir(self):
                return "www"

        def get_tmp_log_dir(self, ident):
                return config.APP_TEMP_DIR + "/log_" + self._config.ALIAS[1] + "_" + str(ident)

        def get_tmp_sql_dir(self, ident):
                if self._dict_tmp_sql_dir.has_key(ident):
                        return self._dict_tmp_sql_dir[ident]

                tmp_sql_dir = os.path.abspath(config.APP_TEMP_DIR + "/sql_" + self._config.ALIAS[1] + "_" + str(ident))

                self._dict_tmp_sql_dir[ident] = tmp_sql_dir

                return tmp_sql_dir

        def get_tmp_ebin_dir(self, ident):
                if self._dict_tmp_ebin_dir.has_key(ident):
                        return self._dict_tmp_ebin_dir[ident]

                tmp_ebin_dir = os.path.abspath(config.APP_TEMP_DIR + "/ebin_" + self._config.ALIAS[1] + "_" + str(ident))

                self._dict_tmp_ebin_dir[ident] = tmp_ebin_dir

                return tmp_ebin_dir

        def restore_app(self, ident, app_dir):
                app_bak_dir = "%s_bak" % (app_dir,)

                if os.path.exists(app_bak_dir):
                        os.system("cp -R %s/* %s/" % (app_bak_dir, app_dir))
                        os.system("rm -rf %s" % (app_bak_dir,))
                else:
                        os.system("rm -rf %s" % (app_dir,))

        def install_app(self, ident, override = False):
                detail = self._config.APP_DEPLOY.get(ident, None)

                if not detail:
                        print "ident invalid."

                        return False

                if self.skip_override():
                        override = False

                cwd = os.getcwd()

                #创建app目录
                app_dir = self.get_app_dir(ident)

                app_lck = lock.lock("%s.lck" % (app_dir,))

                oinst = False  #是否删档重新安装

                if os.path.exists(app_dir) and override:
                        if not self.skip_break():
                                Question = "Are you sure to force installing? \nWarnning: This operation will clear all data!!!! (y/n)\n"

                                ret = raw_input(Question)

                                if ret == "y" or ret == "Y":
                                        ret = raw_input("Again!\n" + Question)

                                        if ret != "y" and ret != "Y":
                                                lock.unlock(app_lck)

                                                return False
                                else:
                                        lock.unlock(app_lck)

                                        return False

                        oinst = True

                out_app_dir2    = app_dir + "/app"
                out_ebin_dir    = app_dir + "/ebin"
                out_sql_dir     = app_dir + "/sql"
                out_sbin_dir    = app_dir + "/sbin"
                out_log_dir     = self.get_log_dir(ident)
                out_www_dir     = self.get_www_dir(ident)

                version_file = app_dir + "/version.dat"

                tmp_log_dir  = self.get_tmp_log_dir(ident)
                tmp_sql_dir  = self.get_tmp_sql_dir(ident)
                tmp_ebin_dir = self.get_tmp_ebin_dir(ident)

                do_update = False

                old_version = detail["version"]
                new_version = detail["version"]

                if os.path.exists(app_dir):
                        if not oinst:
                                do_update = True

                                try:
                                        old_version = config.parse_version(file.read_file(version_file))
                                except:
                                        pass

                                if new_version[0] != old_version[0] or \
                                   new_version[1] != old_version[1] or \
                                   (new_version[2] == old_version[2] and \
                                    new_version[3] == old_version[3]):
                                        print "version set may be invalid, please check. (%s ==> %s)" % (".".join([str(i) for i in old_version]),
                                                                                                         ".".join([str(i) for i in new_version]))

                                        lock.unlock(app_lck)

                                        return False

                                os2.system_cp2(app_dir, "%s_bak" % (app_dir,), (out_log_dir, out_sql_dir))

                                if os.path.exists(out_sql_dir):
                                        os2.system_cp2(out_sql_dir, tmp_sql_dir, ("%s/alter" % (out_sql_dir,),
                                                                                  "%s/data" % (out_sql_dir,)))

                                os.system("cp -R %s %s" % (out_ebin_dir, tmp_ebin_dir))
                        else:
                                os.system("rm -rf %s" % (app_dir,))
                                os.system("mkdir %s" % (app_dir,))
                else:
                        os.system("mkdir %s" % (app_dir,))

                alias = "".join(self._config.ALIAS)

                tmp_lck = lock.lock("%s/%s-%s.lck" % (config.APP_TEMP_DIR, alias, detail["str_version"]))

                tar_gz = "%s-%s.tar.gz" % (alias, detail["str_version"])

                local_tar_gz = "%s/%s" % (config.APP_TEMP_DIR, tar_gz)

                local_exists = False

                if not os.path.exists(local_tar_gz):
                        try:
                                scp.download(self._config.PUBLISH_DIR + "/" + tar_gz, config.APP_TEMP_DIR)
                        except:
                                traceback.print_exc()

                                self.restore_app(ident, app_dir)

                                lock.unlock(tmp_lck)
                                lock.unlock(app_lck)

                                return False

                        local_exists = os.path.exists(local_tar_gz)
                else:
                        local_exists = True

                if not local_exists:
                        print "version not exists, please check. (%s)" % (detail["str_version"])

                        self.restore_app(ident, app_dir)

                        lock.unlock(tmp_lck)
                        lock.unlock(app_lck)

                        return False

                os.chdir(config.APP_TEMP_DIR)
                os.system("tar -zxvf %s" % (tar_gz,))
                os.chdir(cwd)

                tmp_src_dir = "%s/%s-%s" % (config.APP_TEMP_DIR, alias, detail["str_version"])

                if not os.path.exists(tmp_src_dir):
                        print "version has error, please check. (%s)" % (detail["str_version"])

                        self.restore_app(ident, app_dir)

                        lock.unlock(tmp_lck)
                        lock.unlock(app_lck)

                        return False

                def export_src(src_dir, unit_src_dir, unit_out_dir):
                        if unit_src_dir == "":
                                return

                        full_src_dir = src_dir + "/" + unit_src_dir

                        if not os.path.exists(full_src_dir):
                                return

                        if not os.path.exists(unit_out_dir):
                                os.system("mkdir " + unit_out_dir)

                        os.system("cp -R %s/%s/* %s/" % (src_dir, unit_src_dir, unit_out_dir))

                export_src(tmp_src_dir, self.get_app_src_dir(),  out_app_dir2)
                export_src(tmp_src_dir, self.get_ebin_src_dir(), out_ebin_dir)
                export_src(tmp_src_dir, self.get_sql_src_dir(),  out_sql_dir)
                export_src(tmp_src_dir, self.get_sbin_src_dir(), out_sbin_dir)
                export_src(tmp_src_dir, self.get_log_src_dir(),  out_log_dir)
                export_src(tmp_src_dir, self.get_www_src_dir(),  out_www_dir)

                os.system("rm -rf %s" % (tmp_src_dir,))

                lock.unlock(tmp_lck)

                success = True

                try:
                        alter_db = False

                        if out_sql_dir != "" and os.path.exists(out_sql_dir):
                                os.chdir(out_sql_dir)

                                alter_db = self.modify_sql(ident, detail, do_update, old_version, new_version)

                        os.chdir(cwd)

                        os.chdir(out_app_dir2)
                        self.modify_app(ident, detail, new_version)
                        os.chdir(cwd)

                        os.chdir(out_sbin_dir)
                        self.modify_sbin(ident, detail, do_update)
                        os.chdir(cwd)

                        os.system("chmod a+x -R " + out_sbin_dir)

                        os.chdir(out_ebin_dir)
                        self.modify_ebin(ident, detail,
                                         do_update, alter_db,
                                         old_version, new_version)
                        os.chdir(cwd)

                        if out_www_dir != "" and os.path.exists(out_www_dir):
                                os.chdir(out_www_dir)
                                self.modify_www(ident, detail)
                                os.chdir(cwd)

                        if not do_update:
                                if out_sql_dir != "" and os.path.exists(out_sql_dir):
                                        #导入数据库
                                        os.chdir(out_sql_dir)
                                        self.import_db(detail)
                                        os.chdir(cwd)
                        elif os.path.exists(tmp_sql_dir):
                                #维护数据库
                                os.chdir(tmp_sql_dir)
                                self.backup_db(ident, detail, old_version)
                                os.chdir(cwd)

                                os.chdir(out_sql_dir)
                                try:
                                        #修改数据库结构
                                        if alter_db:
                                                self.alter_db(old_version, new_version, detail)

                                        #升级数据库内容
                                        self.upgrade_db(old_version, new_version, detail)

                                except:
                                        traceback.print_exc()

                                        os.chdir(tmp_sql_dir)
                                        self.restore_db(ident, detail, old_version)

                                        raise Exception("AlterDbError.")

                                os.chdir(cwd)

                        #写版本号
                        file.write_file(version_file, detail["str_version"])

                        print "install app %s_%s success." % (alias, ident)
                except:
                        success = False

                        traceback.print_exc()

                        os.chdir(cwd)

                        #还原备份
                        self.restore_app(ident, app_dir)

                        if do_update and os.path.exists(tmp_sql_dir):
                                os.system("cp -R %s/* %s/" % (tmp_sql_dir, out_sql_dir))

                if do_update:
                        if os.path.exists(tmp_sql_dir):
                                os.system("rm -rf %s" % (tmp_sql_dir,))

                        os.system("rm -rf %s" % (tmp_ebin_dir,))

                        os.system("rm -rf %s_bak" % (app_dir,))

                lock.unlock(app_lck)

                return success

        def db_name(self):
                return ""

        def db_bak_filename(self, ident, version):
                return "%s_%s_%s-%d.%d.%d.bak" % (self.db_name(), ident,
                                                  version[0], version[1], version[2], version[3])

        def backup_db(self, ident, detail, version):
                db_name = self.db_name() + "_" + str(ident)

                db_bak_filename = self.db_bak_filename(ident, version)

                os2.system("mysqldump -h%s -P%s -u%s -p%s --skip-lock-tables %s > %s" % \
                           (detail["db_host"], detail["db_port"],
                            detail["db_username"], detail["db_password"],
                            db_name, db_bak_filename))

                content = file.read_file(db_bak_filename)

                content = "USE `%s`;\n\n%s" % (db_name, content)

                file.write_file(db_bak_filename, content)

                print "backup db ok."

        def restore_db(self, ident, detail, version):
                db_bak_filename = self.db_bak_filename(ident, version)

                os2.system("mysql -h%s -P%s -u%s -p%s < %s" % \
                           (detail["db_host"], detail["db_port"],
                            detail["db_username"], detail["db_password"],
                            db_bak_filename))

                print "restore db ok."

        def modify_sql(self, ident, detail,
                             do_update,
                             old_version, new_version):
                return False

        def modify_app(self, ident, detail, version):
                pass

        def modify_sbin(self, ident, detail, do_update):
                pass

        def modify_ebin(self, ident, detail,
                              do_update, alter_db,
                              old_version, new_version):
                pass

        def modify_www(self, ident, detail):
                pass

        def import_db(self, detail):
                pass

        def alter_db(self, old_version, new_version, detail):
                pass

        def upgrade_db(self, old_version, new_version, detail):
                pass

        def db_key_host(self):
                return "db_host"

        def db_key_port(self):
                return "db_port"

        def db_key_username(self):
                return "db_username"

        def db_key_password(self):
                return "db_password"

        def db_key_name(self):
                return "db_name"

        def replace_db_config(self, config, detail):
                config = string2.str_repl("\{\s*%s,\s*\".+\"\s*\}" % (self.db_key_host(),), config,
                                          "{%s, \"%s\"}" % (self.db_key_host(), detail["db_host"]))
                config = string2.str_repl("\{\s*%s,\s*\d+\s*\}" % (self.db_key_port(),), config,
                                          "{%s, %d}" % (self.db_key_port(), detail["db_port"]))
                config = string2.str_repl("\{\s*%s,\s*\"\w+\"\s*\}" % (self.db_key_username(),), config,
                                          "{%s, \"%s\"}" % (self.db_key_username(), detail["db_username"]))
                config = string2.str_repl("\{\s*%s,\s*\"\w+\"\s*\}" % (self.db_key_password(),), config,
                                          "{%s, \"%s\"}" % (self.db_key_password(), detail["db_password"]))
                return string2.str_repl("\{\s*%s,\s*\"\w+\"\s*\}" % (self.db_key_name(),), config,
                                        "{%s, \"%s\"}" % (self.db_key_name(), detail["db_name"]))

        def replace_kernel_config(self, config, alias, version):
                kernel_log_filename = "%s%s_kernel.log" % (self.get_log_run_path(), alias)

                return string2.str_repl("\{\s*error_logger,\s*(?:[^\[\]\{\}]+|\{[^\[\]\{\}]+\})\}", config,
                                        "{error_logger, {file, \"%s\"}}" % (kernel_log_filename,))

        def replace_sasl_config(self, config, alias, version):
                sasl_log_filename = "%s%s_sasl.log" % (self.get_log_run_path(), alias)

                return string2.str_repl("\{\s*sasl_error_logger,.+\}", config,
                                        "{sasl_error_logger, {file, \"%s\"}}" % (sasl_log_filename,))
# -*- coding: utf8 -*-

from xml.dom import minidom
import gtools.util.file as file_util

class InfoPlist(object):
    info_plist = None

    path = None

    def __init__(self, path):
        super(InfoPlist, self).__init__()
        self.parse(path)

    def parse(self, path):
        self.path = path
        plist = minidom.parse(path)
        root = plist.getElementsByTagName("dict")[0]
        self.info_plist = self._parse_info_plist(root)

    def _parse_info_plist(self, root):
        info_plist = []

        info = None
        for node in root.childNodes:
            if node.nodeName == "#text":
                continue
            elif node.nodeName == "dict":
                value = {"type": "dict", "value": self._parse_info_plist(node)}
                info["value"] = value
                info_plist.append(info)
            elif node.nodeName == "array":
                value = {"type": "array", "value": self._parse_info_plist(node)}
                info["value"] = value
                info_plist.append(info)
            elif node.nodeName == "key":
                info = {"key": node.childNodes[0].nodeValue, "value": None}
            else:
                value = {"type": node.nodeName}
                if len(node.childNodes):
                    value["value"] = node.childNodes[0].nodeValue

                if info:
                    info["value"] = value
                    info_plist.append(info)
                else:
                    info_plist.append(value)

        return info_plist

    def set_info(self, key, type, value):
        for info in self.info_plist:
            if info["key"] == key:
                self.info_plist.remove(info)

        self.info_plist.append({"key": key, "value": self.format_value(type, value)})

    def format_value(self, type, value):
        if type == "bool":
            return {"type": value}
        else:
            return {"type": type, "value": value}

    def _gen_info_plist(self):
        xml = u'<?xml version="1.0" encoding="UTF-8"?>\n' \
              u'<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">\n' \
              u'<plist version="1.0">\n'
        xml += u'<dict>\n'

        xml += self._gen_plist_child(self.info_plist, u"\t")

        xml += u'</dict>\n'
        xml += u'</plist>\n'
        return xml


    def _gen_plist_child(self, info_plist, tag):
        xml = ""
        for info in info_plist:
            if "key" not in info:
                xml += tag + u'<%s>%s</%s>\n' % (info["type"], info["value"], info["type"])
            else:
                xml += tag + u'<key>%s</key>\n' % info["key"]
                value = info["value"]

                if "value" in value and value["value"]:
                    if value["type"] == "dict" or value["type"] == "array":
                        xml += tag + u'<%s>\n' % value["type"]
                        xml += self._gen_plist_child(value["value"], tag + u'\t')
                        xml += tag + u'</%s>\n' % value["type"]
                    else:
                        xml += tag + u'<%s>%s</%s>\n' % (value["type"], value["value"], value["type"])
                else:
                    xml += tag + u'<%s/>\n' % value["type"]
        return xml

    def save(self, path = None):
        if not path:
            path = self.path

        plist = self._gen_info_plist().encode('utf-8')
        file_util.write_file(path, plist)
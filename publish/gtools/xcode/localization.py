# -*- coding: utf8 -*-
# 本地化语言配置导出生成

import sys
import os
import gtools.conf.xls2xml as xls2xml
import gtools.util.file as file_util
import gtools.util.xml2 as xml
import gtools.util.os2 as os2
import pbx_project

def xls2strings(xls_file, out_dir, name):
    xls_dir = os.path.dirname(xls_file)
    file_name = os.path.splitext(os.path.basename(xls_file))[0]
    xls2xml.export_file(xls_file, xls_dir, xls_dir)

    for _, v in xml.toDict(xml.parse("%s/%s.conf" % (xls_dir, file_name)).getElementsByTagName(file_name + "s")[0])[file_name].items():
        str = ""
        lang = None
        for k in v.keys():
            k = k.encode("utf8")
            if k == "id" or k == "note":
                continue

            v2 = v[k].encode("utf8")
            if k == "language":
                lang = v2
                if not os.path.exists("%s/%s.lproj" % (out_dir, lang)):
                    os2.system_mkdir("%s/%s.lproj" % (out_dir, lang), True)
                continue

            str += '%s = "%s";\n' % (k, v2)

        if str and lang and os.path.exists("%s/%s.lproj" % (out_dir, lang)):
            file_util.write_file("%s/%s.lproj/%s.strings" % (out_dir, lang, name), str.encode("utf8"))
    os2.system_rm("%s/%s.conf" % (xls_dir, file_name))

def add_language(pbxproj_path, lang_path, group_path, rel_path, target_name = None):
    lang = os.path.splitext(os.path.basename(lang_path))[0]

    project = pbx_project.PBXProjectHelper(pbxproj_path + '/project.pbxproj')
    project.project.addRegion(lang)

    target = None

    if not target_name:
        target = project.project.targets[0]
    else:
        for t in project.project.targets:
            if t.getName() == target_name:
                target = t
                break

    group = project.project.mainGroup.find(group_path)

    for _, _, lang_files in os.walk(lang_path):
        for f in lang_files:
            group.addLocalizedFile("%s/%s.lproj/%s" % (rel_path, lang, f), lang, target)

    project.save()

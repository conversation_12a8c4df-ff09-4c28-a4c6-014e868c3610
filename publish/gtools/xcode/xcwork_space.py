# -*- coding: utf8 -*-

from xml.dom import minidom
import gtools.util.file as file_util

class XCWorkSpace(object):

    file_refs = None

    path = None

    def __init__(self, path):
        super(XCWorkSpace, self).__init__()
        self.parse(path)

    def parse(self, path):
        self.path = path

        self.file_refs = []

        xml = minidom.parse(path)
        refs = xml.getElementsByTagName("FileRef")
        for ref in refs:
            self.file_refs.append(ref.getAttribute('location'))

    def add_ref(self, path):
        ref = u'group:%s' % path
        if ref not in self.file_refs:
            self.file_refs.append(ref)

    def remove_ref(self, path):
        ref = u'group:%s' % path
        if ref not in self.file_refs:
            self.file_refs.remove(ref)

    def _gen_xml(self):
        xml = u'<?xml version="1.0" encoding="UTF-8"?>\n' \
              u'<Workspace version = "1.0">\n' \

        for ref in self.file_refs:
            xml += u'<FileRef location = "%s"></FileRef>\n' % ref

        xml += u'</Workspace>\n'
        return xml

    def save(self, path = None):
        if not path:
            path = self.path

        xml = self._gen_xml().encode('utf-8')
        file_util.write_file(path, xml)
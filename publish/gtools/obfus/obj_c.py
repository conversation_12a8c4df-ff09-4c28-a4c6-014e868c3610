# -*- coding: utf8 -*-
# objective-c代码混淆
from errno import ERANGE
import re
import os
import CSGString
import random
import string
import ast
import json
import CSGFile
import shutil
from pbxproj import *
from datetime import datetime
import gtools.obfus.obj_c_util as obj_c_util
import gtools.obfus.obfus_util as obfus_util
import gtools.util.file as file_util
import gtools.obfus.obfuscator as obfuscator
from pbxproj.pbxextensions import FileOptions


class Obj_C(obfuscator.Obfucator):
    xcodeproj_path = ""
    encrypt_type = 0
    
    # 线上配置参数字典
    config_params_dic = {}
    
    # 垃圾json对象集合
    totol_trash_list = []
    
    # 代码中增加的垃圾对象集合
    add_totol_trash_list = []

    disturb_funs = False  # 乱序函数
    split_classes = False  # 类切分
    garbage_code = False  # 添加垃圾代码
    
    restore_code_mode = False  # 代码还原模式
    restore_trash_config_path = None  #垃圾代码路径
    restore_obfuse_config_path = None #代码混淆词库路径
    restore_trash_code_class_list = []  # 垃圾代码配置数据集合
    restore_obfuse_code_pool = {}  # 还原混淆词库
    restore_trash_file_path = None # 还原垃圾代码路径
    
    restore_or_obfuse = False # 重新混淆，保持公开接口一样
    oc_append_trash_code_to_code = False #合并一个垃圾代码函数到一个正常的函数中，形成一个函数
    
    job_name = ''
    sdk_ver = ''
    
    obfuse_class_prefix = None
        
    # 拆分的字符串词库
    split_str_pool = {}
    
    # 收集宏名称
    define_str_pool = {}

    headers = {}
    classes = {}

    pchs = {}

    multi_classes = None
    
    

    add_code_file_num = 10
    add_code_file_fun_num = 5

    add_code_num_min = 3
    add_code_num_max = 10
    add_code_rate = 50
    
    add_attribute_min = 3
    add_attribute_max = 10

    code_file_target = None

    xibs = {}

    unclassfun = {} #非类文件方法 2019-12-28

    default_keyword = ["JSON.*", "NS.+", "UI.+", "name", "md5", "main", "AppDelegate", "viewWillAppear",
                       "ViewController", "init", "initWith.*", "show",
                       "setValue", "DEPRECATED_MSG_ATTRIBUTE", "dataTaskWithRequest", "appendData",
                       "valueForHTTPHeaderField",
                       "sharedManager",
                       "uploadTaskWithRequest", "uploadTaskWithStreamedRequest", "downloadTaskWithRequest",
                       "downloadTaskWithResumeData"]
    

    def parse_args(self, args):
        super(Obj_C, self).parse_args(args)
        self.xcodeproj_path = args.proj
        self.encrypt_type = args.encrypt
        self.code_file_target = args.code_file_target
        self.config_params_dic = ast.literal_eval(args.config_params_dic)
        if self.config_params_dic != None:
            print('get config datas')
            oc_class_number_min = self.config_params_dic.get("oc_class_number_min")
            oc_class_number_max = self.config_params_dic.get("oc_class_number_max")
            self.add_code_file_num = random.randint(oc_class_number_min, oc_class_number_max)
            
            oc_func_number_min = self.config_params_dic.get("oc_func_number_min")
            oc_func_number_max = self.config_params_dic.get("oc_func_number_max")
            self.add_code_file_fun_num = random.randint(oc_func_number_min, oc_func_number_max)
            
            self.add_code_num_min = self.config_params_dic.get("oc_func_number_min")
            self.add_code_num_max = self.config_params_dic.get("oc_func_number_max")
            
            self.add_code_rate = self.config_params_dic.get("oc_trash_use_rame")
            
            self.add_attribute_min = self.config_params_dic.get("oc_attr_number_min")
            self.add_attribute_max = self.config_params_dic.get("oc_attr_number_max")
            if self.config_params_dic.get("oc_append_trash_code_to_code")  == 1:
                self.oc_append_trash_code_to_code = True
        else:    
            if "code_file_num" in args:
                self.add_code_file_num = args.code_file_num
            if "code_num_min" in args:
                self.add_code_num_min = args.code_num_min
            if "code_num_max" in args:
                self.add_code_num_max = args.code_num_max
            if "code_rate" in args:
                self.add_code_rate = args.code_rate
            if "code_file_fun_num" in args:
                self.add_code_file_fun_num = args.code_file_fun_num
                
        
        if "code_file_target" in args:
            self.code_file_target = args.code_file_target
        if "code_restore_mode" in args and args.code_restore_mode == '1':
            self.restore_code_mode = True
        if "code_restore_trash_config" in args:
            self.restore_trash_config_path = args.code_restore_trash_config
        if "code_restore_obfuse_config" in args:
            self.restore_obfuse_config_path = args.code_restore_obfuse_config
        if "restore_trash_file_path" in args:
            self.restore_trash_file_path = args.restore_trash_file_path
        if "restore_or_obfuse" in args and args.restore_or_obfuse == '1':
            self.restore_or_obfuse = True
        if "job_name" in args:
            self.job_name = args.job_name
        if "sdk_ver" in args:
            self.sdk_ver = args.sdk_ver
            

    # 分解当前类文件成若干个对象
    def parse(self, path, file):
        
        if self.in_exclude_path(path):
            return
        if file.endswith(".h"):
            header = OC_Header(path, file)

            if '@implementation' not in header.content and '@interface' not in header.content:  # 收集非类文件中的方法
                list = OC_Header.find_funs(header.content)
                if len(list) > 0:
                    file_name = file.replace(".h", "")
                    self.unclassfun[file_name] = self.encrypt_str(file_name, 'fun')
                for fun_info in list:
                    if not self.is_exclude_keyword(fun_info[2]):
                        self.unclassfun[fun_info[2]] = self.encrypt_str(fun_info[2], 'fun')
                        
                # 匹配常量
                if  not self.in_exclude_path(header.path):
                    const_list = OC_Header.find_constant(header.content)
                    if len(const_list) > 0:
                        for item in const_list:
                            item = item.replace(' ', '')
                            pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
                            is_match = bool(re.match(pattern, item))
                            if is_match:
                                self.const_pool[item] = 'const'
                                
            if os.path.exists(header.dir + "/" + header.name + ".m"):
                header.parse(header.name + ".m")
            elif os.path.exists(header.dir + "/" + header.name + ".mm"):
                header.parse(header.name + ".mm")

            if self.split_classes:
                if not self.multi_classes:
                    self.multi_classes = {}

                if header.classes and len(header.classes) > 1:
                    if not self.in_exclude_path(header.dir):
                        print("multi-classes:%s" % header.name)
                        self.multi_classes.update(header.classes)

            self.headers[header.dir + "/" + header.name] = header

            if header.classes:
                self.classes.update(header.classes)

        elif file == 'main.m':
            header = OC_Header(path, file)
            self.headers[header.dir + "/" + header.name] = header
        elif file.endswith(".xib") or file.endswith(".storyboard"):
            xib = XibFile(path, file)
            xib.parse()
            self.xibs[xib.dir + "/" + xib.name] = xib
        elif file.endswith(".pch"):
            pch = PchFile(path, file)
            pch.parse()
            self.pchs[pch.dir + "/" + pch.name] = pch

    def gen_code_file(self, path, name, imports, clzs, prop_num, fun_num, rand_rang=[5, 10], letters=string.ascii_letters):
        obj_c_util.gen_code_file(path, name, imports, clzs, prop_num, fun_num, rand_rang, letters, self.gen_fun)

    def gen_fun(self, clz_name, fun_name=None, fun_type=None, mark=None, rand_rang=[5, 10], letters=string.ascii_letters,
                fun_temp=None):
        return obj_c_util.gen_fun(clz_name, fun_name, fun_type, mark, rand_rang, letters, fun_temp)

    def make_property_code(self):
        data_types = []

    # 还原代码
    def restore_code(self):
        # 加载配置文件的json数据
        if os.path.exists(self.restore_trash_config_path) and os.path.exists(self.restore_obfuse_config_path):
            trash_config_data = file_util.read_file(self.restore_trash_config_path)
            self.restore_trash_code_class_list = json.loads(trash_config_data)
            obfuse_config_data = file_util.read_file(self.restore_obfuse_config_path)
            self.restore_obfuse_code_pool = json.loads(obfuse_config_data)
        
    # 开始混淆
    def do_obfuscate(self, words_path=None):
        super(Obj_C, self).do_obfuscate(words_path)
        
        # 类前缀
        oc_class_head_upper = self.config_params_dic.get('oc_class_head_upper')
        if oc_class_head_upper == 1:
            self.obfuse_class_prefix = CSGString.generate_random_str().upper()
        elif oc_class_head_upper == 100:
            self.obfuse_class_prefix = ''
        # 如果是还原模式下就还原垃圾代码及垃圾文件
        if self.restore_code_mode:
            # 加载垃圾配置文件
            self.restore_code()
            
            if self.restore_or_obfuse == False:
                self.restore_trash_files()
                print("add restore trash file done")
        
        # 开始混淆
        print("start confuse code")
        self.obfu_strings()
        print("confuse code done")
        print("start add trash code files")
        used_str = self.str_pool.copy()
        used_str = used_str.update(self.str_pool2)
        
        
        # 还原模式下不生成垃圾代码
        if self.restore_code_mode and self.restore_or_obfuse == False:
            # 自身类加垃圾
            print("add trash code restoreing")
            self.restore_add_trash_code()
            print("add trash code restore done")
            
            
            print("restore code done")
            self.restore_trash_code_file()
            print("all restore done")
            return
        
        self.add_trash_code_file(used_str)
        print("add trash code files done")
        
        self.add_trash_code()
        print("add trash code done")
        
        self.append_trash_code_to_code()
        print("append trash code files done")
        
        # 添加文本 方便检测是否为混淆成功
        print('all confuse done')
        print(self.input_path)
        file_util.write_file(self.input_path + "/success.txt", "success")
        
        print('生成配置文件...')
        file_util.write_file(self.input_path + "/__obfuscator.json",
                             json.dumps(self.str_pool, indent=4, ensure_ascii=False))
                
        # 写入垃圾代码配置json文件
        file_util.write_file(self.input_path + "/__trash_config.json",
                             json.dumps(self.totol_trash_list, indent=4, ensure_ascii=False))
        
        # 写入增加自身混淆垃圾代码json映射表
        file_util.write_file(self.input_path + "/__add_code_trash_config.json",
                             json.dumps(self.add_totol_trash_list, indent=4, ensure_ascii=False))
        
        print('生成配置文件完成')
        
    
    
    # 还原垃圾代码文件到proj中
    def restore_trash_files(self):
        add_files = []
        for dir, _, files in os.walk(self.restore_trash_file_path):
            for f in files:
                if f.endswith('.h') or f.endswith('.m'):
                    header = OC_Header(dir, f)
                    header.parse(f)
                    add_files.append(header)
        project = XcodeProject.load(self.xcodeproj_path + '/project.pbxproj')
        group_base_name = os.path.basename(self.input_path) + "SDK"
        main_group = project.get_groups_by_name(group_base_name)
        project.get_or_create_group('ReFiles', path='ReFiles', parent=main_group[0])
        sub_group = project.get_groups_by_name('ReFiles')
        for f in add_files:
            r = project.add_file(f.file, parent=sub_group[0], tree=u'<group>', target_name=group_base_name)
            pass
        project.save()
    
    
    
    # 还原类中的垃圾代码
    def restore_trash_code_file(self):
        add_trash_classes = []
        trash_codes_list = []
        for dir, _, files in os.walk(self.input_path):
            for f in files:
                self.parse(dir, f)
        
        for clz in self.classes.values():
            old_fun_items = {} #添加参数处理垃圾方法未被调用的问题
            for item in self.restore_trash_code_class_list:
                if clz.name == item['belong_class']:
                    code_file = clz.header.code_file        
                    add_trash_classes = item['trash_classes']
                    trash_codes_list = item['trash_codes']
                
                    for fun_name, fun_content in code_file.funs.items():
                        old_fun_items[fun_name] = fun_content #new
                        sub_fun_name = fun_name.split(" ")[-1]
                        funObj = OC_Func()
                        funObj.content = fun_content
                        con_split = funObj.split_content()
                        for trash_code_item in trash_codes_list:
                            trash_code_line = '%s' % trash_code_item['trash_content']
                            if not trash_code_line.startswith('\tif (NSThread.currentThread.isMainThread) {'):
                                trash_code_line = "\n\tif (NSThread.currentThread.isMainThread) {%s} else {dispatch_async(dispatch_get_main_queue(), ^{%s});}" % (trash_code_line, trash_code_line)
                            trash_belong_func_name = trash_code_item['belong_func_name']
                            trash_func_line_num = trash_code_item['func_line_num']
                            if trash_belong_func_name == sub_fun_name:
                                for i in range(1, len(con_split) - 1):
                                    if i == trash_func_line_num - 1:
                                        con_split.insert(i, "\n%s" % trash_code_line)
                            pass
                        funObj.gen_content()
                        code_file.funs[fun_name] = funObj.content
                        pass
                    
                    template = code_file.get_template()
                    for fun_name, fun_content in code_file.funs.items():
                        con = old_fun_items[fun_name] #new
                        template = template.replace(con, fun_content) #new
                    for trash_file in add_trash_classes:
                        template = '#import "%s.h"\n' % trash_file + template
                    code_file.content = template
                    file_util.write_file(code_file.path, code_file.content)
                    
    def restore_add_trash_code(self):
        return     
                        
            

    def add_trash_code(self):
        oc_init_attr_in_ori_file = self.config_params_dic.get("oc_init_attr_in_ori_file")
        oc_init_func_in_ori_file = self.config_params_dic.get("oc_init_func_in_ori_file")
        if oc_init_func_in_ori_file == False:
            return
        
        self.classes = {}
        # 更新class
        for dir, _, files in os.walk(self.input_path):
            if self.in_exclude_path(dir):
                continue
            for f in files:
                self.parse(dir, f)
        
        
        # 加入垃圾代码
        for clz in self.classes.values():
            # 初始化增加的垃圾代码对象
            add_trash_object = OC_Add_Trash_Object()
            if self.in_exclude_path(clz.header.path):
                continue
            add_trash_object.belong_class = clz.name
            code_file = clz.header.code_file
            add_funs = []
            used_str = {}
            add_fun_names = []
            old_fun_items = {} #添加参数处理垃圾方法未被调用的问题
            for i in range(random.randint(self.add_code_num_min, self.add_code_num_max)):
                if self.encrypt_type == 2:
                    letters = []
                    letters.extend(self.en_lexicon["prefix"])
                    letters.extend(self.en_lexicon["postfix"])
                    fun = self.gen_fun(clz.name, mark=used_str, rand_rang=[2, 4], letters=letters)
                else:
                    fun = self.gen_fun(clz.name, mark=used_str)
                    
                if fun.name not in add_fun_names:  # 过滤同名方法
                    add_funs.append(fun)
                    add_fun_names.append(fun.name)
                    self.append_defined_str(fun.name)  # 垃圾方法名存入屏蔽字库 不做混淆
                    if fun.name not in self.exclude_keyword:
                        self.exclude_keyword.append(fun.name)

            if oc_init_attr_in_ori_file == False:
                continue
            
            # 增加垃圾属性
            add_property = []
            add_property_names = []
            #property_prefix = CSGString.generate_random_str(2)
            property_types = ['NSString','NSAttributedString','NSParagraphStyle','NSObject','NSData','NSDate','NSMutableSet',
                              'NSSet','NSMutableDictionary','NSTimer','NSArray','NSData','NSMutableArray','NSDictionary']
            attribute_verbs = ['Variable', 'Constant', 'Attribute', 'Parameter', 'Data', 'Value', 
                               'Array', 'List', 'Dictionary','Statement', 'Module', 'Library', 
                               'Interface', 'Comment', 'Handling', 'Slider','PickerView','SegmentedControl',
                               'SearchBar','DatePicker','ActivityIndicatorView','PageControl','TextView',
                               'TextField','Button','View','Label','ImageView','TableView','TableViewCell',
                               'String','AttributedString','ParagraphStyle','Object','Data',
                               'Date','MutableSet','Set','MutableDictionary','Timer','MutableArray','Dictionary']
            for i in range(random.randint(self.add_attribute_min, self.add_attribute_max)):
                property_type = CSGString.getRandomStr(property_types)
                oc_attr_name_ab = self.config_params_dic.get("oc_attr_name_ab")
                property_value = ""
                last_word= random.choice(attribute_verbs)
                if oc_attr_name_ab:
                    word_A = random.choice(self.en_lexicon["prefix"]).lower()
                    word_B = random.choice(self.en_lexicon["prefix"]).capitalize()
                    property_value = word_A + word_B + last_word
                else:
                    property_value = random.choice(self.en_lexicon["prefix"]).lower() + last_word
                add_value = "@property (nonatomic, strong) %s *%s" % (property_type, property_value)
                # 过滤同名方法
                if property_value not in add_property_names:
                    add_property.append(add_value)
                    add_property_names.append(property_value)
                    # 垃圾属性加入屏蔽字库，不做混淆
                    if property_value not in self.exclude_keyword:
                        self.exclude_keyword.append(property_value)
            
            
                        
            flag = False
            # .h 文件增加垃圾属性和垃圾方法声明
            add_interfaces = []
            trash_fun_list = []
            attribute_list = []
            for interface in clz.header.interfaces.values():
                if interface.name == clz.name:
                    content = interface.content.replace("@end", "")
                    add_interface = OC_Add_Trash_Interface()
                    add_interface.name = interface.name
                    # 加垃圾属性
                    for item in add_property:
                        interface.funs[item] = item
                        content += "\n%s;" % item
                        sub_item = "\n%s;" % item
                        attribute_list.append(sub_item)
                    
                    #加垃圾方法
                    for f in add_funs:
                        interface.funs[f.fun_key] = f
                        content += "\n%s;" % f.name
                        sub_content = "\n%s;" % f.name
                        trash_fun_list.append(sub_content)
                        if f.name not in self.defined_str:
                            self.append_defined_str(f.name)
                        if f.name not in self.exclude_keyword:
                            self.exclude_keyword.append(f.name)
                    add_interface.trash_fun_list = trash_fun_list
                    add_interface.proprety_list = attribute_list
                    interface.content = content + "\n@end"
                    flag = True
                    add_interfaces.append(add_interface.__dict__)
                    break
            
            
            if not flag:
                continue
            
            trash_body_fun_list = []
            add_trash_object.header_interfaces = add_interfaces
            for fun in add_funs:
                sub_content = "\n" + fun.content
                trash_body_fun_list.append(sub_content)
                
            add_trash_object.code_file_codes = trash_body_fun_list
            
            clz.funs.extend(add_funs)
            # .m 文件增加垃圾方法实现代码
            
            for fun_name, fun_content in code_file.funs.items():
                
                old_fun_items[fun_name] = fun_content #new
                clz_name = fun_name.split(" ")[0]

                if clz_name != clz.name:
                    continue
                
                if len(clz.header.interfaces) >= 2:
                    print('屏蔽的类：%s' % clz.name)
                    continue

                # 如果不是OC方法则不插入垃圾代码调用
                oc_funs = OC_Header.find_funs(fun_content)
                if len(oc_funs) == 0 or oc_funs[0][0] != "-":
                    continue
                
                if 'uncaughtExceptionHandler' in fun_content or 'formatLogWithExceArray' in fun_content or 'DiskCacheFileNameForKey' in fun_content or 'formatDateTime' in fun_content:
                    continue
                
                
                funObj = OC_Func()
                funObj.content = fun_content
                con_split = funObj.split_content()
                for i in range(1, len(con_split) - 1):
                    random_index = random.randint(0, 100)
                    if random_index <= self.add_code_rate and funObj.content.startswith('-'):
                        add_fun = random.choice(add_funs)
                        fun_center_name = add_fun.name.split(')')[-1]
                        trash_code_body = "\n\tif (NSThread.currentThread.isMainThread) {[self %s];} else {dispatch_async(dispatch_get_main_queue(), ^{[self %s];});}" % (fun_center_name, fun_center_name)
                        con_split.insert(i, trash_code_body)
                funObj.gen_content()
                code_file.funs[fun_name] = funObj.content
                pass
            template = code_file.get_template()
            for fun_name, fun_content in code_file.funs.items():
                con = old_fun_items[fun_name] #new
                template = template.replace(con, fun_content) #new
            
            content = re.match(r'.*@implementation(.*)@end', template, re.M | re.I | re.S).group(1)
            template = template.replace(content, "__{{@temp_content}}__")
            
            # .m文件开始增加垃圾代码实现
            if len(clz.header.interfaces) == 1:
                for fun in add_funs:
                    content += "\n" + fun.content
                    code_file.funs[fun.fun_key] = fun.content

            template = template.replace("__{{@temp_content}}__", content)
            
            # 更新.h文件
            clz.header.update_content()
            code_file.update_content()
            
            # 更新.m文件
            code_file.content = template
            code_file.update_template()

            file_util.write_file(clz.header.path, clz.header.content)
            file_util.write_file(code_file.path, code_file.content)
            
        
            self.add_totol_trash_list.append(add_trash_object.__dict__)
        
    # 合并垃圾函数中的代码到一个正常的函数中            
    def append_trash_code_to_code(self):
        if self.oc_append_trash_code_to_code == False:
            print('no append trash code to code')
            return
        
        if self.add_code_file_num == 0:
            print('no add trash file')
            return
        
        # 先生成垃圾代码
        add_files = []
        tmp_trash_path = self.input_path + '/tmp_trash_files'
        for i in range(self.add_code_file_num):
            if self.encrypt_type == 2:
                letters = []
                letters.extend(self.en_lexicon["prefix"])
                letters.extend(self.en_lexicon["postfix"])
                rand_rang = [2, 4]
            else:
                letters = string.ascii_letters
                rand_rang = [3, 7]

            name = self.encrypt_str('', 'class')
            classArray = ['NSString', 'NSArray', 'NSDictionary', 'NSData']
            self.gen_code_file(tmp_trash_path, name, ["<Foundation/Foundation.h>","<UIKit/UIKit.h>"], classArray, random.randint(0, 10), self.add_code_file_fun_num, rand_rang, letters)
            header = OC_Header(tmp_trash_path, name + ".h")
            header.parse(header.name + ".m")

            add_files.append(header)
        pass
        
        append_trash_funcs = []
        for header in add_files:
            trash_code_file = header.code_file
            for fun_name, fun_content in trash_code_file.funs.items():
                funObj = OC_Func()
                funObj.name = fun_name.split(" ")[-1]
                funObj.content = fun_content
                funObj.gen_content()
                append_trash_funcs.append(funObj)
        
        old_fun_items = {}
        
        
        # 更新class
        self.classes = {}
        for dir, _, files in os.walk(self.input_path):
            if self.in_exclude_path(dir):
                continue
            for f in files:
                self.parse(dir, f)
        
        
        # 合并垃圾代码到一个正常的函数中
        for clz in self.classes.values():
            if self.in_exclude_path(clz.header.path):
                continue
            if 'HWPay' in clz.header.path or 'MiddleComponent' in clz.header.path:
                continue
            # .m文件中的body
            code_file = clz.header.code_file
            for fun_name, fun_content in code_file.funs.items():
                # 屏蔽方法不加垃圾
                sub_fun_name = fun_name.split(" ")[-1]
                if self.is_exclude_keyword(sub_fun_name):
                    continue
                old_fun_items[sub_fun_name] = fun_content #new
                funObj = OC_Func()
                funObj.content = fun_content
                code_con_split = funObj.split_content()
                if len(code_con_split) < 3:
                    continue
                
                # 随机选择一个垃圾函数
                trash_func_obj = random.choice(append_trash_funcs)
                if trash_func_obj.name == "sharedInstance":
                    continue
                trash_con_split = trash_func_obj.split_content()[:-1]
                if len(trash_con_split) < 3:
                    continue
                
                result = []
                index = 0
                while index < len(code_con_split):
                    if index == 0:
                        first_line = code_con_split[index]
                        matches = re.findall('{', first_line)
                        if len(matches) > 1:
                            result = code_con_split
                            break
                        else:
                            first_trash_code = trash_con_split[index].split('{')[-1]
                            result.insert(index, code_con_split[index])
                            result.append(first_trash_code)
                    elif index == len(code_con_split) - 1:
                        result.append(code_con_split[index])
                    else:
                        if index < len(trash_con_split):
                            result.append(trash_con_split[index])
                            result.append(code_con_split[index])
                        else:
                            result.append(code_con_split[index])
                    index += 1
                
                funObj.content_split = result
                # 合并垃圾代码
                funObj.gen_content()
                code_file.funs[fun_name] = funObj.content
                
            template = code_file.get_template()
            for fun_name, fun_content in code_file.funs.items():
                sub_fun_name = fun_name.split(" ")[-1]
                con = old_fun_items.get(sub_fun_name) #new
                if con != None:
                  template = template.replace(con, fun_content) #new
            code_file.content = template
            file_util.write_file(code_file.path, code_file.content)
    
                    

    def add_trash_code_file(self, used_str):
        if self.add_code_file_num == 0:
            print('no add trash file')
            self.input_path = self.input_path + '/' + self.code_file_target + 'SDK'
            return
        if used_str is None:
            used_str = {}
        classArray = ['NSString', 'NSArray', 'NSDictionary', 'NSData']

        add_files = []
        self.input_path = self.input_path + '/' + self.code_file_target + 'SDK'
        tmp_trash_path = self.input_path + '/ReFiles'
        trash_file_list = []
        CSGFile.get_files_in_dir(trash_file_list, tmp_trash_path)
        if len(trash_file_list) > 0:
            shutil.rmtree(tmp_trash_path)
            os.mkdir(tmp_trash_path)
        for i in range(self.add_code_file_num):
            path = self.input_path + '/ReFiles'
            if self.encrypt_type == 2:
                letters = []
                letters.extend(self.en_lexicon["prefix"])
                letters.extend(self.en_lexicon["postfix"])
                rand_rang = [2, 4]
            else:
                letters = string.ascii_letters
                rand_rang = [3, 7]

            name = self.encrypt_str('', 'class')
            self.gen_code_file(path, name, ["<Foundation/Foundation.h>","<UIKit/UIKit.h>"], classArray, random.randint(0, 10), self.add_code_file_fun_num, rand_rang, letters)
            header = OC_Header(path, name + ".h")
            header.parse(header.name + ".m")

            add_files.append(header)
        pass

        project = XcodeProject.load(self.xcodeproj_path + '/project.pbxproj')
        group_base_name = os.path.basename(self.input_path)
        main_group = project.get_groups_by_name(group_base_name)
        project.get_or_create_group('ReFiles', path='ReFiles', parent=main_group[0])
        sub_group = project.get_groups_by_name('ReFiles')
        for f in add_files:
            r = project.add_file(f.file, parent=sub_group[0], tree=u'<group>', target_name=group_base_name)
            r = project.add_file(f.code_file.file, parent=sub_group[0], tree=u'<group>', target_name=group_base_name)
            pass
        project.save()
        
        # 更新class
        self.classes = {}
        for dir, _, files in os.walk(self.input_path):
            if self.in_exclude_path(dir):
                continue
            for f in files:
                self.parse(dir, f)
        
        # 在代码中调用
        used_files = {}
        for clz in self.classes.values():
            # 声明垃圾代码对象，并赋值垃圾代码所属类名  2023-4-24
            if  clz.name not in self.str_pool.values():
                continue
            
            trash_obj = OC_Trash()
            trash_obj.belong_class = clz.name
            if self.in_exclude_path(clz.header.path):
                continue
            code_file = clz.header.code_file

            if code_file.path in used_files:
                continue

            used_files[code_file.path] = code_file

            trash_files = random.sample(add_files, random.randint(1, len(add_files)))

            old_fun_items = {} #添加参数处理垃圾方法未被调用的问题
            trash_classes_list = []
            trash_codes = []
            for fun_name, fun_content in code_file.funs.items():
                # 屏蔽方法不加垃圾
                sub_fun_name = fun_name.split(" ")[-1]
                if sub_fun_name in self.exclude_keyword:
                    continue
                old_fun_items[sub_fun_name] = fun_content #new
                funObj = OC_Func()
                funObj.content = fun_content
                con_split = funObj.split_content()
                for i in range(1, len(con_split) - 1):
                    if random.randint(0, 100) <= self.add_code_rate:
                        trash_file = random.choice(trash_files)
                        fun = random.choice(trash_file.classes[trash_file.name].funs)
                        
                        
                        if fun.name == "sharedInstance":
                            continue
                        if fun.name in self.exclude_keyword:
                            continue
                        fun_args = fun.fun_key.split(' ')[-1]
                        # trash_code_body = "\n\tif (NSThread.currentThread.isMainThread) {[[%s sharedInstance] %s];} else {dispatch_async(dispatch_get_main_queue(), ^{[[%s sharedInstance] %s];});}" % (trash_file.name, fun_args, trash_file.name, fun_args)
                        # trash_code_body = "\n\tdispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{ [[%s sharedInstance] %s]; });" % (trash_file.name, fun_args)
                        trash_code_body = "\n\t[[%s sharedInstance] %s];" % (trash_file.name, fun_args)
                        con_split.insert(i, trash_code_body)
                        # trash_code_content = "\tif (NSThread.currentThread.isMainThread) {[[%s sharedInstance] %s];} else {dispatch_async(dispatch_get_main_queue(), ^{[[%s sharedInstance] %s];});}" % (trash_file.name, fun_args, trash_file.name, fun_args)
                        trash_code_content = "\t[[%s sharedInstance] %s];" % (trash_file.name, fun_args)


                        temp_obj = {}
                        temp_obj['trash_content'] = trash_code_content
                        temp_obj['belong_func_name'] = fun_name.split(' ')[-1]
                        temp_obj['func_line_num'] = i + 1
                        trash_codes.append(temp_obj)
                funObj.gen_content()
                code_file.funs[fun_name] = funObj.content
                pass
                
            template = code_file.get_template()
            for fun_name, fun_content in code_file.funs.items():
                sub_fun_name = fun_name.split(" ")[-1]
                con = old_fun_items.get(sub_fun_name) #new
                if con != None:
                  template = template.replace(con, fun_content) #new

            for trash_file in trash_files:
                template = '#import "%s.h"\n' % trash_file.name + template
                # 收集一个类中的垃圾代码类名  2023-4-24
                trash_classes_list.append(trash_file.name)
            code_file.content = template

            file_util.write_file(code_file.path, code_file.content)
            
            #排序
            def sort_by_line_num(d):
                return d.get("func_line_num")
        
            # 根据line_num进行排序
            sort_trash_codes = sorted(trash_codes, key=sort_by_line_num)
            
            
            
            trash_obj.trash_codes = sort_trash_codes
            trash_obj.trash_classes = list(set(trash_classes_list))
            if len(trash_obj.trash_codes) > 0 and len(trash_obj.trash_classes) > 0 :
                self.totol_trash_list.append(trash_obj.__dict__)
    # 收集变量
    def collect_preperty(self, clz):
        #收集.h文件中的属性
        first_attrubuit_list = []
        for interface in clz.header.interfaces.values():
                if 'Model' in clz.header.path or 'Common' in clz.header.path:
                    continue
                if interface.name == clz.name:
                    attrubuit_list = re.findall(r'@property(.*?);', interface.content)
                    first_attrubuit_list = list(set(attrubuit_list))
        
        # 收集.m文件中的属性
        sum_list = re.findall(r'@property(.*?);', clz.header.code_file.content)
        new_list = first_attrubuit_list + list(set(sum_list))  # 数组去重

        for item in new_list:
            str = ''
            property_type = ''
            is_contain_type = False
            for key in self.elem_type:
                if key in item:
                    str = item.split(key)[-1]
                    property_type = key
                    str = str.replace("*", '').replace(' ', '')
                    is_contain_type = True
            if is_contain_type == False:
                if '*' in item:
                    str = item.split('*')[-1]
                    property_type = "unknow"
                else:
                    str = item.split(' ')[-1]
                    property_type = "unknow"
            # python 判断字符串非全字母
            pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
            is_contain = bool(re.match(pattern, str))
            if not is_contain:
                continue
            if len(str)<4:
                continue
            if str in self.defined_str:
                self.defined_str.remove(str)
            
            if not self.is_exclude_keyword(str):
                self.change_str[str] = property_type
                # 兼容成员变量
                self.change_str["_" + str] = property_type
    
    #  收集私有方法名
    def collect_private_func(self ,clz):
        for str_fun in clz.header.code_file.funs:
            fun_name = str_fun.split(" ")[1]  # 字符串切割
            fun_name = fun_name.split(":")[0]  # 字符串切割
            fun_name = fun_name.replace("\n", '')
            fun_name = fun_name.replace("//", '')
            fun_name = fun_name.replace("-", '')
            if fun_name in self.defined_str:
                self.defined_str.remove(fun_name)
            if not self.is_exclude_keyword(fun_name):
                self.fun_pool[fun_name] = fun_name
    
    # 收集.m文件中的@"xxx"字符
    def collect_single_str(self, content, class_name):
        if class_name == "Macro":
            pattern = r'@"[\w\d\u4e00-\u9fa5./:%@\-#$!]+"'
            matches = re.findall(pattern, content)
            avoid_list = []
            tmp_sdk_ver = "@\"%s\"" % self.sdk_ver
            avoid_list.append(tmp_sdk_ver)
            for item in matches:
                if len(item) >= 3 and item not in avoid_list:
                    self.split_str_pool[item] = item
                else:
                    print('avoid str:' + item)
            
                
            
    # 收集宏名称
    def collect_define_str(self, content):
        pattern = r'#define\s+(\w+)'
        matches = re.findall(pattern, content)
        for item in matches:
            if not self.is_exclude_keyword(item):
                self.define_str_pool[item] = item
            else:
                print('avoid define str:' + item)
            
            
    # 收集类别名称
    def collect_category_str(self, header_class):
        class_name = header_class.name
        if '+' in class_name:
            if not self.is_exclude_keyword(class_name):
                self.define_str_pool[class_name] = class_name
    

    # 混淆代码，类名、方法名、属性名
    def obfu_strings(self):
        # 收集字符串池
        starttime = datetime.now()
        print("collect strings")
        self.fun_pool = {}
        self.change_str = {}
        
        for head_class in self.headers.values():
            if self.in_exclude_path(head_class.path):
                # 需要把限制的类的关键字添加到排除关键字列表
                if head_class.name not in self.exclude_keyword:
                    self.exclude_keyword.append(head_class.name)
                continue
            
            if head_class.name in self.defined_str:
                self.defined_str.remove(head_class.name)

            if not self.is_exclude_keyword(head_class.name):
                self.str_pool[head_class.name] = head_class.name
                
            # 收集字符串名
            self.collect_single_str(head_class.content, head_class.name)
            # 收集宏名称
            self.collect_define_str(head_class.content)
            # 收集类别名称
            self.collect_category_str(head_class)
        
        
        for clz in self.classes.values():
            if self.in_exclude_path(clz.header.path):
                # 需要把限制的类的关键字添加到排除关键字列表
                if clz.name not in self.exclude_keyword:
                    self.exclude_keyword.append(clz.name)
                for fun in clz.funs:
                    if fun.name not in self.exclude_keyword:
                        self.exclude_keyword.append(fun.name)
                continue
            
            if clz.name in self.defined_str:
                self.defined_str.remove(clz.name)

            if not self.is_exclude_keyword(clz.name):
                self.str_pool[clz.name] = clz.name

            # 收集属性名
            self.collect_preperty(clz)
            # 收集私有方法名
            self.collect_private_func(clz)
            # 收集字符串名
            self.collect_single_str(clz.header.code_file.content, clz.name)
            
        pass
        endtime = datetime.now()
        print(f"collect strings总共花了{(endtime - starttime).seconds}秒")
        
        # 开始混淆字符串池
        print("obfuse strings")
        obfuse_time = datetime.now()
        
        self.str_pool2 = {}
        for k in self.str_pool.keys():
            if "+" in k:
                first_str = self.encrypt_str(k, 'class')
                while first_str in self.str_pool2:
                    first_str = self.encrypt_str(k, 'class')
                last_str = self.encrypt_str(k, 'class')
                v = first_str + "+" + last_str
            else:
                v = self.encrypt_str(k, 'class')
            while v in self.str_pool2:
                v = self.encrypt_str(k, 'class')

            self.str_pool2[v] = k
            self.str_pool[k] = v
        for k in self.fun_pool.keys():
            v = self.encrypt_str(k, 'fun')
            while v in self.str_pool2:
                v = self.encrypt_str(k, 'fun')

            self.str_pool2[v] = k
            self.str_pool[k] = v

        # # ////start 变量名
        # for k, v1 in self.change_str.items():
        #     v = self.encrypt_str(k, 'attribute')
        #     while v in self.str_pool2:
        #         v = self.encrypt_str(k, 'attribute')
        #     v = v.lower() + CSGString.getDex(v1)
        #     self.str_pool2[v] = k
        #     self.str_pool[k] = v


        # 变量名混淆
        property_setter_mapping = {}  # 用于存储属性名和setter方法名的映射关系
        for k, v1 in self.change_str.items():
            # 获取不带下划线的key
            base_key = k[1:] if k.startswith('_') else k
            
            # 如果已经处理过这个base_key，直接使用已有的混淆名称
            if base_key in self.str_pool:
                v = self.str_pool[base_key]
            else:
                # 生成新的混淆名称
                v = self.encrypt_str(base_key, 'attribute')
                while v in self.str_pool2:
                    v = self.encrypt_str(base_key, 'attribute')
                self.str_pool2[v] = base_key
                self.str_pool[base_key] = v
            
            # 同时设置带下划线和不带下划线的映射
            if k.startswith('_'):
                self.str_pool[k] = "_" + v
            else:
                self.str_pool[k] = v
                
            # 存储属性名和setter方法名的映射关系
            property_setter_mapping[base_key] = v
            
        # 处理setter方法名映射
        for base_key, v in property_setter_mapping.items():
            setter_key = "set" + base_key[0].upper() + base_key[1:]
            setter_value = "set" + v[0].upper() + v[1:]
            self.str_pool[setter_key] = setter_value
            self.str_pool2[setter_value] = setter_key

        
            
        #  常量名
        for k, v1 in self.const_pool.items():
            v = self.encrypt_str(k, 'const')
            while v in self.str_pool2:
                v = self.encrypt_str(k, 'const')
            v = v.lower() + CSGString.getDex(v1)
            self.str_pool2[v] = k
            self.str_pool[k] = v
        
        # 拆分的字符串
        for k, v1 in self.split_str_pool.items():
            bundle_str = self.code_file_target + "SDKBundle"
            if bundle_str in k:
                continue
            v = self.encrypt_str(k, 'split_str')
            self.str_pool[k] = v
            
        # 宏名称&类别名称
        for k, v1 in self.define_str_pool.items():
            if '+' in k:
                first_class_name = k.split('+')[0]
                last_class_name = k.split('+')[-1]
                
                if self.is_exclude_keyword(last_class_name):
                    continue
                
                if first_class_name not in self.elem_type:
                    if self.str_pool.get(first_class_name) is None:  
                        new_first_class_name = self.encrypt_str(k, 'class')
                        self.str_pool[first_class_name] = new_first_class_name
                        first_class_name = new_first_class_name
                    else:
                        first_class_name = self.str_pool[first_class_name]
                        
                if self.str_pool.get(last_class_name) is None:
                    new_class_name = self.encrypt_str(k, 'class')
                    self.str_pool[last_class_name] = new_class_name
                    last_class_name = new_class_name
                else:
                    last_class_name = self.str_pool[last_class_name]
                    
                v = first_class_name + '+' + last_class_name
                self.str_pool[k] = v
            else:
                v = self.encrypt_str(k, 'define')
                while v in self.str_pool2:
                    v = self.encrypt_str(k, 'define')
                # v = v.upper() + CSGString.getDex(v1)
                self.str_pool2[v] = k
                self.str_pool[k] = v

            
            
        
        # ////end

        # ////非类文件
        if len(self.unclassfun) > 0:
            for key in self.unclassfun:
                self.str_pool[key] = self.unclassfun[key]

        xcode_proj = file_util.read_file(self.xcodeproj_path + "/project.pbxproj")

        if self.append_str_pool:
            self.str_pool.update(self.append_str_pool)
        
        if self.restore_code_mode:
            print('start restore code...')
            
            # 判断是否需要重新还原
            if self.restore_or_obfuse:
                print('---重新混淆但保持头文件不变---')
                new_restore_obfuse_code_pool = {}
                # 国内SDKdemo中要替换的字符
                inside_public_api_names = [
                    'GNPublicGameApi','SDPublicGameApi','shareGameSDKApi','application','applicationDidBecomeActive',
                    'logWithEnable','initializeSDKWithGameID','INFORMATIONLANDLORD_LOGIN_RESULT','INFORMATIONLANDLORD_LOGOUT_RESULT',
                    'INFORMATIONLANDLORD_SERVER_STATUS','showGameSDKUI','setupServerId','settingRoleName','settingRoleID',
                    'setttingServerName','setttingServerType','settingRoleLevel','submitPlayerRoleInfo','activedPlayerGameActivity',
                    'makeDealWithAppStore','logoutSDKFromGame','updateBehaviorCap','reportPayCdkey','BehaviorEvent','upgradeRoleInfo']
                outside_public_api_names = [
                    'MFSGameCore','showComment','MFSGameParam','runAwayStripmine','shakingVpo','gameID','gameName',
                    'jumpDownBronx','application','matiealingMesantoin','showLoginUI','MFSReportRoleInfo','server_name','role_id',
                    'role_name','role_level''role_vip','role_first_money','role_total_money','role_power','create_role_time',
                    'cp_timestamp','_server_id','makeitTelecommerce','warmningLyf','quitingDispersal','flySeventhlarge',
                    'QetrieRassSDKLoginNoticeKey','QetrieRassSDKLogoutNoticeKey', 'QetrieRassSDKRewardedAdNoticeKey',
                    'sdkRegionType','MFSGameParamTypeOverseas','sh_server','showLog','serverID','goAwayLocky','flitpNightglass',
                    'goTogetherLowwage','showMFSRewardAD']
                restore_apis = []
                if self.job_name == 'iOS_vTD':
                    restore_apis = outside_public_api_names
                else:
                    restore_apis = inside_public_api_names
                
                for k, v in self.restore_obfuse_code_pool.items():
                    if k in restore_apis:
                        new_restore_obfuse_code_pool[k] = v
                self.restore_obfuse_code_pool = new_restore_obfuse_code_pool
            else:
                print('---重新混淆----')
            for k, v in self.restore_obfuse_code_pool.items():
                for k1, v1 in self.str_pool.items():
                    if k == k1:
                        self.str_pool[k1] = v

                
        # 开始替换字符串
        print("replace strings")
        for header in self.headers.values():
            if self.in_exclude_path(header.path):
                # 需要把限制的类的关键字添加到排除关键字列表
                if header.name not in self.exclude_keyword:
                    self.exclude_keyword.append(header.name)
                print('屏蔽的类：%s' % header.name)
                continue
            
            for k, v in self.str_pool.items():
                changed, header.content = self.replace_str(header.content, k, v)
                if changed:
                    file_util.write_file(header.path, header.content)

                if header.code_file:
                    if header.code_file.replace_str(k, v):
                        header.code_file.update_content()
                        file_util.write_file(header.code_file.path, header.code_file.content)
                        
                if header.split_code_files:
                    for f in header.split_code_files:
                        changed, f.content = self.replace_str(f.content, k, v)
                        if changed:
                            f.update_template()
                            file_util.write_file(f.path, f.content)

            # 替换文件名
            # 支持类似 CSHttpUtils+Support.h 这样的命名文件
            change_name = False
            header_names = header.name.split("+")
            for i in range(0, len(header_names)):
                if header_names[i] in self.str_pool:
                    change_name = True
                    header_names[i] = self.str_pool.get(header_names[i])

            if change_name:
                new_name = "+".join(header_names)

                # 替换project.pbxproj中文件记录
                xcode_proj = xcode_proj.replace(header.file, new_name + header.ext)
                if header.code_file:
                    xcode_proj = xcode_proj.replace(header.code_file.file,
                                                    new_name + header.code_file.ext)

                # 重命名文件
                header.rename(new_name)
                if header.code_file:
                    header.code_file.rename(new_name)
                if header.split_code_files:
                    for f in header.split_code_files:
                        new_name = self.str_pool.get(f.name)
                        if new_name:
                            f.rename(new_name)
        for xib in self.xibs.values():
            content = xib.content
            for clz in xib.classes:
                if clz in self.str_pool:
                    content = content.replace('customClass="' + clz + '"',
                                              'customClass="' + self.str_pool[clz] + '"')
            for selector in xib.selectors:
                if selector in self.str_pool:
                    content = content.replace('selector="' + selector + ':"',
                                              'selector="' + self.str_pool[selector] + ':"')

            file_util.write_file(xib.path, content)

            if xib.name in self.str_pool:
                new_name = self.str_pool.get(xib.name)

                # 替换project.pbxproj中文件记录
                xcode_proj = xcode_proj.replace(xib.file, new_name + xib.ext)

                # 重命名文件
                xib.rename(new_name)

        for pch in self.pchs.values():
            content = pch.content
            for i in pch.imports:
                if i in self.str_pool:
                    content = content.replace(i + ".h", self.str_pool[i] + ".h")
            file_util.write_file(pch.path, content)

            if pch.name in self.str_pool:
                pch.rename(self.str_pool.get(pch.name))

        file_util.write_file(self.xcodeproj_path + "/project.pbxproj", xcode_proj)
        print("code obfuse done!")
        end_obfuse_time = datetime.now()
        print(f"replace strings总共花了{(end_obfuse_time - obfuse_time).seconds}秒")



    def add_frameworks(self):
        first_header = "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/"
        last_layer = first_header + "JavaScriptCore.framework"
        last_layer1 = first_header + "AdSupport.framework"
        last_layer2 = first_header + "CoreServices.framework"
        last_layer3 = first_header + "OpenAL.framework"
        last_layer4 = first_header + "CoreLocation.framework"
        last_layer5 = first_header + "CoreGraphics.framework"
        last_layer6 = first_header + "OpenGLES.framework"
        last_layer7 = first_header + "libz.tbd"
        array_list = []
        array_list.append(last_layer)
        array_list.append(last_layer1)
        array_list.append(last_layer2)
        array_list.append(last_layer3)
        array_list.append(last_layer4)
        array_list.append(last_layer5)
        array_list.append(last_layer6)
        array_list.append(last_layer7)
        project = XcodeProject.load(self.xcodeproj_path + '/project.pbxproj')
        project.get_or_create_group("Frameworks")
        group = project.get_groups_by_name("Frameworks")
        options = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False)
        for i in array_list:
            r = project.add_file(i, parent=group[0], tree=u'SDKROOT', force=False, target_name=self.code_file_target, file_options=options)
        project.save()

        print("add framework done")
        


class OC_Header(obfuscator.CFile):
    @staticmethod
    def find_funs(str):
        return re.findall(r'(?:^|\n)([\-|\+]) *\(([^\(\)]+)\) *([^\:\(\);\s]*) *:? *([^;]*);', str, re.M | re.S)

    
    def find_constant(str):
        return re.findall(r'const(.*?);', str)
    
    funs = None
    classes = None
    mine_trash_list = None

    defined_str = None  # 代码中出现的字符串

    code_file = None  # 默认的代码文件

    split_code_files = None  # 拆分的代码文件

    _template = None

    interfaces = None
    imports = None

    def parse(self, *args):
        self.funs = {}
        self.classes = {}
        self.defined_str = []
        self.mine_trash_list = []

        self.collectDefinedStr(self.content, self.name)
        if '#import <UIKit/UIKit.h>' not in self.content:
            add_uikit_str = '#import <UIKit/UIKit.h>\n'
            self.content = add_uikit_str + self.content
        if args[0]:
            self.code_file = OC_Code(self.dir, args[0])
            self.collectDefinedStr(self.code_file.content, self.code_file.name)
            self.code_file.parse()

        self.gen_template()

        for data in self.interfaces.values():
            clz = OC_Class()
            clz.header = self
            clz.name = data.name
            clz.funs = []

            for fun in data.funs.values():
                if self.code_file and fun.fun_key in self.code_file.funs:
                    fun.content = self.code_file.funs[fun.fun_key]
                clz.funs.append(fun)

            self.classes[clz.name] = clz

    def gen_template(self):
        self.interfaces = {}
        self._template = ""
        arr = re.split(r'(@interface *[^\n]+)|(@end)', self.content, 0, re.M | re.S)

        temp = None
        for str in arr:
            if not str:
                continue
            if str.startswith("@interface"):
                temp = str
            elif temp and str.startswith("@end"):
                temp += str
                interface = OC_Interface()
                interface.parse(temp)
                self.interfaces[interface.key] = interface
                self._template += "__{{@interface__%s}}__" % interface.key
                temp = None
            elif temp:
                temp += str
            else:
                self._template += str
        pass

    def add_interface(self, interface):
        if not isinstance(interface, OC_Interface):
            return
        self.interfaces[interface.key] = interface
        self._template += "\n__{{@interface__%s}}__" % interface.key

    def update_content(self):
        content = self._template
        for i in self.interfaces.values():
            content = content.replace("__{{@interface__%s}}__" % i.key, i.content)
        content = re.sub(r'__{{@\w+__[\w:\(\)<>]+}}__', "", content, 0, re.M | re.S)
        self.content = content

    def rename(self, name):
        super(OC_Header, self).rename(name)

    def collectDefinedStr(self, content, *args):
        content = re.sub(r'//[^\r\n]*', "", content, 0, re.M | re.S)

        strings = re.findall(r'.*@[\"\'](\w+)[\"\']', content, re.M)

        argsCnt = len(args)

        if strings:
            for s in strings:
                if s in self.defined_str:
                    continue
                if argsCnt > 0:
                    flag = False
                    for a in args:
                        if a == s:
                            flag = True
                            break
                    if flag:
                        continue
                self.defined_str.append(s)


class OC_Code(obfuscator.CFile):
    @staticmethod
    def find_funs(str):
        return re.findall(r'(^[-|\+] *\([^\(\)]+\)([^\:\(\);\{\}]*)([^\{]*))', str, re.M | re.S)

    funs = None
    _template = None
    def parse(self, *args):
        self.funs = {}
        self.interfaces = {}
        self._template = self.content
        implementations = self.content.split("@end")
        for str in implementations:

            # 获取类名
            group = re.search(r'.*@implementation\ *(\w+)', str, re.M | re.S)

            if not group:
                continue
            clzname = group.group(1).strip()

            funs = OC_Code.find_funs(str)

            def filterRegKeyWord(s):
                return s.replace("[", "\[").replace("]", "\]").replace("?", "\?")

            cnt = len(funs)
            for i in range(cnt):
                if i == cnt - 1:
                    fun1 = filterRegKeyWord(self.format_reg(funs[i][0]))
                    # try:
                    obj = re.search(r'(' + fun1 + '.*)', str,
                                    re.M | re.S)  # fun1.replace("[", "\[").replace("]", "\]") 方法参数中含[]与正则冲突
                    # except:
                    #         print(fun1 + "")
                else:
                    fun1 = filterRegKeyWord(self.format_reg(funs[i][0]))
                    fun2 = filterRegKeyWord(self.format_reg(funs[i + 1][0]))
                    reg = r'(' + fun1 + ' *.*)' + '(?:' + fun2 + "\s*\{)"
                    # try:
                    obj = re.search(reg, str, re.M | re.S)
                    # except:
                    #         print(fun1)
                if not obj:
                    print("error: no functions")
                    continue

                fun_key = OC_Func.gen_key(clzname, funs[i][1], funs[i][2])
                self.funs[fun_key] = obj.group(1)
                # self._template = self._template.replace(self.funs[fun_key], "\n__{{@fun_%s}}__\n" % fun_key)
        return

    
    def replace_str(self, k, v):
        changed1, self._template = obfus_util.replace_str(self._template, k, v)
        changed2 = False
        new_funs = {}
        for f_name, f_content in self.funs.items():
            c1, f_name2 = obfus_util.replace_str(f_name, k, v)
            c2, f_content2 = obfus_util.replace_str(f_content, k, v)
            if c1 or c2:
                new_funs[f_name2] = f_content2
            changed2 = changed2 or c1 or c2

        self.funs.update(new_funs)
        return changed1 or changed2
    

    def update_content(self):
        content = self._template
        self.content = content

    def update_template(self):
        self._template = self.content
        pass

    def get_template(self):
        self.update_content()
        self.update_template()
        return self._template

    def format_reg(self, str):
        return str.replace("+", "\+").replace("-", "\-").replace("(", "\(").replace(")", "\)").replace("*",
                                                                                                       "\*").replace(
            ".", "\.").replace("^", "\^")


class XibFile(obfuscator.CFile):
    classes = None
    selectors = None

    def parse(self, *args):
        self.classes = re.findall(r'customClass=\"(\w+)\"', self.content, re.M | re.S)
        self.selectors = re.findall(r'selector=\"(\w+)\:\"', self.content, re.M | re.S)


class PchFile(obfuscator.CFile):
    imports = None

    def parse(self, *args):
        self.imports = re.findall(r'#import "([^#\.]*)\.h"', self.content, re.M | re.S)
        pass


class OC_Interface(object):
    name = None
    key = None
    content = None
    funs = None

    def parse(self, content):
        
        self.key = re.sub(r'\s', "", re.search(r'@interface *([^\n]*)', content).group(1))
        if "(" in self.key and ")" in self.key:
            self.name = self.key.replace("(", "+").replace(")", "")
        else:
            self.name = re.match(r'(\w*).*', self.key).group(1)
        self.content = content
        self.funs = {}

        funs = OC_Header.find_funs(content)

        if self.name == "JSONKeyMapper":
            pass
        for f_type, f_return, f_name, f_argkeys in funs:
            fun = OC_Func()
            fun.clz = self.name
            fun.name = f_name.strip()
            fun.method_tyep = f_type
            fun.return_type = f_return
            fun.fun_key = OC_Func.gen_key(self.name, fun.name, f_argkeys)
            self.funs[fun.fun_key] = fun


class OC_Class(obfuscator.CClass):
    header = None
    splite_code_index = None

# 垃圾代码对象
class OC_Trash(object):
    belong_class = None
    trash_classes = []
    trash_codes = []
    
# interface对象
class OC_Add_Trash_Interface(object):
    name = None
    proprety_list = []
    trash_fun_list = []

    
# 代码中的垃圾混淆映射表对象
class OC_Add_Trash_Object(object):
    # 所属的类名
    belong_class = None
    # .h中的interfaces
    header_interfaces = []
    # .m中的垃圾代码方法
    code_file_codes = []


class OC_Func(obfuscator.CFunction):
    @staticmethod
    def gen_key(clz, fun, args):
        argkeys = re.findall(r'(\w+:)', args, re.M | re.S)
        return clz + " " + fun.strip() + (
                    (":" if len(args) > 0 else "") + (":".join(argkeys) if len(argkeys) > 0 else ""))

    fun_key = ""
    content_split = None       
    

    def split_content(self):
        if self.content.find("environment") != -1:
            pass

        self.content_split = []
        lines = self.content.split("\n")

        one_line_condition = False

        str = ""
        for line in lines:
            if line.lstrip().startswith("//"):
                continue
            if (line.find("if") != -1 or line.find("else if") != -1 or line.find("else") != -1) and line.find(
                    "{") == -1:
                one_line_condition = True
            elif one_line_condition and line.find("{") != -1:
                one_line_condition = False
            line = line.rstrip()
            str += line
            if line.endswith(";") and not one_line_condition:
                self.content_split.append(str)
                str = ""
            str += "\n"
        if str:
            self.content_split.append(str)

        return self.content_split

    def gen_content(self):
        if self.content_split:
            self.content = "".join(self.content_split)

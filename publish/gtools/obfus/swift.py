
# -*- coding: utf8 -*-
# typescript代码混淆

import re
import json
import uuid
import random
import string
from pbxproj import *
import sys
import gtools.obfus.swift_util as swift_util


import gtools.obfus.obfuscator as obfuscator
import gtools.util.file as file_util

class Swift(obfuscator.Obfucator):
    xcodeproj_path = ""
    code_files = {}
    pchs = {}
    xibs = {}
    multi_classes = None

    add_code_file_num = 10

    add_code_num_min = 3
    add_code_num_max = 10
    add_code_rate = 50

    code_file_target = None
    default_keyword = ["JSON.*", "NS.+", "UI.+", "name", "md5", "main", "AppDelegate", "ViewController", "init",
                       "initWith.*", "show",
                       "setValue", "DEPRECATED_MSG_ATTRIBUTE", "dataTaskWithRequest", "appendData",
                       "valueFor<PERSON><PERSON><PERSON>eader<PERSON>ield",
                       "sharedManager",
                       "uploadTaskWithRequest", "uploadTaskWithStreamedRequest", "downloadTaskWithRequest",
                       "downloadTaskWithResumeData"]
    def parse_args(self, args):
        super(Swift, self).parse_args(args)

        self.xcodeproj_path = args.proj
        self.encrypt_type = args.encrypt
        self.root_path = args.root

        if "code_file_num" in args:
            self.add_code_file_num = args.code_file_num
        if "code_num_min" in args:
            self.add_code_num_min = args.code_num_min
        if "code_num_max" in args:
            self.add_code_num_max = args.code_num_max
        if "code_rate" in args:
            self.add_code_rate = args.code_rate
        if "code_file_target" in args:
            self.code_file_target = args.code_file_target

    def parse(self, path, file):
        if file.endswith(".swift"):
            code_file = Swift_Code(path, file)
            code_file.parse()
            self.append_defined_str(code_file.defined_str)

            self.code_files[code_file.name] = code_file
        elif file.endswith(".xib"):
            xib = XibFile(path, file)
            xib.parse()
            self.xibs[xib.dir + "/" + xib.name] = xib
        elif file.endswith(".pch"):
            pch = PchFile(path, file)
            pch.parse()
            self.pchs[pch.dir + "/" + pch.name] = pch

    def gen_code_file(self, path, name, imports, clzs, prop_num, fun_num, rand_rang=[5, 10], letters=string.ascii_letters):
        swift_util.gen_code_file(path, name, imports, clzs, prop_num, fun_num, rand_rang, letters, self.gen_fun)

    def gen_fun(self, clz_name, fun_name=None, fun_type=None, mark=None, rand_rang=[5, 10], letters=string.ascii_letters,
                fun_temp=None):
        return swift_util.gen_fun(clz_name, fun_name, fun_type, mark, rand_rang, letters, fun_temp)

    # python 获取字符串末尾第一次出现某个字符的位置
    def find_last(self, string, str):
        last_position = -1
        while True:
            position = string.find(str, last_position + 1)
            if position == -1:
                return last_position
            last_position = position

    def add_trash_code(self):

        self.code_file_names = []
        if self.add_code_num_max == 0:
            return

        for code in self.code_files.values():
            self.code_file_names.append(code.name)

        # 加入垃圾代码
        for code in self.code_files.values():
            for clz in code.classes.values():

                if self.in_exclude_path(clz.file.path):
                    continue

                # for fun_name, fun_content in clz.file.funs.items():
                # code_file = clz.header.code_file
                add_funs = []
                used_str = {}
                add_fun_names = []
                for i in range(random.randint(self.add_code_num_min, self.add_code_num_max)):
                    if self.encrypt_type == 2:
                        letters = []
                        letters.extend(self.en_lexicon["prefix"])
                        letters.extend(self.en_lexicon["postfix"])
                        fun = self.gen_fun(clz.name, mark=used_str, rand_rang=[2, 4], letters=letters)
                        fun.content = fun.content.replace('-(func)', '    static public func ')
                        # fun.content = fun.content.replace(fun.name, fun.name + '()')
                    else:
                        fun = self.gen_fun(clz.name, mark=used_str)
                    if fun.name not in add_fun_names:  # 过滤同名方法
                        add_funs.append(fun)
                        add_fun_names.append(fun.name)
                        # self.exclude_keyword.append(fun.name_str)
                        self.append_defined_str(fun.name)  # 垃圾方法名存入屏蔽字库 不做混淆
                        if fun.name not in self.exclude_keyword:
                            self.exclude_keyword.append(fun.name)
                            class_content = clz.content
                            code_file = '\n'+fun.content + clz.content # clz.file.content + fun.content  # 在内容中加入垃圾代码
                            clz.content = code_file

                            pos = self.find_last(clz.file.content, '}')
                            file_content = clz.file.content[:pos]
                            clz.file.content = file_content + '\n'+ fun.content + "\n}\n\n"


                            # clz.file.content = clz.file.content.replace(class_content, code_file)
                            file_util.write_file(clz.file.path,  clz.file.content)


                            #实现垃圾方法在其它地方调用
                            state = True
                            while(state):
                                num = random.randint(0,self.code_files.__len__()-1)
                                class_name = self.code_file_names[num]
                                if class_name != code.name:
                                    if self.code_files[class_name].funs.__len__() > 0:
                                        class_name = self.code_file_names[num]
                                        insert_funs = self.code_files[class_name].funs
                                        state = False


                            insert_num = random.randint(0, insert_funs.__len__())
                            s = -1
                            for fun_info in insert_funs:
                                s += 1
                                if s == int(insert_num):
                                    if fun.name not in self.exclude_keyword and class_name not in self.exclude_keyword and clz.name not in self.exclude_keyword:
                                    # if fun_info != 'viewDidLoad' and class_name != 'AppDelegate' and class_name != 'Constraint'and  clz.name!= 'func':
                                        print(clz.name,fun.name)
                                        str = "     %s.%s()" %(clz.name, fun.name) #方法调用
                                        old_fun_count = self.code_files[class_name].funs[fun_info].content
                                        new_fun_count = '\n'+ str + '\n' + old_fun_count

                                        new_content = self.code_files[class_name].content.replace(old_fun_count, new_fun_count)
                                        self.code_files[class_name].content = new_content

                                    # state = True
                                    # lines = old_fun_count.split("\n") #防止在被注释的方法中加入调用报错
                                    # for line in lines:
                                    #     if line.lstrip().startswith("//"):
                                    #         state = False

                                    # if state == True:
                                        file_util.write_file(self.code_files[class_name].path, self.code_files[class_name].content)

                                    break



    def add_trash_code_file(self, used_str):
        if self.add_code_file_num == 0:
            return
        if used_str is None:
            used_str = {}
        classArray = ['NSString', 'NSArray', 'NSDictionary', 'NSData']

        add_files = []
        for i in range(self.add_code_file_num):
            # path = self.input_path + "/"+ obfuscator.obfus_path("", 3, 0)
            path = self.root_path#self.input_path +'/ChessGame'

            if self.encrypt_type == 2:
                letters = []
                letters.extend(self.en_lexicon["prefix"])
                letters.extend(self.en_lexicon["postfix"])
                rand_rang = [2, 4]
            else:
                letters = string.ascii_letters
                rand_rang = [3, 7]

            # 生成一个指定长度的随机字符串，其中
            # string.digits=0123456789
            # string.ascii_letters=abcdefghigklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
            length = random.randint(5,13)
            str_list = [random.choice(string.digits + string.ascii_letters) for i in range(length)]
            random_str = ''.join(str_list)
            left = [random.choice(string.ascii_letters) for i in range(3)]
            str = ''.join(left)
            name = self.encrypt_str('','class')   #    str+random_str#obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), True, letters, used_str)
            # if self.file_name_add_str != None:
            #     name = self.file_name_add_str + name
            self.gen_code_file(path, name, ["<Foundation/Foundation.h>"], classArray, random.randint(0, 10),
                               random.randint(3, 20), rand_rang, letters)

            add_ob_file = Swift_Code(path,name + '.swift')
            add_ob_file.parse()
            add_files.append(add_ob_file)

        pass

        reload(sys)
        sys.setdefaultencoding('utf-8')

        project = XcodeProject.load(self.xcodeproj_path + '/project.pbxproj')
        group = project.get_groups_by_path(os.path.basename(self.input_path))

        for f in add_files:
            r = project.add_file(f.file, parent=group[0], tree=u'<group>', target_name=self.code_file_target)

        project.save()

        # # 在代码中调用
        for code in add_files:
            if self.code_file_names.__len__() == 0:
                break
            state = True
            while (state):
                num = random.randint(0, self.code_files.__len__() - 1)
                class_name = self.code_file_names[num]
                if class_name != code.name:
                    if self.code_files[class_name].funs.__len__() > 0:
                        class_name = self.code_file_names[num]
                        insert_funs = self.code_files[class_name].funs
                        state = False

            insert_num = random.randint(0, insert_funs.__len__())
            s = -1

            for fun_info in insert_funs:
                s += 1
                if s == int(insert_num):
                    insert_str = ''
                    for clz in code.funs:
                        if fun_info not in self.exclude_keyword and class_name not in self.exclude_keyword and code.name not in self.exclude_keyword:
                        # if fun_info != 'viewDidLoad' and class_name != 'AppDelegate' and class_name != 'Constraint'and code.name != 'func':
                            print(code.name, clz)
                            insert_str += "     %s.%s()\n" % (code.name, clz)  # 方法调用

                    old_fun_count = self.code_files[class_name].funs[fun_info].content
                    new_fun_count = '\n' + insert_str + '\n' + old_fun_count

                    new_content = self.code_files[class_name].content.replace(old_fun_count, new_fun_count)
                    self.code_files[class_name].content = new_content

                    # lines = old_fun_count.split("\n")  # 防止在被注释的方法中加入调用报错
                    # stat = True
                    # for line in lines:
                    #     if line.lstrip().startswith("//"):
                    #        state = False

                    if state == True:
                        file_util.write_file(self.code_files[class_name].path, self.code_files[class_name].content)
                    break


    def obfu_strings(self):
        # 收集字符串池
        print("collect strings")
        self.fun_pool = {}
        for code in self.code_files.values():

            for ns in code.namespaces.values():
                for str in ns.strings:
                    if not self.is_exclude_keyword(str):
                        self.str_pool[str] = str

            for clz in code.classes.values():
                if self.in_exclude_path(clz.file.path):
                    continue


                if not self.is_exclude_keyword(clz.name):
                    self.str_pool[clz.name] = clz.name
                for fun in clz.funs:
                    if not self.is_exclude_keyword(fun.name):
                        self.fun_pool[fun.name] = fun.name

            for fun in code.funs.values():
                if not self.is_exclude_keyword(fun.name):
                    self.fun_pool[fun.name] = fun.name
            pass


        # 开始混淆字符串池
        print("obfuse strings")
        self.str_pool2 = {}
        for k in self.str_pool.keys():

            v = self.encrypt_str(k,'class')
            while v in self.str_pool2:
                v = self.encrypt_str(k,'class')

            self.str_pool2[v] = k

            self.str_pool[k] = v

        for k in self.fun_pool.keys():
            v = self.encrypt_str(k, 'fun')
            while v in self.str_pool2:
                v = self.encrypt_str(k, 'fun')

            self.str_pool2[v] = k

            self.str_pool[k] = v

            # print("k: %s \n v: %s" % (k, v))
        pass

        file_util.write_file(self.input_path + "/__obfuscator.txt", json.dumps(self.str_pool, ensure_ascii=False))

        xcode_proj = file_util.read_file(self.xcodeproj_path + "/project.pbxproj")
        if self.append_str_pool:
            self.str_pool.update(self.append_str_pool)

        # 开始替换字符串
        print("replace strings")
        for f in self.code_files.values():
            for k, v in self.str_pool.items():

                changed, f.content = self.replace_str(f.content, k, v)

                if changed:
                    file_util.write_file(f.path, f.content)

            # # 替换文件名
            if f.name in self.str_pool:
                new_name = self.str_pool.get(f.name)
                # 重命名文件/
                f.rename(new_name)

                oldname = list(self.str_pool.keys())[list(self.str_pool.values()).index(f.name)]
                # 替换project.pbxproj中文件记录
                xcode_proj = xcode_proj.replace(oldname + f.ext, new_name + f.ext)
                # if f.file:
                #     xcode_proj = xcode_proj.replace(f.file,
                #                                     new_name + f.file.ext)

        for xib in self.xibs.values():
            content = xib.content
            for clz in xib.classes:
                if clz in self.str_pool:
                    content = content.replace('customClass="' + clz + '"',
                                              'customClass="' + self.str_pool[clz] + '"')
            for selector in xib.selectors:
                if selector in self.str_pool:
                    content = content.replace('selector="' + selector + ':"',
                                              'selector="' + self.str_pool[selector] + ':"')

            file_util.write_file(xib.path, content)

            if xib.name in self.str_pool:
                new_name = self.str_pool.get(xib.name)

                # 替换project.pbxproj中文件记录
                xcode_proj = xcode_proj.replace(xib.file, new_name + xib.ext)

                # 重命名文件
                xib.rename(new_name)

        for pch in self.pchs.values():
            content = pch.content
            for i in pch.imports:
                if i in self.str_pool:
                    content = content.replace(i + ".swift", self.str_pool[i] + ".swift")
            file_util.write_file(pch.path, content)

            if pch.name in self.str_pool:
                pch.rename(self.str_pool.get(pch.name))

        file_util.write_file(self.xcodeproj_path + "/project.pbxproj", xcode_proj)

    def do_obfuscate(self, words_path = None):
        super(Swift, self).do_obfuscate(words_path)

        print("start add Swift trash code")
        self.add_trash_code()
        print("add trash Swift code done")

        self.obfu_strings()
        print("code obfuse done!")

        print("start add trash Swift code files")
        used_str = self.str_pool.copy()
        used_str = used_str.update(self.str_pool2)
        self.add_trash_code_file(used_str)
        print("add trash Swift code files done")

        file_util.write_file(self.input_path + "/success.txt", "success")

class Swift_Code(obfuscator.CFile):
    defined_str = None  # 代码中出现的定义字符串

    namespaces = None
    classes = None

    funs = None

    def parse(self, *args):
        self.namespaces = {}
        self.classes = {}
        self.defined_str = []
        self.funs = {}

        strings = re.findall(r'[\"\'](\w+)[\"\']', self.content, re.M)

        for s in strings:
            if s in self.defined_str:
                continue
            self.defined_str.append(s)

        namespaces = re.findall(r'namespace *([^\{\n\r]+)', self.content, re.M | re.S)

        if namespaces:
            for n in namespaces:
                ns = Swift_NS()
                ns.parse(n)
                self.namespaces[n] = ns

        classes = re.findall(r'class *(\w+)[^\{]*\{(((?!class).)*)\}', self.content, re.M | re.S)
        if classes:
           for c in classes:
                clz = Swift_Class()
                clz.file = self
                clz.name = c[0]
                clz.parse(c[1])

                self.classes[clz.name] = clz
        pass

        # funs = re.findall(r'func *(\w+)[^\{]*\{(((?!func).)*)\}',self.content, re.M | re.S)
        funs = re.findall(r'func *(\w+)[^\{]*\{(((?!func).)*)\}', self.content, re.M | re.S)
        # funs = re.findall(r'func (\w+)\(([^\)]*)\)(?: *: *(\w+))', self.content, re.M | re.S)
        if funs:
            for f in funs:
                fun = Swift_Func()
                fun.name = f[0]
                fun.method_tyep = "public"
                fun.return_type = f[2]
                fun.content = f[1] #add

                self.funs[fun.name] = fun
        pass

class XibFile(obfuscator.CFile):
    classes = None
    selectors = None

    def parse(self, *args):
        self.classes = re.findall(r'customClass=\"(\w+)\"', self.content, re.M | re.S)
        self.selectors = re.findall(r'selector=\"(\w+)\:\"', self.content, re.M | re.S)


class PchFile(obfuscator.CFile):
    imports = None

    def parse(self, *args):
        self.imports = re.findall(r'#import "([^#\.]*)\.h"', self.content, re.M | re.S)
        pass

class Swift_NS(object):
    strings = None

    def parse(self, content):
        self.strings = []
        self.strings = content.replace(" ", "").split(".")

class Swift_Class(obfuscator.CClass):
    file = None

    def parse(self, content):
        super(Swift_Class, self).parse(content)
        funs = re.findall(r'(static *|public *|private *|protected *)?(\.?)(\w+)[^\{]*\{(((?!func).)*)\}', content,
                          re.M | re.S)
        # funs = re.findall(r'(static *|public *|private *|protected *)?(\.?)(\w+)\(([^\)]*)\)(?: *: *(\w+))', content, re.M | re.S)
        self.funs = []

        for f in funs:
            if f[1] == ".": #调用的方法，非类方法
                continue
            fun = Swift_Func()
            fun.clz = self
            fun.name = f[2]
            fun.method_tyep = f[0] if f[0] else "public"
            fun.return_type = f[4]

            self.funs.append(fun)

class Swift_Func(obfuscator.CFunction):
    @staticmethod
    def gen_key(clz, fun, args):
        argkeys = re.findall(r'(\w+:)', args, re.M | re.S)
        return clz + " " + fun.strip() + (
                    (":" if len(args) > 0 else "") + (":".join(argkeys) if len(argkeys) > 0 else ""))

    fun_key = ""
    content_split = None

    def split_content(self):
        if self.content.find("environment") != -1:
            pass

        self.content_split = []
        lines = self.content.split("\n")

        one_line_condition = False

        str = ""
        for line in lines:
            if line.lstrip().startswith("//"):
                continue
            if (line.find("if") != -1 or line.find("else if") != -1 or line.find("else") != -1) and line.find(
                    "{") == -1:
                one_line_condition = True
            elif one_line_condition and line.find("{") != -1:
                one_line_condition = False
            line = line.rstrip()
            str += line
            if line.endswith(";") and not one_line_condition:
                self.content_split.append(str)
                str = ""
            str += "\n"
        if str:
            self.content_split.append(str)

        # self.content_split = self.content.split(";")
        return self.content_split

    def gen_content(self):
        if self.content_split:
            self.content = "".join(self.content_split)
# -*- coding: utf8 -*-
# # 代码混淆器
import os
import re
import random
import string
import argparse
import struct
import json
import gtools.util.file as file_util
import gtools.util.string2 as string_util
from pathlib import Path

class Obfucator(object):
    input_path = ""
    output_path = ""

    default_keyword = []
    defined_str = []
    exclude_keyword = []
    exclude_path = []

    elem_type = []  #变量类型

    str_pool = None  # 字符串库
    const_pool = {} # 常量库
    str_pool2 = None
    append_str_pool = None  # 字符串库

    encrypt_type = 0
    en_lexicon = None

    def parse_args(self, args):
        self.output_path = args.o if args.o else args.p
        if args.ep:
            self.exclude_path = args.ep.split(",")
        if args.ek:
            self.exclude_keyword = args.ek.split(",")
        if args.element_key:
            self.elem_type = args.element_key.split(",")
        if args.encrypt:
            self.encrypt_type = args.encrypt

            if self.encrypt_type == 2:
                self.en_lexicon = {"prefix": [], "postfix": []}
        if args.en_lexiconA:
            contents = file_util.read_file_lines(args.en_lexiconA)
            print("lexiconA num:%s" % len(contents))
            for w in contents:
                if not re.search(r'[^\w\d]', w):
                    w = w.capitalize()
                    self.en_lexicon["prefix"].append(w)
            pass
        if args.en_lexiconB:
            contents = file_util.read_file_lines(args.en_lexiconB)
            print("lexiconB num:%s" % len(contents))
            for w in contents:
                if not re.search(r'[^\w\d]', w):
                    w = w.capitalize()
                    self.en_lexicon["postfix"].append(w)
            pass


    def pre_proc(self):
        pass

    def parse(self, path, file):
        pass

    def do_obfuscate(self, words_path=None):
        def byteify(input, encoding='utf-8'):
            if isinstance(input, dict):
                return {byteify(key): byteify(value) for key, value in input.iteritems()}
            elif isinstance(input, list):
                return [byteify(element) for element in input]
            elif isinstance(input, unicode):
                return input.encode(encoding)
            else:
                return input

        if words_path:
            content = file_util.read_file(words_path)
            self.append_str_pool = byteify(json.loads(content, encoding='utf-8'))
        self.str_pool = {}

    def in_exclude_path(self, path):
        if '+' in path:
            for p in self.exclude_path:
                if path.find(p) != -1:
                    return True
            return False
        else:
            cd_path = Path(path)
            for p in self.exclude_path:
                if p in cd_path.parts:
                    return True
            return False

    def is_exclude_keyword(self, str):
        for k in self.exclude_keyword:
            groups = re.match(r'' + k, str)
            if groups and groups.group(0) == str:
                return True
        for k in self.default_keyword:

            groups = re.match(r'' + k, str)
            if groups and groups.group(0) == str:
                return True
        for k in self.defined_str:
            if str == k:
                return True
        if self.append_str_pool and str in self.append_str_pool:
            return True

        return False

    def append_defined_str(self, strings):
        if not len(strings):
            return
        for str in strings:
            if str in self.defined_str:
                return
            self.defined_str.append(str)

    def encrypt_str(self, str, type):
        if type == "class":
            num = random.randint(3, 20)
            return self.random_str(num, str[0].isupper())
        elif type == "fun":
            num1 = random.randint(1, 4)
            num2 = random.randint(1, 4)
            prefix = self.random_str(num1, str[0].isupper())
            postfix = self.random_str(num2)
            return prefix + str + postfix
        elif type == "attribute":
            if not self.en_lexicon:
                raise Exception("need lexicon")
            prefix = random.choice(self.en_lexicon["prefix"])
            return prefix
        elif type == "split_str":
            if not self.en_lexicon:
                raise Exception("need lexicon")
            prefix = random.choice(self.en_lexicon["prefix"])
            return prefix
        else:
            return str

    def random_str(self, num, firstupper=None, elements=string.ascii_letters + string.digits):
        keylist = [random.choice(elements) for i in range(num if firstupper is None else num - 1)]
        str = ("".join(keylist))
        if firstupper is not None:
            str = (random.choice(string.uppercase) if firstupper else random.choice(string.lowercase)) + str

        return str

    def replace_str(self, str, source, repl):
        reg = r'([^\w])(' + source + ')([^\w]|\Z)'
        if not re.search(reg, str, re.M | re.S):
            return False, str

        flag = False

        arr = re.split(reg, str)
        for i in range(len(arr)):
            if arr[i] == source:
                arr[i] = repl
                flag = True

        try:
            # str2 = "".join(arr).encode("utf-8")
            str2 = ""
            for s in arr:
                str2 += s
        except UnicodeDecodeError:
            pass
        return flag, str2


class CFile(object):
    def __init__(self, dir, file, flag="r"):
        self.dir = dir
        self.file = file
        self.path = dir + "/" + file
        self.name = file[:file.rindex(".")]
        self.ext = file[file.rindex("."):]
        self.content = file_util.read_file(self.path, flag)

    def parse(self, *args):
        pass

    def rename(self, name):
        try:
            os.rename(self.path, self.dir + "/" + name + self.ext)
        except WindowsError:
            print("Error")
            return
        except TypeError:
            print("Error")
            return

        self.name = name
        self.file = name + self.ext
        self.path = self.dir + "/" + self.file


class CClass(object):
    name = ""
    funs = None

    content = ""

    def parse(self, content):
        self.content = content


class CFunction(object):
    clz = ""
    name = ""
    method_tyep = ""
    return_type = ""
    content = ""

    def parse(self, content):
        self.content = content


def argument_parser(description):
    opts = argparse.ArgumentParser(description=description)
    opts.add_argument('-p', default=None, help="dir to obfucate")
    opts.add_argument('-o', help="output the files <dir>")
    opts.add_argument('-ep', help="exclude the path not to obfucate")
    opts.add_argument('-ek', help="exclude the key word not to obfucate")
    opts.add_argument('-encrypt', type=int, default=0,
                      help="encrypt type of string\n 0:like HZr36FdSJhcMZb 1:prefix+name+posfix 2:english lexicon")
    opts.add_argument("-en_lexiconA", default=None)
    opts.add_argument("-en_lexiconB", default=None)
    return opts

def run(mod, path, args, words_path=None):
    if not os.path.exists(path):
        print("Couldn't open the path:%s" % path)
        return

    parser_mod = mod()
    parser_mod.input_path = path

    if args:
        parser_mod.parse_args(args)

    parser_mod.pre_proc()
    for dir, _, files in os.walk(path):
        # if 'BaseComponent' in dir or '.xcframework' in dir or 'Demo' in dir or 'Firebase' in dir or '.framework' in dir:
        if 'BaseComponent' in dir or '.xcframework' in dir or 'Demo' in dir or '.framework' in dir:
            continue    
        for f in files:
            parser_mod.parse(dir, f)

    parser_mod.do_obfuscate(words_path)

def obfus_img(img_data):
    # 改变md5，后面随机加一个字节
    img_data += struct.pack('<b', random.randint(-128, 127))
    return img_data


def obfus_path(path, depth=3, min_depth=1):
    file_ext = path.split(".")[-1]

    depth = random.randint(min_depth, depth)

    ou_path = ""

    for i in range(random.randint(min_depth, depth)):
        ou_path += string_util.random_str() + "/"

    ou_path = ou_path[0:-1]

    if file_ext != "":
        ou_path += "." + file_ext

    return ou_path

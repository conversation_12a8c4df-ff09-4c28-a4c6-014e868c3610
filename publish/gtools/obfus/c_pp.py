# -*- coding: utf8 -*-
# C++代码混淆

#python 获取C++ 文件中所有方法名
#1.自定义垃圾方法
#2.自定义垃圾文件
#3.类名混淆
#4.获取所有方法名和内容
#5.混淆方法名
#6.在类中插入垃圾方法
#7.在类中插入垃圾文件
#8.垃圾方法和文件的调用
#语法比较复杂 规则比较多,定义比较灵活，结构不统一
#luaregister, struct, label, imagebutton, imagesprite, progresscontrol, buttoncontainer, highlightarea, graphiclabel, scrollView, scenemanager, gamescene, gamelayer, edittext, messagelayer, ProgressTimer, actionmanager, nodeunity, FontManager, UITableView, UITableViewCell, PlayerManager, ImageSetManager, ParticleManger, EditBoxEx, RichText, soundmanagerExcy, zqengine, resmanagerExcy, reshelperExcy, httpstruct, httpmanager, logutil, strutil, bitutil, platformtools, cocos2d, luaengine, scrollView, scale9sprite, ImageDataManager, CCClippingNode, CCShaderCache, immanager, CCAuroraSprite, lyheader, PluginChannelEx, PluginChannel, AndroidStatisticData, LabelBMFont, GameConfig, GameLib, GameLib, COCOLog, Md5, UrlEncode, Rand, GetFightState, BitAnd, BitOr, LuaEngine, RegistPackageLoad, SetReloadFlag, LuaEngine, GameEngine, GetTimeTicket, GetNowTime, GetCurrentTimeSecond, CloseApp, GameEngine, ResManager, SetPlatAndResURL, SetGameURL, CheckVersionFile, UpdateFileList, UpdateFileListEx, GetSetting, SetSetting, GetLog, HasNewCppVersion, GetCpp3rdUpdateUrl, GetSystemType, GetFileVersion, GetFileVersionWithVerFile, GetLastVersionFile, UpdateLocalLastResVersion, UpdateAllResourceFile, GetNeedUpdateFileCount, GetFileCount, GetUrlData, GetRemoteFile, GetRemoteFileWithDl, SetCountOfMaxProcessingDownloadTasks, GetCountOfMaxProcessingDownloadTasks, SetGlobalTimeoutOfDownloader, GetGlobalTimeoutOfDownloader, ResetDownloader, SetLuaCallbackDownloadOnProcessing, WriteLog, IsHaveFile, DownloadFile, UnzipFile, CopyFileToDownloadPathEx, ResManager, ResHelper, LoadImageResource, ReleaseImageResource, ReleaseAllImageResource, GetCompressSuffix, UnzipImageFile, GetSoundSuffix, PrintAllTextureCache, ReleaseUnusedImageResource, ReleaseUnusedImageExcept, AddSpriteFrame, GetTextureCacheMemory, IsImageExitCache, COCOSize, width, height, equals, COCOPoint, equals, CCRect, origin, size, setRect, containsPoint, intersectsRect, equals, getMinX, getMidX, getMaxX, getMinY, getMidY, getMaxY, COCORect, width, height, ImageData, width, height, offset_x, offset_y, image_path, scale_size, rotated, ImageDataManager, ImageData, EmptyImageData, ClearImageDataBy, ClearAllImageData, GetInstance, ActionManager, MoveBy, MoveTo, MoveOvalBy, JumpBy, JumpTo, Jump, RotateBy, RotateTo, FadeIn, FadeOut, FadeTo, ScaleTo, ShakeLoop, StopAllActions, EaseInMoveBy, EaseOutMoveBy, EaseInScaleTo, EaseInOutMoveBy, EaseBackOutMoveTo, RepeatShuttled, SceneManager, GetSize, GetScaleSize, RunWithScene, ReplaceScene, PushScene, PopScene, GetRunningScene, ShowMessage, GetStartTime, SetEnableTouchEvent, SetIsHighQualityVersion, GetIsHighQualityVersion, SetKeypadEnabled, SetLuaBackKey, SetLuaMenuKey, SetLuaKeyCode, ScreenTouch, SceneManager, IMManager, GetInstance, Init, IsInit, Release, Login, IsLogin, Logout, SetStandby, StartRecord, StopRecord, Speech, PlayAudio, StopPlayAudio, UploadFile, DownloadFile, BindLuaEventMSG, BindLuaEventLogin, BindLuaEventRecord, BindLuaEventPlay, BindLuaEventSpeech, BindLuaEventUpload, BindLuaEventDownload, PlatformTools, PLATFORM_IOS, PLATFORM_ANDROID, PLATFORM_WIN32, PLATFORM_MAC, NETWORK_WIFI, NETWORK_MOBILE, NETWORK_NONE, NETWORK_UNKNOW, GetNetWorkConnectingState, BuyProduct, SetLuaPayResultCallBack, GoToBrowser, DownloadApk, GetMemorySize, GetAvailMemorySize, GetAppUsedMemory, ShowDialog, GetPlatform, GetProductVersion, GetStorePlatform, GetStoreIdentifier, IsThirdPlatform, VerifyAppUpdate, GetAccountName, GetAccountId, SetUserInfo, SetRoleInfo, GetServerListURl, GetSDKChannelId, GetSDKAppId, GetSDKMdId, ShowLoginDialog, ShowCenterDialog, SetToolBarVisible, LogoutAccount, Pay, onStartTalkingData, IsUseJPush, SetAliasAndTag, SubmitExtendData, shareGame, SetExtra, VoiceRecord, StartRecorder, PauseRecorder, ResumeRecorder, StopRecorder, StringRegexFilter, GetBattery, GetDeviceID, GetDeviceModel, GetDeviceNetworkCode, GetDeviceNetworkType, GetDeviceResolution, GetDeviceOSName, InitAppStore, InviteFacebook, ShareToFaceBookWithJSON, TalkingDataAccount, setAccount, setAccountType, setAccountName, setLevel, setGameServer, commitOrder, OnChargeOrder, onReward, onPurchase, onUse, onBegin, onCompleted, onFailed, onCollectDeadInfo, onUseYuanbaoBuyItem, onUseLieyanBuyItem, createTalkingDataAccount, NodeUnity, ConvertToNodePosition, ConvertToWorldPosition, ConvertToNodePositionXY, ConvertToWorldPositionXY, GetWorldPosition, SetPosition, GetPosition, GetWidth, GetHeight, GetAnchorPoint, SetAnchorPoint, GetRotation, SetRotation, GetScaleFactor, GetScaleXFactor, GetScaleYFactor, SetScaleFactor, SetScaleXFactor, SetScaleYFactor, SetVisible, GetVisible, SetZOrder, RemoveAllChildren, GetTag, SetTag, AddChild, GetContentSize, IsContainsPoint, ReorderChildren, RemoveFromParent, GetNodeAndParentVisible, SetGrey, CreateCursorSprite, ShaderCache, SharedShaderCache, AddProgramWithString, ReloadShader, ReloadDefaultShaders, CCNode, AddChild, RemoveChild, RemoveChildByTag, RemoveFromParent, RemoveAllChild, GetChildByNameRecursively, GetChildByTag, SetPosition, SetVisible, GetPositionX, GetPositionY, GetContentSize, SetContentSize, SetTag, GetTag, IsContainsPoint, ConvertToNodeSpace, ConvertToWorldSpace, GetParent, BoundingBox, SetShaderProgramWithType, Create, GameScene, AddLayer, RemoveLayer, RemoveLayerByTag, RemoveAllLayer, IsObjectActive, addImageAsync, Cast, Node, GameLayer, SetVisible, IsVisible, SetTouchRect, SetPosition, SetAnchorPoint, SetScissorSection, SetScissorSectionByLayer, AddChild, RemoveChild, RemoveChildByTag, RemoveAllChild, RemoveSelf, SetLuaTouchDown, SetLuaTouchMove, SetLuaTouchUp, SetLuaDoubleTap, SetLuaCallBack, SetLuaTouchSwallowTest, SetZOrder, SetScaleFactor, SetLuaUpdate, SetSwallow, SetTouchPriority, setTouchEnabled, SetContentSize, GetWidth, GetHeight, GetRotation, GetChildByTag, GetChildByName, SetName, SetRotation, setColor, SetOpacity, ScreenTouchingCount, Cast, Node, Label, ALIGIN_TOP, ALIGIN_MIDDLE, ALIGIN_BOTTOM, ALIGIN_LEFT, ALIGIN_CENTER, ALIGIN_RIGHT, SetPosition, SetString, SetStringWithFont, SetImageFile, SetImage, SetColor, SetVisible, IsVisible, SetScaleFactor, SetAnchorPoint, SetOpacity, SetStroke, SetHighLight, SetStringStrokeAndShadow, SetStringStroke, SetStringStrokeColor, SetStringStrokeLineWidth, SetGrey, SetGrey, SetLineWidth, SetHeightMax, SetTextWidthAndHeight, SetFlipX, SetFlipY, StretchTo, SetTextHorizontalAlign, SetTextVertiacalAlign, GetChildByTag, GetChildByName, SetName, AddChild, GetWidth, GetHeight, GetString, Cast, CreateWithString, CreateWithImageData, CreateWithImageFile, ImageButton, SetPosition, SetLuaCallBack, SetLuaTouchDown, SetLuaTouchMove, SetLuaTouchUp, SetButtonTextColor, SetIsEnabled, SetNormalImage, SetNormalImageFile, SetNormalImageFileEx, SetSelectedImage, SetSelectedImageFile, SetDisabledImage, SetDisabledImageFile, GetNormalImage, GetSelectedImage, GetDisabledImage, SetAnchorPoint, SetButtonText, SetVisible, SetSwallow, SetScaleFactor, SetDisableButtonTextColor, SetHandlerPriority, SetResponseRect, SetResponseSize, GetWidth, GetHeight, SetFlipX, IsContainsPoint, SetMultipleTouchEnabled, Cast, Node, CreateWithImageFile, CreateWithImageData, CreateWithImageFiles, CreateWithImageDatas, ButtonContainer, ALIGN_NONE, ALIGN_LEFT, ALIGN_RIGHT, ALIGN_TOP, ALIGN_BOTTOM, SetAlign, SetPosition, SetPadding, AddButton, SortButtons, SetAnchorPoint, SetZOrder, SetScaleFactor, SetVisible, RemoveButton, AttachWithButton, DetachWithButton, GetButtonOriginPos, Cast, Node, ImageSprite, SetPosition, AddSpriteState, AddSpriteStateImage, AddSpriteStatePath, AddSpriteStateData, AddSpriteStateIndex, SetSpriteState, IsSpriteStateExist, SetFlipX, SetFlipY, SetVisible, SetZOrder, GetPosition, SetLuaPlayOnceFun, SetAnchorPoint, IsPlayOnce, GetFrameIndex, SetScaleFactor, SetOpacity, SetHighLight, SetUpdate, SetGrey, SetGrey, SetShadow, SetPoison, AddShadowOffset, SetShadowInfo, GetSpriteState, GetWidth, GetHeight, ClearState, SetStateFrame, SetImageBasePath, Cast, Node, CreateWithImage, CreateWithImagePath, CreateWithImageData, ProgressControl, SetPosition, SetRotation, SetProgressMax, AddProgress, SetProgress, SetAnchorPoint, SetCompletedLuaCallBack, InitWithImage, InitWithImage, SetScaleFactor, GetSize, SetVisible, Cast, CreateWithFile, CreateWithImage, EditText, SetMaxLength, SetMaxShowLength, SetPosition, SetText, GetText, SetColor, SetPlaceHolderColor, SetPasswordMode, SetNumberMode, SetKeyBoardWillShowCallBack, SetKeyBoardDidShowCallBack, SetKeyBoardWillHideCallBack, SetKeyBoardDidHideCallBack, SetDidInsertTextCallBack, SetDidDeleteTextCallBack, SetDidEditDoneCallBack, SetVisible, SetAnchorPoint, SetFilterWords, SetVagueLimit, SetKeyboardHide, GetKeyboardHide, SetCursorColor, DetachWithIME, AttachWithIME, Cast, CreateEditText, GraphicLabel, DrawSolidRect, DrawSolidCircle, DrawSolidTriangle, DrawLine, DrawCircle, SetPosition, SetAnchorPoint, SetOpacity, SetScaleFactor, SetVisible, Clear, Cast, Node, GraphicLabelEx, DrawSolidCircle, DrawSolidTriangle, DrawLine, DeleteGraphic, Clear, Cast, Node, UITableView, CELL_WILL_LOAD, CELL_UPDATE, CELL_TOUCHED, CELL_HIGHLIGHT, CELL_UNHIGHLIGHT, CELL_WILL_RECYCLE, DID_SCROLL, DirectionHorizontal, DirectionVertical, ReloadData, SetNumberOfCells, GetNumberOfCells, SetVisible, IsVisible, CellAtIndex, UpdateCellAtIndex, InsertCellAtIndex, RemoveCellAtIndex, SetDefaultCellHeight, GetDefaultCellHeight, SetCellHeightAtIndex, GetCellHeightAtIndex, SetPosition, GetPositionX, GetPositionY, GetSize, SetTableViewEventLuaFuncId, DeleteTableViewEventLuaFuncId, GetCurrentCell, SetDirection, SetTableCellViewSize, SetBounceable, SetContentOffsetXY, SetContentOffset, SetSwallow, SetViewSize, GetContentOffset, SetTouchEnabled, SetContentOffsetInDuration, SetClippingToBounds, MinContainerOffset, MaxContainerOffset, SetLuaTouchDown, SetLuaTouchMove, SetluaTouchEnded, Cast, Create, UITableViewCell, GetIdx, SetIdx, SetObjectID, GetObjectID, Reset, GetIsInit, HasBeenInit, GetChildByTag, GetTouchedSpacePoint, GetTouchedWorldPoint, AddChild, RemoveAllChildren, ScrollView, init, SetPosition, SetDirection, AddChild, AdjustPageScrollView, SetSwallow, SetViewSize, SetBounceable, SetLuaTouchDown, SetLuaTouchMove, SetluaTouchEnded, setluaEndDrag, SetLuaScrollToPage, SetTouchRect, SetPage, SetPageNum, SetTouchEnabled, GetPage, GetContainer, SetPageWidth, SetPageHeight, SetContentSize, GetContentSize, SetContentOffset, GetContentOffset, MinContainerOffset, MaxContainerOffset, ManualRetain, ManualRelease, SetVisible, SetClippingToBounds, SetAnchorPoint, RemoveAllChildren, SetMoveMul, GetContentWidth, GetContentHeight, GetViewSizeWidth, GetViewSizeHeight, SetContentOffsetInDuration, Cast, Node, Create, ProgressTimer, TimerTypeRadial, TimerTypeBar, SetType, SetPercentage, ProgressTimerAction, StopProgressTimerAction, SetPosition, SetActionFinishLuaCallBack, SetVisible, GetVisible, SetBarChangeRate, SetMidpoint, RemoveSelf, SetActionFinishRemoveSelf, SetMaxValue, SetCurValue, SetTextVisible, SetTextColor, SetReverseProgress, GetTextLabel, Cast, Create, CreateWithImageData, CreateWithImageDatas, PlayerManager, SetSpriteState, GetSpriteState, GetFrameIndex, IsPlayOnce, SetUpdate, SetLuaPlayOnceFun, AddSprite, DeleteSprite, Clear, SetOpacity, SetHighLight, SetPoison, SetStateFrame, SetFlipX, SetReturnIndex, SetLuaIndexCallFun, SetScaleFactor, Cast, Node, LabelBMFont, SetString, SetString, SetVisible, SetPosition, SetGrey, SetAnchorPoint, SetFntFile, SetScale, SetOpacity, SetAlignment, SetWidth, GetConsentSizeWidth, GetConsentSizeHeight, Cast, CreateLabelBMFont, EditBoxEx, SetText, GetText, SetColor, SetFontSize, SetFontName, SetWorkBreak, SetContentSize, SetPlaceHolder, SetEnable, SetPassword, SetMaxByte, SetKeyBoardWillShowCallBack, SetKeyBoardDidShowCallBack, SetKeyBoardWillHideCallBack, DetachWithIME, AttachWithIME, SetKeyBoardDidHideCallBack, Cast, create, RichText, setText, scrollToBottom, SetText, SetPosition, GetPosition, SetAnchorPoint, SetVisible, ScrollToBottom, SetTextAndLineSpacing, AddTextAndLineSpacing, setHeightFixed, SetButtonEvents, SetImgBtnEvents, SetLuaTouchDown, SetLuaTouchMove, SetLuaTouchUp, setWidthFixed, SetTouchEnabled, SetScrollEnbled, GetTextHeight, SetColor, SetContentSize, SetHerfUnderLine, SetFontSize, GetContentSize, SetIsUpdate, SetIsStroke, SetSpacingH, Cast, create, CreateRichText, Scale9sprite, SetPreferredSize, SetPosition, SetAnchorPoint, SetVisible, GetWidth, GetHeight, Cast, createWithImgFile, createWithImgFileRect, createWithImgFileCaps, createWithImg, createWithImgRect, createWithImgCaps, ParticleManger, createWithTotalParticles, SetPosition, SetRotation, SetPositionType, ReSetParticle, SetOrder, RemoveShelf, createWithSystem, createWithPlist, ClippingNode, Create, CreateWithStencil, GetStencil, SetStencil, GetAlphaThreshold, SetAlphaThreshold, IsInverted, SetInverted, AuroraSprite, SetLoopCount, SetPosition, Tick, SetUnitInterval, CreateWithPath, SoundManager, PreloadBGMusic, PlayBGMusic, StopBGMusic, PauseBGMusic, ResumeBGMusic, RewindBGMusic, IsBGMusicPlaying, GetBGMusicVolume, SetBGMusicVolume, PlayEffect, PauseEffect, PauseAllEffects, ResumeEffect, ResumeAllEffects, StopEffect, StopAllEffects, PreloadEffect, UnloadEffect, GetEffectsVolume, SetEffectsVolume, Release, Stop, SoundManager, ImageSet, createImage, ImageSetManager, createImageSet, getImageSet, isImageSetExist, ImageSetManager, Font, GetFontHeight, FontManager, RegisterFont, SetFontSizeRange, GetFont, ClearAllFonts, FontManager, BatchRendererManager, g3E, BatchRendererManager, AndroidStatisticData, InitAndroidStatisticManager, OnResetGameParams, OnNewPlayer, OnLoginPlayer, OnPayPlayer, OnPaySuccess, OnGameConsum, AndroidStatisticData, PluginChannelManager, loginEx, login, loginOutEx, enterPlatform, accountSwitch, showToolBar, hideToolBar, setPayInfo_1, setPayInfo_2, setPayInfo, setPayInfo_Ext, pay, pay9377, setlistener, getInitResult, getOrderId, getUserID, getChannelId, getLoginSuccess, submitLoginGameRole, antiAddictionQuery, realNameRegister, Init9377Sdk, getToken, exitGame, updateLevel, setNewRefer, PluginChannelManager, PushManager, initJPush, startPush, closePush, setAlias, delAlias, delAlias, PushManager
#-ep
#cocos2dxlib,libs,Classes,immanager,include,luaengine,luascript,network,platform,resmanager,sound,zip,zqcommon,zqengine,widget,zqengine,scenemanager,gamescene,gamelayer,actionmanager,UITableView,scrollView,ProgressTimer,progresscontrol,PlayerManager,messagelayer,imagesprite,imagebutton,highlightarea,edittext,RichText,EditBoxEx,resmanagerExcy,VersionManager,platformtools,networkcallback,luaregister,luaengine,AppController,SDKInterface,ThirdPlatformInterface,CCScriptSupport,immanager,DownloadExtension,lua
#-ek
#zqengine,scenemanager,gamescene,gamelayer,actionmanager,UITableView,scrollView,ProgressTimer,progresscontrol,PlayerManager,messagelayer,imagesprite,imagebutton,highlightarea,edittext,RichText,EditBoxEx,resmanagerExcy,VersionManager,platformtools,networkcallback,luaregister,luaengine,AppController,SDKInterface,ThirdPlatformInterface,CCScriptSupport,immanager,DownloadExtension,start,Disconnect,registerRenderer,flush,setDirty,Tex_QuadList,strutil,logutil,label,AsynTCPSocket2,fontmanager,ParticleManager,ImageDataManager,ImageSetManager,LabelBMFont,Downloader,font,CCProgressTimer,stringconv,static,public,richitemparser,func,var,Bool,override,init,update,set,get,clear,reset,initialize,Update,Clear,draw,DrawSolidRect,DrawCircle,download,downloadFile,BatchRenderer,SetColor,RotateBy,Jump,MoveTo,MoveBy,ScaleTo,FadeOut,FadeTo,FadeIn,onEnter,RemoveAllChild,clearLuaFuncIds,addImageAsync,RotateTo,destroyInstance,uploadFile,RemoveChild,clearAllFonts,ClearAllImageData,ClearImageDataBy,RemoveAllChildren,unregisterRenderer,Downloader-android,bindLuaEventDownload,initPlatformConfig,bindLuaEventSpeech,JumpBy,JumpTo,SetScale,SetScaleFactor,finalize,playAudio,setRecord,setFontSizeRange,SetVisible,SetLuaCallBack,SetScissorSection,SetEffectsVolumeFxcy,setTouchEnabled,SetPosition,SetString,SetWidth,SetFntFile,speech,initializeItem,applicationWillEnterForeground,applicationDidEnterBackground,AsyncLoadingCallback,logout,setColor,visit,objc,@,in,make,let,lazy,super,required,self,fileprivate,add,animate,layoutSubviews,description,viewDidLoad,prepare,viewDidLayoutSubviews,touchesBegan,touchesEnded,remove,move,_,time,priority,activate,deactivate,advanced,distance,Constraint,tlgspressHUD,IncludePch,addAnimation,initInQueue,sharedInstance,seekToTime,play,pause,initSerial,initSerialWithLabel,initConcurrent,initConcurrentWithLabel,logWithFileName,PVM,YES,NO,DrawSolidCircle,DrawLine,DrawRect,DrawSolidCircle,DrawSolidTriangle,lua,OnReceiveData,DecodeData,RegisterCallback,registerFont,OnSocketClose,RemoveAllSocket,OnSocketConnect,AddChild,ReleaseConnnect,SetAnchorPoint


import re
import os
import uuid
import random
import string
import json
from pbxproj import *
import sys
import c_pp_util
import obfus_util
import gtools.util.file as file_util

import obfuscator

class C_Add(obfuscator.Obfucator):
        xcodeproj_path = ""
        encrypt_type = 0

        disturb_funs = False #乱序函数
        split_classes = False #类切分
        garbage_code = False #添加垃圾代码

        headers = {}
        classes = {}

        pchs = {}

        multi_classes = None

        add_code_file_num = 10

        add_code_num_min = 3
        add_code_num_max = 10
        add_code_rate = 50

        code_file_target = None

        xibs = {}

        default_keyword = ["JSON.*", "NS.+", "UI.+", "name", "md5", "main", "AppDelegate", "ViewController", "init", "initWith.*", "show",
                           "setValue", "DEPRECATED_MSG_ATTRIBUTE", "dataTaskWithRequest", "appendData", "valueForHTTPHeaderField",
                           "sharedManager",
                           "uploadTaskWithRequest", "uploadTaskWithStreamedRequest", "downloadTaskWithRequest", "downloadTaskWithResumeData"]

        def parse_args(self, args):
                super(C_Add, self).parse_args(args)
                self.xcodeproj_path = args.proj
                self.encrypt_type = args.encrypt
                self.proj = args.p
                if "code_file_num" in args:
                        self.add_code_file_num = args.code_file_num
                if "code_num_min" in args:
                        self.add_code_num_min = args.code_num_min
                if "code_num_max" in args:
                        self.add_code_num_max = args.code_num_max
                if "code_rate" in args:
                        self.add_code_rate = args.code_rate
                if "code_file_target" in args:
                        self.code_file_target = args.code_file_target



        def parse(self, path, file):
                if file.endswith(".h"):
                        header = CAA_Header(path, file)

                        if os.path.exists(header.dir + "/" + header.name + ".cpp"):
                                header.parse(header.name + ".cpp")
                        # elif os.path.exists(header.dir + "/" + header.name + ".mm"):
                        #         header.parse(header.name + ".mm")

                        if self.split_classes:
                                if not self.multi_classes:
                                        self.multi_classes = {}

                                if header.classes and len(header.classes) > 1:
                                        if not self.in_exclude_path(header.dir):
                                                print("multi-classes:%s" % header.name)
                                                self.multi_classes.update(header.classes)

                        self.headers[header.dir + "/" + header.name] = header

                        if header.classes:
                                self.classes.update(header.classes)

                        if header.defined_str:
                            for s in header.defined_str:

                                if s in self.defined_str:
                                    continue
                                self.defined_str.append(s)

        def gen_code_file(self, path, name, imports, clzs, prop_num, fun_num, rand_rang=[5, 10], letters=string.ascii_letters):
                c_pp_util.gen_code_file(path, name, imports, clzs, prop_num, fun_num, rand_rang, letters, self.gen_fun)

        def gen_fun(self, clz_name, fun_name=None, fun_type=None, mark=None, rand_rang=[5, 10], letters=string.ascii_letters, fun_temp=None):
                return c_pp_util.gen_fun(clz_name, fun_name, fun_type, mark, rand_rang, letters, fun_temp)

        # python 获取字符串末尾第一次出现某个字符的位置
        def find_last(self, string, str):
                last_position = -1
                while True:
                        position = string.find(str, last_position + 1)
                        if position == -1:
                                return last_position
                        last_position = position

        def add_trash_code(self):
                if self.add_code_num_max == 0:
                        return
                class_name_list = []
                condle_class = {}
                for clz in self.classes.values():
                        if 'cocos2dxlib' not in clz.header.path and 'libs' not in clz.header.path and  'luaengine' not in clz.header.path and  'luascript' not in clz.header.path and  'zqengine' not in clz.header.path and  'soundmanagerExcy' not in clz.header.path  and  'ziper' not in clz.header.path and  'Downloder' not in clz.header.path and  'graphiclabel' not in clz.header.path:
                                if clz.header.code_file.funs.__len__() > 0:
                                        if clz.name not in self.exclude_keyword:
                                                class_name_list.append(clz.name)
                                                condle_class[clz.name] = clz

                # 加入垃圾代码
                # for clz in self.classes.values():
                for clz in condle_class.values():
                        # if self.in_exclude_path(clz.header.path):
                        #         continue

                        # if clz.header.name.__len__() > 1:
                        #         class_name = clz.header.name

                        code_file = clz.header.code_file
                        add_funs = []
                        used_str = {}
                        add_fun_names = []
                        for i in range(random.randint(self.add_code_num_min, self.add_code_num_max)):
                                if self.encrypt_type == 2:
                                        letters = []
                                        letters.extend(self.en_lexicon["prefix"])
                                        letters.extend(self.en_lexicon["postfix"])
                                        fun = self.gen_fun(clz.name, mark=used_str, rand_rang=[2,4], letters=letters)
                                else:
                                        fun = self.gen_fun(clz.name, mark=used_str)
                                if fun.name not in add_fun_names:#过滤同名方法
                                        add_funs.append(fun)
                                        #self.exclude_keyword.append(fun.name_str)
                                        self.append_defined_str(fun.name)#垃圾方法名存入屏蔽字库 不做混淆
                                        if fun.name not in self.exclude_keyword:
                                                self.exclude_keyword.append(fun.name)
                                                add_fun_names.append(fun.name)
                                                clz.header.code_file.content = clz.header.code_file.content + '\n' + fun.content

                                                pos = self.find_last(clz.header.content, '};')
                                                file_content = clz.header.content[:pos]
                                                head = ''
                                                if '<iostream>' not in clz.header.content:
                                                        head = '\n#include<iostream>\n'
                                                if '<string>' not in clz.header.content:
                                                        head = head + '\n#include <string>'

                                                #clz.header.content = '\n#include<iostream>\n#include <string>' + file_content + '\n' + 'public:\n    void %s();\n};\n'%fun.name
                                                clz.header.content = head + file_content + '\n' + 'public:\n    void %s();\n};\n' % fun.name

                                                # clz.header.content = clz.header.content.replace('};','public:\n    void %s();\n};\n'%fun.name)
                                                file_util.write_file(clz.header.path, clz.header.content)
                                                file_util.write_file(code_file.path, clz.header.code_file.content)

                        # 在代码中调用
                        # state = True
                        # while (state):
                        #         num = random.randint(0, class_name_list.__len__() - 1)
                        #         insert_file = ''
                        #         insert_file = self.classes[class_name_list[num]]
                        #         if insert_file.header.code_file.funs.__len__() > 0:
                        #                 state = False

                        num = random.randint(0, class_name_list.__len__() - 1)
                        insert_file = ''
                        insert_file = self.classes[class_name_list[num]]
                        file_name = clz.header.name
                        ins_fun = ''
                        str = [random.choice(string.ascii_letters) for i in range(5)]
                        str1 = ''.join(str)
                        ins_fun = "\n    %s %s;\n" % (file_name, str1)
                        if insert_file != '' and insert_file.header.code_file.name != file_name:
                                for fun_name in add_fun_names:
                                        ins_fun = ins_fun + "\n    %s.%s();\n" % (str1, fun_name)
                                # ins_fun = ins_fun + "\n    %s.%s();\n" % (str, fun.name)

                                if insert_file.header.code_file.funs.__len__() > 0:
                                        num1 = random.randint(0, insert_file.header.code_file.funs.__len__() - 1)

                                        fun_content = insert_file.header.code_file.funs[num1][4]
                                        new_fun_content = ins_fun + fun_content
                                        insert_file.header.code_file.content = insert_file.header.code_file.content.replace(fun_content, new_fun_content)
                                        old_s = '#include "%s.h"' % insert_file.header.code_file.name
                                        new_s = '#include "%s.h"\n#include "%s.h"\n' % (insert_file.header.code_file.name, file_name)
                                        insert_file.header.code_file.content = insert_file.header.code_file.content.replace(old_s,new_s)
                                        file_util.write_file(insert_file.header.code_file.path,insert_file.header.code_file.content)



        def add_trash_code_file(self, used_str):
                class_name_list = []
                for clz in self.classes.values():
                        if 'cocos2dxlib' not in clz.header.path and 'libs' not in clz.header.path and 'luaengine' not in clz.header.path and 'luascript' not in clz.header.path and 'zqengine' not in clz.header.path and 'soundmanagerExcy' not in clz.header.path and 'ziper' not in clz.header.path and 'Downloder' not in clz.header.path and 'graphiclabel' not in clz.header.path and 'functionUtil' not in clz.header.path and 'asyntcpsocket2' not in clz.header.path and 'reshelperExcy' not in clz.header.path and 'resmanagerExcy' not in clz.header.path:
                                if clz.header.code_file.funs.__len__() > 0:
                                        class_name_list.append(clz.name)

                if self.add_code_file_num == 0:
                        return
                if used_str is None:
                        used_str = {}
                classArray = ['NSString', 'NSArray', 'NSDictionary', 'NSData']

                add_files = []
                for i in range(self.add_code_file_num):
                        # path = self.input_path + "/"+ obfuscator.obfus_path("", 3, 0)
                        path = self.proj#self.input_path

                        if self.encrypt_type == 2:
                                letters = []
                                letters.extend(self.en_lexicon["prefix"])
                                letters.extend(self.en_lexicon["postfix"])
                                rand_rang = [2, 4]
                        else:
                                letters = string.ascii_letters
                                rand_rang = [10, 20]

                        name = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), True, letters, used_str)
                        self.gen_code_file(path, name, ["<Foundation/Foundation.h>"], classArray, random.randint(0, 10), random.randint(3, 20), rand_rang, letters)

                        header = CAA_Header(path, name +".h")
                        header.parse(header.name + ".cpp")

                        add_files.append(header)
                pass

                reload(sys)
                sys.setdefaultencoding('utf-8')

                project = XcodeProject.load(self.xcodeproj_path + '/project.pbxproj')
                group = project.get_groups_by_path(os.path.basename(self.proj))

                for f in add_files:
                        r = project.add_file(f.file, parent=group[0], tree=u'<group>', target_name=self.code_file_target)
                        r = project.add_file(f.code_file.file, parent=group[0], tree=u'<group>', target_name=self.code_file_target)
                        pass
                project.save()

                # 在代码中调用
                for code in add_files:
                        # state = True
                        # while (state):
                        #         num = random.randint(0, class_name_list.__len__() - 1)
                        #         insert_file = ''
                        #         insert_file = self.classes[class_name_list[num]]
                        #         if insert_file.header.code_file.funs.__len__() > 0:
                        #                 state = False
                        num = random.randint(0, class_name_list.__len__() - 1)
                        insert_file = ''
                        insert_file = self.classes[class_name_list[num]]
                        file_name = code.name
                        # fun_array = []
                        ins_fun = ''
                        str = [random.choice(string.ascii_letters) for i in range(5)]
                        str1 = ''.join(str)
                        ins_fun = "\n    %s %s;\n" % (file_name, str1)
                        if insert_file != '' and insert_file.header.code_file.name != file_name:
                                for fun in code.code_file.funs:
                                        ins_fun = ins_fun + "\n    %s.%s();\n" % (str1, fun[2])

                                if insert_file.header.code_file.funs.__len__() > 0:
                                        num1 = random.randint(0, insert_file.header.code_file.funs.__len__() - 1)
                                        fun_content = insert_file.header.code_file.funs[num1][4]
                                        new_fun_content = ins_fun + fun_content
                                        insert_file.header.code_file.content = insert_file.header.code_file.content.replace(fun_content, new_fun_content)
                                        old_s = '#include "%s.h"' % insert_file.header.code_file.name
                                        new_s = '#include "%s.h"\n#include "%s.h"\n' % (
                                        insert_file.header.code_file.name, file_name)
                                        insert_file.header.code_file.content = insert_file.header.code_file.content.replace(old_s,new_s)
                                        file_util.write_file(insert_file.header.code_file.path, insert_file.header.code_file.content)


        def obfu_strings(self):
                # 收集字符串池
                print("collect strings")
                for clz in self.classes.values():
                        if self.in_exclude_path(clz.header.path):
                                # 需要把限制的类的关键字添加到排除关键字列表
                                if clz.header.name not in self.exclude_keyword:
                                        self.exclude_keyword.append(clz.header.name)
                                for fun in clz.funs:
                                        if fun.name not in self.exclude_keyword:
                                                self.exclude_keyword.append(fun.name)
                                continue

                        if not self.is_exclude_keyword(clz.header.name):
                                self.str_pool[clz.header.name] = clz.header.name
                        for fun in clz.header.code_file.funs:
                                fun_name = fun[2]
                                if not self.is_exclude_keyword(fun_name):
                                        self.str_pool[fun_name] = fun_name
                pass
                # return
                # 开始混淆字符串池
                print("obfuse strings")
                self.str_pool2 = {}
                for k in self.str_pool.keys():

                        v = self.encrypt_str(k,'class')
                        while v in self.str_pool2:
                                v = self.encrypt_str(k,'class')

                        self.str_pool2[v] = k

                        self.str_pool[k] = v

                        # print("k: %s \n v: %s" % (k, v))
                pass

                file_util.write_file(self.input_path + "/__obfuscator.txt",
                                     json.dumps(self.str_pool, ensure_ascii=False))

                xcode_proj = file_util.read_file(self.xcodeproj_path + "/project.pbxproj")

                if self.append_str_pool:
                        self.str_pool.update(self.append_str_pool)

                # 开始替换字符串
                print("replace strings")
                for header in self.headers.values():
                        content = header.content
                        code = None

                        if header.code_file:
                                code = header.code_file.content

                        for k, v in self.str_pool.items():
                                changed, header.content = self.replace_str(header.content, k, v)

                                if changed:
                                        file_util.write_file(header.path, header.content)

                                if header.code_file:
                                        if k in header.code_file.content:
                                                header.code_file.content = header.code_file.content.replace(k,v)
                                                file_util.write_file(header.code_file.path, header.code_file.content)
                                        # if header.code_file.replace_str(k, v):
                                        #         header.code_file.update_content()
                                        #         file_util.write_file(header.code_file.path, header.code_file.content)

                                if header.split_code_files:
                                        for f in header.split_code_files:
                                                changed, f.content = self.replace_str(f.content, k, v)
                                                if changed:
                                                        f.update_template()
                                                        file_util.write_file(f.path, f.content)

                        # 替换文件名
                        #支持类似 CSHttpUtils+Support.h 这样的命名文件
                        change_name = False
                        header_names = header.name.split("+")
                        for i in range(0, len(header_names)):
                                if header_names[i] in self.str_pool:
                                        change_name = True
                                        header_names[i] = self.str_pool.get(header_names[i])

                        if change_name:
                                new_name = "+".join(header_names)

                                # 替换project.pbxproj中文件记录
                                xcode_proj = xcode_proj.replace(header.file, new_name + header.ext)
                                if header.code_file:
                                        xcode_proj = xcode_proj.replace(header.code_file.file,
                                                                        new_name + header.code_file.ext)

                                # 重命名文件
                                header.rename(new_name)
                                if header.code_file:
                                        header.code_file.rename(new_name)
                                if header.split_code_files:
                                        for f in header.split_code_files:
                                                new_name = self.str_pool.get(f.name)
                                                if new_name:
                                                        f.rename(new_name)

                for pch in self.pchs.values():
                        content = pch.content
                        for i in pch.imports:
                                if i in self.str_pool:
                                        content = content.replace(i + '/' + i + ".h", i + '/' + self.str_pool[i] + ".h")
                                        content = content.replace(i+".h", self.str_pool[i] + ".h")
                                        #content = content.replace('/'+i + ".h", '/'+self.str_pool[i] + ".h")
                        file_util.write_file(pch.path, content)

                        if pch.name in self.str_pool:
                                pch.rename(self.str_pool.get(pch.name))

                file_util.write_file(self.xcodeproj_path + "/project.pbxproj", xcode_proj)



                print("code obfuse done!")

        def do_obfuscate(self, words_path = None):
                super(C_Add, self).do_obfuscate(words_path)

                print("start add trash code")
                self.add_trash_code()
                print("add trash code done")


                self.obfu_strings()

                print("start add trash code files")
                used_str = self.str_pool.copy()
                used_str = used_str.update(self.str_pool2)
                self.add_trash_code_file(used_str)
                print("add trash code files done")


class CAA_Header(obfuscator.CFile):
        @staticmethod
        def find_funs(str):
                # return re.findall(r'(?:^|\n)([\-|\+]) *\(([^\(\)]+)\) *([^\:\(\);\s]*) *:? *([^;]*);', str, re.M | re.S)
                return  re.findall(r'(^void )([^\(]*);',str, re.M | re.S)

        funs = None
        classes = None

        defined_str = None  #代码中出现的字符串

        code_file = None # 默认的代码文件

        split_code_files = None #拆分的代码文件

        _template = None

        interfaces = None
        imports = None

        def parse(self, *args):
                # print("header:%s" % self.name)
                self.funs = {}
                self.classes = {}
                self.defined_str = []

                self.collectDefinedStr(self.content, self.name)

                if args[0]:
                        self.code_file = CAA_Code(self.dir, args[0])
                        implementations = self.code_file.content.split("};")
                        for str in implementations:
                                funs = CAA_Code.find_funs(str)
                                self.code_file.funs = funs
                        # self.collectDefinedStr(self.code_file.content, self.code_file.name)
                        # self.code_file.parse()

                self.gen_template()

                for data in self.interfaces.values():
                        clz = CAA_Class()
                        clz.header = self
                        clz.name = data.name
                        clz.funs = []

                        for fun in data.funs.values():
                                if self.code_file and fun.fun_key in self.code_file.funs:
                                        fun.content = self.code_file.funs[fun.fun_key]
                                clz.funs.append(fun)

                        self.classes[clz.name] = clz

        def gen_template(self):
                self.interfaces = {}
                self._template = ""
                arr = re.split(r'(class *[^\n]+)|(};)', self.content, 0, re.M | re.S)

                temp = None
                for str in arr:
                        if not str:
                                continue
                        if str.startswith("class"):
                                temp = str
                        elif temp and str.startswith("};"):
                                temp += str
                                interface = CAA_Interface()
                                interface.parse(temp)
                                self.interfaces[interface.key] = interface
                                self._template += "__{{class__%s}}__" % interface.key
                                temp = None
                        elif temp:
                                temp += str
                        else:
                                self._template += str
                pass
        def add_interface(self, interface):
                if not isinstance(interface, CAA_Interface):
                        return
                self.interfaces[interface.key] = interface
                self._template += "\n__{{class__%s}}__" % interface.key

        def update_content(self):
                content = self._template
                for i in self.interfaces.values():
                        content = content.replace("__{{class__%s}}__" % i.key, i.content)
                content = re.sub(r'__{{@\w+__[\w:\(\)<>]+}}__', "", content, 0, re.M | re.S)
                self.content = content

        def rename(self, name):
                super(CAA_Header, self).rename(name)

        def collectDefinedStr(self, content, *args):
                # content = re.sub(r'/\*(.|\r\n)*\*/', "", content, 0, re.M | re.S)
                content = re.sub(r'//[^\r\n]*', "", content, 0, re.M | re.S)

                strings = re.findall(r'.*@[\"\'](\w+)[\"\']', content, re.M)

                argsCnt = len(args)

                if strings:
                        for s in strings:
                                if s in self.defined_str:
                                        continue
                                if argsCnt > 0:
                                        flag = False
                                        for a in args:
                                                if a == s:
                                                        flag = True
                                                        break
                                        if flag:
                                                continue
                                self.defined_str.append(s)

class CAA_Code(obfuscator.CFile):
        @staticmethod
        def find_funs(str):
                # return re.findall(r'(^[-|\+] *\([^\(\)]+\)([^\:\(\);\{\}]*)([^\{]*))', str, re.M | re.S)

                # funs = re.findall(r'(^void )([^\(]*)', self.content, re.M | re.S)
                # return re.findall(r'(\w+) *(\w+)\::*(\w+)[^\{]*\{(((?!void).)*)\}', str, re.M | re.S)
                ####return re.findall(r'(^void *(\w+)\::*(\w+)[^\{]([^\::\(\);\{\}]*))', str, re.M | re.S)
                return re.findall(r'(^void (\w+)\::*(\w+)([^\{w]*)[^\{]*\{(((?!void).)*)\})', str, re.M | re.S)
                # funs = re.findall(r'void *(\w+)[^\{]*\{(((?!void).)*)\}', self.content, re.M | re.S)

        funs = None
        _template = None

        def parse(self, *args):
                self.funs = {}
                self._template = self.content
                implementations = self.content.split("};")

                for str in implementations:
                        # 获取类名
                        group = re.search(r'class\ *(\w+)', str, re.M | re.S)

                        if not group:
                                continue
                        clzname = group.group(1).strip()

                        funs = CAA_Code.find_funs(str)


                        test = funs
                        def filterRegKeyWord(s):
                                return s.replace("[", "\[").replace("]", "\]").replace("?", "\?")


                        for fun in funs:
                                self._template = self._template.replace(self.funs[fun[2]],
                                                                        "\n__{{@fun_%s}}__\n" % fun[2])
                        # cnt = len(funs)
                        # for i in range(cnt):
                        #         if i == cnt-1:
                        #                 fun1 = filterRegKeyWord(self.format_reg(funs[i][2]))
                        #                 # try:
                        #                 obj = re.search(r'(' + fun1 + '.*)', str, re.M | re.S) #fun1.replace("[", "\[").replace("]", "\]") 方法参数中含[]与正则冲突
                        #                 # except:
                        #                 #         print(fun1 + "")
                        #         else:
                        #                 fun1 = filterRegKeyWord(self.format_reg(funs[i][2]))
                        #                 fun2 = filterRegKeyWord(self.format_reg(funs[i+1][2]))
                        #                 reg = r'('+fun1+' *.*)'+ '(?::'+ fun2 +"\s*\{)"
                        #                 # try:
                        #                 obj = re.search(reg, str, re.M | re.S)
                        #                 # except:
                        #                 #         print(fun1)
                        #         if not obj:
                        #                 print("error: no functions")
                        #                 continue
                        #
                        #         fun_key = CAA_Func.gen_key(clzname, funs[i][2], funs[i][0])
                        #         self.funs[fun_key] = obj.group(1)
                        #         self._template = self._template.replace(self.funs[fun_key], "\n__{{@fun_%s}}__\n" % fun_key)
                return

        def replace_str(self, k, v):
                changed1, self._template = obfus_util.replace_str(self._template, k, v)

                changed2 = False
                for f_name, f_content in self.funs.items():
                        c1, f_name2 = obfus_util.replace_str(f_name, k, v)
                        c2, f_content = obfus_util.replace_str(f_content, k, v)
                        if c1 or c2:
                                del self.funs[f_name]
                                self.funs[f_name2] = f_content
                        changed2 = changed2 or c1 or c2

                return changed1 or changed2

        def update_content(self):
                content = self._template
                for fun_key, fun_text in self.funs.items():
                        content = content.replace("\n__{{@fun_%s}}__\n" % fun_key, fun_text)
                self.content = content

        def update_template(self):
                self._template = self.content
                for fun_key, fun_text in self.funs.items():
                        self._template = self._template.replace(fun_text, "\n__{{@fun_%s}}__\n" % fun_key)

                pass

        def get_template(self):
                self.update_content()
                self.update_template()
                return self._template

        def format_reg(self, str):
                return str.replace("+", "\+").replace("-", "\-").replace("(", "\(").replace(")", "\)").replace("*", "\*").replace(".", "\.").replace("^", "\^")


class CAA_Interface(object):
        name = None
        key = None
        content = None
        funs = None

        def parse(self, content):
                self.key = re.sub(r'\s', "", re.search(r'class *([^\n]*)', content).group(1))
                self.name = re.match(r'(\w*).*', self.key).group(1)
                self.content = content
                self.funs = {}

                funs = CAA_Header.find_funs(content)

                if self.name == "JSONKeyMapper":
                        pass
                for f_type, f_return, f_name, f_argkeys in funs:
                        fun = CAA_Func()
                        fun.clz = self.name
                        fun.name = f_name.strip()
                        fun.method_tyep = f_type
                        fun.return_type = f_return
                        fun.fun_key = CAA_Func.gen_key(self.name, fun.name, f_argkeys)
                        self.funs[fun.fun_key] = fun


class CAA_Class(obfuscator.CClass):
        header = None
        splite_code_index = None

class CAA_Func(obfuscator.CFunction):
        @staticmethod
        def gen_key(clz, fun, args):
                argkeys = re.findall(r'(\w+::)', args, re.M | re.S)
                return clz + " " + fun.strip() + (("::" if len(args) > 0 else "") + ("::".join(argkeys) if len(argkeys) > 0 else ""))

        fun_key = ""
        content_split = None

        def split_content(self):
                if self.content.find("environment") != -1:
                        pass

                self.content_split = []
                lines = self.content.split("\n")

                one_line_condition = False

                str = ""
                for line in lines:
                        if line.lstrip().startswith("//"):
                                continue
                        if (line.find("if") != -1 or line.find("else if") != -1 or line.find("else") != -1) and line.find("{") == -1:
                                one_line_condition = True
                        elif one_line_condition and line.find("{") != -1:
                                one_line_condition = False
                        line = line.rstrip()
                        str += line
                        if line.endswith(";") and not one_line_condition:
                                self.content_split.append(str)
                                str = ""
                        str += "\n"
                if str:
                        self.content_split.append(str)

                # self.content_split = self.content.split(";")
                return self.content_split

        def gen_content(self):
                if self.content_split:
                        self.content = "".join(self.content_split)

# -*- coding: utf-8 -*-
import random
import string
import ts
import obfus_util

import gtools.util.file as file_util

def gen_fun(clz_name):

    #生成一个指定长度的随机字符串，其中
    #string.digits=0123456789
    #string.ascii_letters=abcdefghigklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
    # str_list = [random.choice(string.digits + string.ascii_letters) for i in range(16)]
    str_list = [random.choice(string.ascii_letters) for i in range(16)]
    random_str = ''.join(str_list)

    # num = random.randint(10, 200)
    str_con = [random.choice(string.digits + string.ascii_letters) for i in range(random.randint(10, 300))]
    content_str = ''.join(str_con)

    str = "\nfunction %s():void\n{\n \t console.log('%s');\n}\n" %(random_str, content_str)

    fun = ts.TS_Func()
    fun.clz = clz_name

    fun.content = str

    return fun

def gen_code_file(namespace, path, name):
    content_ts = ""
    # str_x = [random.choice(string.digits + string.ascii_letters) for i in range(7)]
    # str_y = [random.choice(string.digits + string.ascii_letters) for i in range(7)]
    str_x = [random.choice(string.ascii_letters) for i in range(7)]
    str_y = [random.choice(string.ascii_letters) for i in range(7)]
    random_x = ''.join(str_x)
    random_y = ''.join(str_y)
    content_ts += "namespace %s{\n" \
                 "\t export class %s{\n" \
                 "\t private static g_objInstance:%s = null;\n" \
                 "\t static get singleton():%s{\n" \
                 "\t\tif (!this.g_objInstance){\n" \
                 "\t\t\t this.g_objInstance = new %s();\n" \
                  "\t\t}\n" \
                 "\t\t return this.g_objInstance;\n" \
                  "\t}\n" \
                 "\t show(%s:number,%s:number):void{\n" \
                 "\t console.log(%s+%s);\n" \
                  "\t}\n" \
                 "}\n" \
                  "}\n" % (namespace, name, name, name, name, random_x, random_y, random_x, random_y)
    file_util.write_file("%s/%s.ts" % (path, name), content_ts)

# -*- coding: utf-8 -*-
# !/usr/bin/env python
import random
import os, sys
import datetime
import string
import gtools.util.file as file_util
import gtools.obfus.obfus_util as obfus_util
import gtools.obfus.swift as swift


def gen_fun(clz_name, fun_name=None, fun_type=None, mark=None, rand_rang = [5, 10], letters = string.ascii_letters, fun_temp = None):
    # name = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), None, letters, mark)
    name = obfus_util.random_str2()
    if not fun_name:
        fun_name = name

    if fun_temp is None:
        str_list = [random.choice(string.digits + string.ascii_letters) for i in range(36)]
        random_str = ''.join(str_list)
        num = random.randint(3,40)
        str_b = [random.choice(string.digits + string.ascii_letters) for i in range(num)]
        random_str1 = ''.join(str_b)
        log_str = random_str + random_str1

        str = " static public func %s()\n{\n  let str1:String = String.init(format:\"%s\")\n  print(str1)\n}\n" % (fun_name, log_str)
        s = "<type 'str'>"
        if s in str:
            str = str.replace(s,name)
    else:
        fun_text = fun_temp()
        str = " static public func %s()\n{\n%s\n}\n" % (fun_name, fun_text)

    fun = swift.Swift_Func()
    fun.clz = clz_name
    fun.name = fun_name
    fun.content = str

    return fun


def gen_code_file(path, name, imports, clzs, prop_num, fun_num, rand_rang = [5, 10], letters = string.ascii_letters, gen_fun2 = None):
    classArray = ['UIColor', 'UILabel', 'UITableView', 'UISlider', 'UIScrollView', 'UIView', 'UIButton']
    array = createClassName()
    array = list(set(array))
    methodArray = []
    for i in range(50, 100):
        methodArray.append(random.choice(array))

    methodArray = list(set(methodArray))  # 数组去重

#
    full_path =  "%s/%s.swift" % (path, name) #'/SwiftFiles/' + fileNmae + '.swift'
    # full_path = sys.path[0] + '/SwiftFiles/' + fileNmae + '.swift'

    # if os.path.exists(full_path):
    #     os.remove(full_path)
    # os.mkdir(full_path)
    file = open(full_path, 'w')

    nowTime = datetime.datetime.now().strftime('%Y/%m/%d')
    file.write(
        '//\n//  ' + name + '.swift\n//  LinkMall\n\n//  Created by 9377 on %s.\n//  Copyright © 2019年 OneThing Ltd. All rights reserved.\n//\n\n' % nowTime)

    file.write('import UIKit \n\n' + 'class ' + name + ': UIViewController {\n\n')

    propryNameArray = []

    for index in range(1, fun_num):
        propryNameArray.append(random.choice(array))

    propryNameArray = list(set(propryNameArray))

    for propertyName in propryNameArray:
        file.write('    public var ' + propertyName + ':' + random.choice(classArray) + '!\n')

    file.write('\n\n')

    file.write('    override func viewDidLoad() {\n        super.viewDidLoad()\n    }\n\n')

    for methodName in methodArray:

        if gen_fun2 is None:
            file.write('    static public func ' + methodName + 'TOVC() {\n\n       var realArr = Array<String>()\n')
            number = random.randint(10, 15)
            for i in range(1, number):
                file.write('       realArr.append("' + random.choice(array) + '")\n')
            file.write('\n    }\n\n')
        else:
            use_strings = {}
            str = obfus_util.random_str2()
            # str = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), None, letters, use_strings)
            fun = gen_fun2(name, str, letters=letters)
            content = ''
            content += fun.content + "\n\n"
            content = content.replace('-(func)', '    static public func ')
            # content = content.replace(fun.name, fun.name+'()')
            # file.write('    static public func ' + methodName+'() {\n\n')
            file.write(content)
            # file.write('\n    }\n\n')

    file.write('}')#

    file.close()


# python 获取字符串第一次出现某个字符的位置
def find_last(self, string, str):
    last_position = -1
    while True:
        position = string.find(str, last_position + 1)
        if position == -1:
            return last_position
        last_position = position

def createClassName():
    first = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

    second = "abcdefghijklmnopqrstuvwxyz"

    index = 0

    array = []

    # 设置生成多少个类
    classNumber = random.randint(30,100) #100
    for i in range(classNumber):

        final = (random.choice(first))
        # 字符串长度
        index = random.randint(7, 15)

        for i in range(index):
            final += (random.choice(second))

        final += (random.choice(first))

        for i in range(index):
            final += (random.choice(second))

        array.append(final)
    return array




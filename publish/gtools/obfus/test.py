# -*- coding: utf8 -*-
import gtools.obfus.obfuscator as obfuscator
import gtools.obfus.ts as ts
import gtools.util.file as file_util
import gtools.obfus.ts_util as ts_util
import gtools.util.os2 as os2
import random
import io
import os
import json
import sys
import shutil
import random
import string
import zipfile
from pbxproj import *
import gtools.obfus.obj_c_util as obj_c_util
import CSGString

reload(sys)
sys.setdefaultencoding('utf-8')


if __name__ == "__main__":
    opts = obfuscator.argument_parser("simple obfuscator tool")
    opts.add_argument('-t', default="oc", help="obfuse type")
    opts.add_argument('-proj', default=None)
    opts.add_argument('-code_file_num', default=30, type=int)
    opts.add_argument('-code_num_min', default=10, type=int)
    opts.add_argument('-code_num_max', default=20, type=int)
    opts.add_argument('-code_file_fun_num', default=10, type=int)
    opts.add_argument('-code_rate', default=0, type=int)

    args = opts.parse_args()
    print('混淆脚本中的结果')

    # obfuscator.run(SDK_MOD_OC, args.p, args)
    obfuscator.run(ts.TypeScript, args.p, args)

# -*- coding: utf-8 -*-
import random
import string
import c_pp
import obfus_util

import gtools.util.file as file_util



def gen_fun(clz_name, fun_name=None, fun_type=None, mark=None, rand_rang = [5, 10], letters = string.ascii_letters, fun_temp = None):
    if not fun_name:
        fun_name = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), None, letters, mark)

    if fun_temp is None:
        str_list = [random.choice(string.digits + string.ascii_letters) for i in range(36)]
        random_str = ''.join(str_list)
        num = random.randint(3,40)
        str_b = [random.choice(string.digits + string.ascii_letters) for i in range(num)]
        random_str1 = ''.join(str_b)
        log_str = random_str + random_str1

        str = ' void %s::%s()\n{\n  std::string str1 = "%s";\n  std::cout<<str1;\n}\n' % (clz_name, fun_name, log_str)
    else:
        str = fun_temp

    fun = c_pp.CAA_Func()
    fun.clz = clz_name
    fun.name = fun_name
    fun.content = str

    return fun


def gen_code_file(path, name, imports, clzs, prop_num, fun_num, rand_rang = [5, 10], letters = string.ascii_letters, gen_fun2 = None):
    classArray = ['UIColor', 'UILabel', 'UITableView', 'UISlider', 'UIScrollView', 'UIView', 'UIButton']
    array = createClassName()
    array = list(set(array))
    methodArray = []
    for i in range(50, 100):
        methodArray.append(random.choice(array))

    methodArray = list(set(methodArray))  # 数组去重


    full_head = "%s/%s.h" %(path, name)
    file_h = open(full_head, 'w')
    file_h.write('#include <string>\n #include <iostream>\n class %s\n{\n    public:\n    %s();\n~%s();\n' %(name,name,name))
    for methodName in methodArray:
        test = '     void %s();\n'% methodName
        file_h.write('     void %s();\n' % methodName)
    file_h.write('};')
    file_h.write('\n')
    file_h.close()


    full_path =  "%s/%s.cpp" % (path, name)
    file = open(full_path, 'w')
    file.write('#include <string>\n#include <stdio.h>\n#include <stdlib.h>\n#include <iostream>\n#include "%s.h"\n%s::%s()\n{\n}\n%s::~%s()\n{\n}\n\n '%(name,name,name,name,name) )

    for methodName in methodArray:
        array_name = obfus_util.random_str(random.randint(3, 7), None, letters, None)
        file.write('void %s::%s() {\n\n     char %s[%s];\n' %(name, methodName,array_name,random.randint(1000,4000)))

        number = random.randint(2, 25)

        for i in range(1, number):
            obf_content = obfus_util.random_str(random.randint(5, 40), None, letters, None)
            file.write('     sprintf(%s, "%s");\n' %(array_name,obf_content))

        file.write('\n    }\n\n')


    file.close()


def createClassName():
    first = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

    second = "abcdefghijklmnopqrstuvwxyz"

    index = 0

    array = []

    # 设置生成多少个类
    classNumber = random.randint(30,100) #100
    for i in range(classNumber):

        final = (random.choice(first))
        # 字符串长度
        index = random.randint(5, 30)

        for i in range(index):
            final += (random.choice(second))

        final += (random.choice(first))

        for i in range(index):
            final += (random.choice(second))

        array.append(final)
    return array


# # include <iostream>
# # include <CTIME>
# using std::cout;
# const int SIZE_CHAR = 32; // 生成32 + 1位CStyle字符串
# const char CCH[] = "_0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_";
# int main()
# {
#     srand((unsigned)time(NULL));
#     char ch[SIZE_CHAR + 1] = {0};
#     for (int i = 0; i < SIZE_CHAR; ++i)
#     {
#         // int x = rand() % (sizeof(CCH) - 1); // 这个方法不好, 因为许多随机数发生器的低位比特并不随机,
#
#         // RAND MAX 在ANSI 里  # define 在<stdlib.h>
#
#         // RAND MAX是个常数, 它告诉你C库函数rand()的固定范围。
#
#         // 不可以设RAND MAX为其它的值, 也没有办法要求rand()返回其它范围的值。
#
#         int x = rand() / (RAND_MAX / (sizeof(CCH) - 1));
#
#         ch[i] = CCH[x];
#     }
#     cout << ch << "\n";
#     return 0;
# }





# # include <iostream>
# # include <stdlib.h>
# # include <time.h>
# using
# namespace
# std;
#
# char * randstr(char * str, const
# int
# len)
# {
# srand(time(NULL));
# int
# i;
# for (i = 0; i < len; ++i)
#     {
#         switch((rand() % 3))
#     {
#         case
#     1:
#     str[i] = 'A' + rand() % 26;
# break;
# case
# 2:
# str[i] = 'a' + rand() % 26;
# break;
# default:
# str[i] = '0' + rand() % 10;
# break;
# }
# }
# str[++i] = '\0';
# return str;
# }
#
# int
# main(void)
# {
#
# char
# name[20];
# cout << randstr(name, 8) << endl;
# system("pause");
# return 0;
# }
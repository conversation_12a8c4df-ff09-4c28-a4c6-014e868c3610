# -*- coding: utf-8 -*-
import random
import string
import gtools.obfus.obj_c as obj_c
import gtools.obfus.obfus_util as obfus_util

import gtools.util.file as file_util

def gen_fun(clz_name, fun_name=None, fun_type=None, mark=None, rand_rang = [5, 10], letters = string.ascii_letters, fun_temp = None):
    if not fun_name:
        fun_name = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), None, letters, mark)

    if fun_temp is None:
        if fun_type is None:
            fun_type = random.randint(0,1)

        if fun_type == 0:
            # fun_text = "\tNSLog(@\"%s\");\n" % obfus_util.random_str(random.randint(10, 100))
            # ret_type = "void"
            fun_text = "\tNSMutableArray *array = [NSMutableArray array];\n"
            for i in range(1, random.randint(3, 10)):
                fun_text += "\t[array addObject:@\"%s\"];\n" % obfus_util.random_str(random.randint(10, 100), elements=letters)
            fun_text += "\treturn array;\n"
            ret_type = "NSMutableArray *"
        elif fun_type == 1:
            fun_text = "\tNSMutableArray *array = [NSMutableArray array];\n"
            for i in range(1, random.randint(3, 10)):
                fun_text += "\t[array addObject:@\"%s\"];\n" % obfus_util.random_str(random.randint(10, 100), elements=letters)
            fun_text += "\treturn array;\n"
            ret_type = "NSMutableArray *"
        else:
            fun_text = ""
            ret_type = "void"
    else:
        fun_text, ret_type = fun_temp()

    fun_name = "-(%s)%s" % (ret_type, fun_name)
    new_fun_name = fun_name
    str = "%s\n{\n%s}\n" % (fun_name, fun_text)


    f_type, f_return, f_name, f_argkeys = obj_c.OC_Header.find_funs(fun_name + ";")[0]

    fun = obj_c.OC_Func()
    fun.clz = clz_name
    fun.name = new_fun_name
    fun.method_tyep = f_type
    fun.return_type = f_return
    fun.fun_key = obj_c.OC_Func.gen_key(clz_name, fun.name, f_argkeys)
    fun.content = str

    return fun

def gen_code_file(path, name, imports, clzs, prop_num, fun_num, rand_rang = [5, 10], letters = string.ascii_letters, gen_fun2 = None):
    content_h = ""
    content_m = ""
    for i in imports:
        content_h += "#import %s\n\n" % i

    content_m += '#import "%s.h"\n\n' % name

    content_h += "@interface %s : NSObject\n\n" % name
    content_m += "@implementation %s\n\n" % name

    content_h += "/*\n单例\n */\n+(%s *)sharedInstance;\n" % name

    content_m += "+(%s *)sharedInstance{\n" \
                 "\tstatic %s *instance;\n" \
                 "\tstatic dispatch_once_t onceToken;\n" \
                 "\tdispatch_once(&onceToken, ^{\n" \
                 "\t\tinstance = [[%s alloc] init];\n" \
                 "\t});\n" \
                 "\treturn instance;" \
                 "\n}\n\n" % (name, name, name)

    use_strings = {}

    # for i in range(1, prop_num):
    #     str = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), None, letters, use_strings)
    #     content_h += '@property(nonatomic,strong)%s * %s;\n' % (random.choice(clzs), str)

    content_h += "\n\n"

    for i in range(1, fun_num):
        str = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), None, letters, use_strings)
        if gen_fun2 is None:
            fun = gen_fun(name, str, letters=letters)
        else:
            fun = gen_fun2(name, str, letters=letters)
        if fun.name is None:
            content_h += "-(%s)%s;\n" % (fun.return_type,str)
        else:
            content_h += "%s;\n" % (fun.name)
        content_m += fun.content +"\n\n"

    content_h += "@end"
    content_m += "@end"


    file_util.write_file("%s/%s.h" % (path, name), content_h)
    file_util.write_file("%s/%s.m" % (path, name), content_m)
# -*- coding: utf-8 -*-
import random
import string
import re
import CSGString

def encrypt_str(str, encrypt_type):
    if encrypt_type == 0:
        num = random.randint(3, 20)
        return random_str(num, str[0].isupper())
    elif encrypt_type == 1:
        num1 = random.randint(1, 4)
        num2 = random.randint(1, 4)
        prefix = random_str(num1, str[0].isupper())
        postfix = random_str(num2)
        return prefix + str + postfix
    else:
        return str


def random_str(num, firstupper=None, elements=string.ascii_letters + string.digits, mark = None):
    # keylist = [random.choice(elements) for i in range(num if firstupper is None else num - 1)]
    # str = ("".join(keylist))
    # if firstupper is not None:
    #     str = (random.choice(string.uppercase) if firstupper else random.choice(string.lowercase)) + str
    # if mark and mark.has_key(str):
    #     str = random_str(num, firstupper, elements, mark)
    #     mark[str] = True

    return CSGString.getRandomStr()

#从a - zA - Z0 - 9生成指定数量的随机字符：
def random_str1():
    num = random.randint(5,13)
    ran_str = ''.join(random.sample(string.ascii_letters + string.digits, num))
    return  ran_str

#从a - zA - Z生成指定数量的随机字符：
def random_str2():
    num = random.randint(5, 13)
    ran_str = ''.join(random.sample(string.ascii_letters, num))
    return  ran_str

def replace_str(str, source, repl):
    str2 = ""
    reg = r'([^\w])(' + source + ')([^\w]|\Z)'
    if not re.search(reg, str, re.M | re.S):
        return False, str

    flag = False

    arr = re.split(reg, str)
    for i in range(len(arr)):
        if arr[i] == source:
            arr[i] = repl
            flag = True

    try:
        str2 = "".join(arr)
    except UnicodeDecodeError:
        pass
    return flag, str2

def gen_png():
    content = str(random.randint(1, 100)) * random.randint(1024, 10240)
    content = content + "0000000049454e44ae426082".decode('hex')
    return content
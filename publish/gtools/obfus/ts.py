# -*- coding: utf8 -*-
# typescript代码混淆
import re
import json

import os
import uuid
import random
import string
from pbxproj import *
import sys
import ts_util
import obfus_util


import obfuscator
import gtools.util.file as file_util

class TypeScript(obfuscator.Obfucator):
    code_files = {}

    multi_classes = None

    add_code_file_num = 10

    add_code_num_min = 3
    add_code_num_max = 10
    add_code_rate = 50

    code_file_target = None

    def parse_args(self, args):
        super(TypeScript, self).parse_args(args)
        self.encrypt_type = args.encrypt

        if "code_file_num" in args:
            self.add_code_file_num = args.code_file_num
        if "code_num_min" in args:
            self.add_code_num_min = args.code_num_min
        if "code_num_max" in args:
            self.add_code_num_max = args.code_num_max
        if "code_rate" in args:
            self.add_code_rate = args.code_rate
        if "code_file_target" in args:
            self.code_file_target = args.code_file_target

    def parse(self, path, file):
        if file.endswith(".ts"):
            code_file = TS_Code(path, file)
            code_file.parse()
            self.append_defined_str(code_file.defined_str)

            self.code_files[code_file.name] = code_file


    def add_trash_code(self):
        if self.add_code_num_max == 0:
            return

        for code in self.code_files.values():
            for clz in code.classes.values():
            # 加入垃圾代码
                if self.in_exclude_path(clz.file.path):
                    continue
                code_file = ""
                add_funs = []
                used_str = {}
                for i in range(random.randint(self.add_code_num_min, self.add_code_num_max)):
                    fun = ts_util.gen_fun(clz.name)
                    add_funs.append(fun)

                    print("fun____________content")
                    print(fun.content)

                    code_file = clz.file.content + fun.content  # 在内容中加入垃圾代码
                    clz.file.content = code_file



    def add_trash_code_file(self, used_str):
        if self.add_code_file_num == 0:
            return
        if used_str is None:
            used_str = {}

        add_files = []
        for i in range(self.add_code_file_num):
            path = self.input_path+'/game'

            if self.encrypt_type == 2:
                letters = []
                letters.extend(self.en_lexicon["prefix"])
                letters.extend(self.en_lexicon["postfix"])
                rand_rang = [2, 4]
            else:
                letters = string.ascii_letters
                rand_rang = [3, 7]

            str_list = [random.choice(string.ascii_letters) for i in range(16)]
            name = ''.join(str_list)
            #name = obfus_util.random_str(random.randint(rand_rang[0], rand_rang[1]), True, letters, used_str)

            ts_util.gen_code_file(name + "_" + name, path, name)

            add_files.append(name)
            #pass

            # 在代码中调用
            used_files = {}
            for code in self.code_files.values():
                for clz in code.classes.values():
                    if self.in_exclude_path(clz.file.path):
                        continue
                    code_file = ""
                    if clz.file.name  not in add_files:

                        str_list = [random.choice(string.ascii_letters) for i in range(random.randint(7,20))]
                        str = ''.join(str_list)

                        template = 'import %s = %s.%s; \n%s.singleton.show(%s,%s);\n' % (
                        str, name + "_" + name, name, str, random.randint(1,100), random.randint(1,100))
                        # code_file = "%s\n" % template +clz.file.content #在内容中加入调用
                        code_file = clz.file.content + "\n%s\n" % template  # 在内容中加入调用
                        clz.file.content = code_file
                        file_util.write_file(clz.file.path, clz.file.content)


    def do_obfuscate(self, words_path = None):
        super(TypeScript, self).do_obfuscate(words_path)

        print("start add TS trash code")
        self.add_trash_code()
        print("add trash TS code done")

        # 收集字符串池
        print("collect strings")
        for code in self.code_files.values():

            for ns in code.namespaces.values():
                for str in ns.strings:
                    if not self.is_exclude_keyword(str):
                        self.str_pool[str] = str

            for clz in code.classes.values():
                if self.in_exclude_path(clz.file.path):
                    continue

                if not self.is_exclude_keyword(clz.name):
                    self.str_pool[clz.name] = clz.name
                for fun in clz.funs:
                   if not self.is_exclude_keyword(fun.name):
                        self.str_pool[fun.name] = fun.name

                # for key in clz.file.defined_str:#收集变量名
                #     if not self.is_exclude_keyword(key):
                #         self.str_pool[key] = key

            for fun in code.funs.values():
                if not self.is_exclude_keyword(fun.name):
                    self.str_pool[fun.name] = fun.name
            pass

        #*****************add-2019-10-17******************
        key_value_path = 'H:\H5_Min_Game_Obfus\key_value.txt'
        file = open(key_value_path, "rb")
        fileJson = json.load(file)
        # ***********************************************

        print("obfuse strings")
        used_str = []
        for k in self.str_pool.keys():

            # *****************add-2019-10-17******************
            if k in fileJson.keys():
                v = fileJson[k]
            else:
                v = self.encrypt_str(k,'class')
            # ***********************************************
            # v = self.encrypt_str(k, 'class')
            while v in used_str:
                v = self.encrypt_str(k,'class')

            used_str.append(v)

            self.str_pool[k] = v

            # print("k: %s \n v: %s" % (k, v))
        pass

        file_util.write_file(self.input_path + "/__obfuscator.txt", json.dumps(self.str_pool, ensure_ascii=False))

        if self.append_str_pool:
            self.str_pool.update(self.append_str_pool)

        # 开始替换字符串
        print("replace strings")
        for f in self.code_files.values():
            for k, v in self.str_pool.items():

                changed, f.content = self.replace_str(f.content, k, v)

                if changed:
                    file_util.write_file(f.path, f.content)

            # # 替换文件名
            if f.name in self.str_pool:
                new_name = self.str_pool.get(f.name)
                # 重命名文件
                f.rename(new_name)
        print("code obfuse done!")

        print("start add trash TS code files")
        self.add_trash_code_file(used_str)
        print("add trash TS code files done")

class TS_Code(obfuscator.CFile):
    defined_str = None  # 代码中出现的定义字符串

    namespaces = None
    classes = None

    funs = None

    def parse(self, *args):
        self.namespaces = {}
        self.classes = {}
        self.defined_str = []
        self.funs = {}

        string_list = re.findall(r'(?<=let )(.*?)(?=:)', self.content, re.M)#获取let和=之间的字符串
        strings = list(set(string_list))
        # strings = re.findall(r'[\"\'](\w+)[\"\']', self.content, re.M)

        for s in strings:
            if s in self.defined_str or ' ' in s or '=' in s or ':' in s or len(s)<4 or len(s)>10 :
                continue
            self.defined_str.append(s)

        namespaces = re.findall(r'namespace *([^\{\n\r]+)', self.content, re.M | re.S)

        if namespaces:
            for n in namespaces:
                ns = TS_NS()
                ns.parse(n)
                self.namespaces[n] = ns

        classes = re.findall(r'class *(\w+)[^\{]*\{(((?!class).)*)\}', self.content, re.M | re.S)
        if classes:
           for c in classes:
                clz = TS_Class()
                clz.file = self
                clz.name = c[0]
                clz.parse(c[1])

                self.classes[clz.name] = clz
        pass

        funs = re.findall(r'function (\w+)\(([^\)]*)\)(?: *: *(\w+))', self.content, re.M | re.S)
        if funs:
            for f in funs:
                fun = TS_Func()
                fun.name = f[0]
                fun.method_tyep = "public"
                fun.return_type = f[2]

                self.funs[fun.name] = fun
        pass

class TS_NS(object):
    strings = None

    def parse(self, content):
        self.strings = []
        self.strings = content.replace(" ", "").split(".")

class TS_Class(obfuscator.CClass):
    file = None

    def parse(self, content):
        super(TS_Class, self).parse(content)

        funs = re.findall(r'(public *|private *|protected *)?(\.?)(\w+)\(([^\)]*)\)(?: *: *(\w+))', content, re.M | re.S)
        self.funs = []

        for f in funs:
            if f[1] == ".": #调用的方法，非类方法
                continue
            fun = TS_Func()
            fun.clz = self
            fun.name = f[2]
            fun.method_tyep = f[0] if f[0] else "public"
            fun.return_type = f[4]

            self.funs.append(fun)

class TS_Func(obfuscator.CFunction):
    pass

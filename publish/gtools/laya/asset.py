# -*- coding: utf8 -*-

import os
import re
import json

import gtools.util.file as file_util
import gtools.util.string2 as string_util
import gtools.util.os2 as os_util
import gtools.obfus.obfuscator as obfuscator
import gtools.util.image as image_util

def obfuscate_asset(bin_dir, content, asset, asset_map):
        if not asset_map.has_key(asset):
                obf_asset = obfuscator.obfus_path(asset, 3, 2)
                asset_map[asset] = obf_asset
                src_asset = bin_dir + "/" + asset
                if os.path.exists(src_asset):
                        asset_type = src_asset.split(".")[-1]
                        if asset_type == "atlas":
                                src_asset2 = src_asset[0:-5] + "png"
                                obf_asset2 = obf_asset[0:-5] + "png"
                                data = obfuscator.obfus_img(file_util.read_file(src_asset2, "rb"))
                                file_util.write_file(bin_dir + "/" + obf_asset2, data, "wb")
                                atlas_content = json.loads(file_util.read_file(src_asset))
                                frames = atlas_content["frames"].keys()
                                prefix = atlas_content["meta"]["prefix"]
                                atlas_content["meta"]["image"] = obf_asset2.split("/")[-1]
                                atlas_content["meta"]["prefix"] = "/".join(obf_asset2.split("/")[0:-1])
                                if atlas_content["meta"]["prefix"] != "":
                                        atlas_content["meta"]["prefix"] += "/"
                                for frame in frames:
                                        asset_map[prefix + frame] = atlas_content["meta"]["prefix"] + frame
                                file_util.write_file(bin_dir + "/" + obf_asset, json.dumps(atlas_content))
                        elif asset_type == "png" or asset_type == "jpg":
                                data = obfuscator.obfus_img(file_util.read_file(src_asset, "rb"))
                                file_util.write_file(bin_dir + "/" + obf_asset, data, "wb")
                        else:
                                data = file_util.read_file(src_asset, "rb")
                                file_util.write_file(bin_dir + "/" + obf_asset, data, "wb")
                        content = content.replace(asset, obf_asset)
                        os_util.system_rm(src_asset)
        else:
                content = content.replace(asset, asset_map[asset])
        return content

def obfuscate_ui(ui_dir, bin_dir, asset_map):
        for root, _, files in os.walk(ui_dir):
                for file in files:
                        file = root + "/" + file

                        if file[-3:] == ".ts":
                                content = file_util.read_file(file).decode("utf8")
                                for skin in re.findall("\"skin\":\"[^\"]+\"", content):
                                        skin = eval(skin[7:])
                                        content = obfuscate_asset(bin_dir, content, skin, asset_map)
                                file_util.write_file(file, content.encode("utf8"))

def obfuscate_config(config_file, bin_dir, asset_map):
        content = file_util.read_file(config_file)
        for asset in re.findall("\"asset_\w+\":\"[^\"]+\"", content):
                asset = eval(asset.split(":")[-1])
                content = obfuscate_asset(bin_dir, content, asset, asset_map)
        file_util.write_file(config_file, content)

def clean_bin(bin_dir):
        rec = False
        for root, dirs, _ in os.walk(bin_dir):
                for dir in dirs:
                        dir = root + "/" + dir
                        if not len(os.listdir(dir)):
                                os_util.system_rmdir(dir)
                                rec = True

        if rec:
                clean_bin(bin_dir)

def obfuscate(proj_dir):
        bin_dir = proj_dir + "/bin"
        ui_dir = proj_dir + "/src/style"
        config_file = proj_dir + "/src/Config.ts"

        asset_map = {}

        obfuscate_config(config_file, bin_dir, asset_map)

        obfuscate_ui(ui_dir, bin_dir, asset_map)

        clean_bin(bin_dir)

def replace_res(proj_dir, res_dir):
        proj_res_dir = proj_dir + "/bin/res"

        for root, _, files in os.walk(res_dir):
                for file in files:
                        temp = file.split(".")
                        if len(temp) > 1:
                                file_type = temp[-1]
                        else:
                                file_type = ""
                        file = (root + "/" + file).replace("\\", "/")
                        res_file = file.replace((res_dir + "/").replace("\\", "/"), "")
                        des_file = proj_res_dir + "/" + res_file
                        if os.path.exists(des_file):
                                if file_type == "png" or file_type == "jpg":
                                        image_util.replace(file, des_file)
                                else:
                                        os_util.system_cp(file, des_file)

#replace_res("F:/work/x2/dev/trunk/client/x2.1/web/cgame2", "F:/work/x2/dev/trunk/client/x2.1/web/res")
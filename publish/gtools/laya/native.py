# -*- coding: utf8 -*-

import pbxproj

import gtools.util.os2 as os_util
import gtools.util.image as image_util
import gtools.util.string2 as string_util

def purge_xcode(layaproj_dir, xcodeproj_dir):
        xcodeproj_dir = xcodeproj_dir.replace("\\", "/")
        proj_name = xcodeproj_dir.split("/")[-1]
        project_file = "%s/ios/%s/%s.xcodeproj/project.pbxproj" % (xcodeproj_dir, proj_name, proj_name)
        project = pbxproj.XcodeProject.load(project_file)
        # project.remove_files_by_path("resource/font", u"<group>")
        project.remove_files_by_path("resource/libconchRuntime.bundle", u"<group>")
        project.remove_files_by_path("resource/logo", u"<group>")
        project.remove_files_by_path("resource/logo.swf", u"<group>")
        project.save(project_file)
        resource_dir = "%s/ios/%s/resource" % (xcodeproj_dir, proj_name)
        # os_util.system_rmdir("%s/font" % (resource_dir,))
        os_util.system_rmdir("%s/libconchRuntime.bundle" % (resource_dir,))
        os_util.system_rmdir("%s/logo" % (resource_dir,))
        os_util.system_rm("%s/logo.swf" % (resource_dir,))

        icon = layaproj_dir + "/bin/res/native/ios/icon.png"
        launch = layaproj_dir + "/bin/res/native/ios/launch.png"
        icon_conf_pat = layaproj_dir + "/bin/res/native/ios/icon.json"
        launch_conf_pat = layaproj_dir + "/bin/res/native/ios/launch.json"

        icon_dir = "%s/ios/%s/%s/Assets.xcassets/AppIcon.appiconset" % (xcodeproj_dir, proj_name, proj_name)
        os_util.del_files(icon_dir)
        image_util.gen_ios_icons(string_util.random_str(), icon, icon_conf_pat, icon_dir)

        launch_dir = "%s/ios/%s/%s/Assets.xcassets/Launch.imageset" % (xcodeproj_dir, proj_name, proj_name)
        os_util.del_files(launch_dir)
        image_util.gen_ios_launches(string_util.random_str(), launch, launch_conf_pat, launch_dir)

        os_util.system_rmdir(layaproj_dir + "/bin/res/native")

#purge_xcode("F:/work/x2/dev/trunk/client/x2.1/web/cgame", "F:/cgame")
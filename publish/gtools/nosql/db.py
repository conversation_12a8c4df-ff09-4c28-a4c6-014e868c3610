# -*- coding: utf8 -*-

from SSDB import SSDB

HOST = "127.0.0.1"
PORT = 8888
TYPE = "ssdb"

conn = None

def config(host, port, type = "ssdb"):
        global HOST, PORT, TYPE

        HOST = host
        PORT = port
        TYPE = type

transaction = False
commands = []

def begin():
        global transaction

        if transaction:
                return False

        transaction = True
        commands = []

def query(cmd, args):
        global transaction, commands

        if not transaction or "set" not in cmd:
                return commit(((cmd, args),))

        commands.append((cmd, args))

def commit(commands2 = None):
        global HOST, PORT, TYPE, conn, transaction, commands

        if not commands2:
                commands3 = commands
        else:
                commands3 = commands2

        if not conn or conn.closed():
                if TYPE == "ssdb":
                        conn = SSDB(HOST, PORT)
                else:
                        raise Exception("unsupport database.")

        ret = []

        for cmd, args in commands3:
                ret.append(conn.request(cmd, args))

        if commands2:
                if len(ret) == 1:
                        return ret[0]

                return ret

        transaction = False
        commands = []

def rollback():
        global transaction, commands

        transaction = False
        commands = []
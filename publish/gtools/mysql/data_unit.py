# -*- coding: utf8 -*-

import sys

def erl_str_to_list(erl_str):
        '''erlang结构(仅支持lisp，tuple)转成py列表'''
        symbols = {"}":"{", "]":"[", ">>":"<<"}
        str_symbols = {">":"<"}
        symbols_l = symbols.values()
        symbols_r = symbols.keys()

        str_symbols_l = str_symbols.values()
        str_symbols_r = str_symbols.keys()

        erl_str = erl_str.strip()
        arr = []
        arr_list = []
        last = 0
        index = 0
        str_flag = False

        def slice_str():
                this_v = erl_str[last+1:index]
                if this_v:
                        arr_list[-1].append(this_v)

        for c in erl_str:
                if c in str_symbols_l:
                    arr.append(c)
                    if not str_flag:
                            str_flag = True
                elif c in str_symbols_r:
                        if arr and arr[-1] == str_symbols[c]:
                                arr.pop()
                                if arr and arr[-1] not in str_symbols_r:
                                        str_flag = False

                elif not str_flag and c in symbols_l:
                        arr.append(c)
                        if c == "[":
                                arr_list.append([])
                        elif c == "{":
                                arr_list.append(["erl_tuple"])

                        last = index
                elif not str_flag and c in symbols_r:
                        if arr and arr[-1] == symbols[c]:
                                slice_str()
                                arr.pop()
                                v = arr_list.pop()
                                if arr:
                                        arr_list[-1].append(v)
                                else:
                                        return v
                        else:
                                return False
                        last = index
                elif not str_flag and c == ",":
                        slice_str()
                        last = index

                index += 1
        return arr_list[0]


def list_to_erl_str(py_list):
        '''python表示的erlang结构准换成字符串'''
        if py_list and type(py_list) == list:
                if py_list[0] == "erl_tuple":
                        return "{%s}"%(",".join([list_to_erl_str(v) for v in py_list[1:]]),)
                else:
                        return "[%s]"%(",".join([list_to_erl_str(v) for v in py_list]),)
        else:
                return "%s"%py_list


class DataUnit(object):
        is_display_sql = False

        def __init__(self,last_ver=0):
                super(DataUnit, self).__init__()
                self.last_ver = last_ver
                self.db_con = None
                self.cursor = None
                self.save_row_flag = 0
                self.all_table_keys = {}
                self.update_table_keys = {}
                self.modify_data = {} #{table:{id_1:row_1, id_2:row_2, ...}, ...}
                self.modify_sql_report = {} #{table:effect_rows}

                self.sql_merge_row = 200

        def set_connection(self, db_con):
                self.db_con = db_con

        def set_last_ver(self, last_ver):
                self.last_ver = last_ver

        def get_cursor(self):
                if not self.cursor:
                        self.cursor = self.db_con.cursor()
                return self.cursor

        def get_rows(self, table, fields="*", where=None):
                if type(fields) == list:
                        fields = ",".join(fields)

                sql = "SELECT %s FROM %s"%(fields, table)
                if where:
                    sql = sql + " WHERE %s"%where
                cursor = self.get_cursor()
                cursor.execute(sql)
                result = cursor.fetchall()

                if result:
                    return result
                else:
                    return []
        def show_table_keys(self, table):
                cursor = self.get_cursor()
                cursor.execute("desc %s"%table)
                return [row["Field"] for row in cursor.fetchall()]

        def save_row(self, table, row, id_key="id"):
                table_data = self.modify_data.get(table, {})

                table_data[row[id_key]] = row
                self.modify_data[table] = table_data

                self.save_row_flag += 1
                if self.save_row_flag >= self.sql_merge_row:
                        self.flush()
                        self.save_row_flag = 0

        def do_modify(self):
                pass

        def flush(self):
                '''批量update数据'''
                for table, rowdata in self.modify_data.items():
                        row_keys = rowdata.keys()
                        if row_keys:
                                table_keys = self.all_table_keys.get(table)
                                if not table_keys:
                                        table_keys = self.show_table_keys(table)
                                        self.all_table_keys[table] = table_keys
                                update_table_keys = self.update_table_keys.get(table)
                                if not update_table_keys:
                                        update_table_keys = rowdata[row_keys[0]].keys()
                                        self.update_table_keys[table] = update_table_keys

                                values = []
                                for id, row in rowdata.iteritems():
                                        values.append("(%s)"%(",".join(["'%s'"%row.get(row_k, 0) for row_k in table_keys ]),))


                                keys_str = "`%s`"%("`,`".join(table_keys),)
                                values_update = ",".join(["`%s`=VALUES(`%s`)"%(k,k) for k in update_table_keys if k!="id"])

                                sql = '''INSERT INTO %s (%s) VALUES %s ON DUPLICATE KEY
                                         UPDATE %s'''%(table, keys_str, ",".join(values), values_update)

                                cursor = self.get_cursor()

                                try:
                                        if DataUnit.is_display_sql:
                                            print "\n%s\n"%sql

                                        result = cursor.execute(sql)
                                except :
                                        print sql
                                        print "\nSql_len=%s\n"%(len(sql),)
                                        raise

                                #如修改成功插入的行数会是2倍
                                effect_rows = result/2 if type(result) == int else result
                                effect_rows = self.modify_sql_report.get(table, 0) + effect_rows
                                self.modify_sql_report[table] = effect_rows

                                del self.modify_data[table]

                self.save_row_flag = 0

        def do_upgrade(self):
                self.do_modify()
                self.flush()

                if self.cursor:
                        self.cursor.close()

                for table, effect_rows in self.modify_sql_report.iteritems():
                        print "update table=%s ,effect_rows=%s"%(table, effect_rows)

        def parse_erl(self, erl_str):
                '''erlang结构(仅支持lisp，tuple)转成py列表'''
                return erl_str_to_list(erl_str)

        def str_erlang(self, py_list):
                '''python表示的erlang结构准换成字符串'''
                return list_to_erl_str(py_list)

        def is_erl_tuple(self, py_list):
                if py_list and type(py_list) == list and py_list[0]=="erl_tuple":
                        return True
                else:
                        return False











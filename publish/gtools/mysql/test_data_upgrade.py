# -*- coding: utf8 -*-

from data_upgrade import DateUpgrade
from test_unit_v1 import DataUnitV1

def start_upgrade(host="",
                  port=3306,
                  user="",
                  passwd="",
                  db_name="",
                  old_ver=0,
                  new_ver=0
                  ):
        UpgradeTask = DateUpgrade(host=host,
                                  port=port,
                                  user=user,
                                  passwd=passwd,
                                  db_name=db_name)
        UpgradeTask.add_unit(DataUnitV1(last_ver=1))
        UpgradeTask.set_versions(old_ver, new_ver)
        UpgradeTask.do()

if __name__ == '__main__':
        start_upgrade(host="127.0.0.1",
                      port=3306,
                      user="root",
                      passwd="123456",
                      db_name="x1_sgame_0",
                      old_ver=1,
                      new_ver=2)



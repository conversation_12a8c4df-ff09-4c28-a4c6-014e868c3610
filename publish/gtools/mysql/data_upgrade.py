# -*- coding: utf8 -*-
import time
import pymysql

class DateUpgrade(object):
        def __init__(self, host=None, port=3306,user=None, passwd=None,db_name=None):
                super(DateUpgrade, self).__init__()
                self.data_unit_list = []
                self.db_con = None
                self.mysql_host = host
                self.mysql_port = port
                self.mysql_user = user
                self.mysql_passwd = passwd
                self.mysql_db = db_name

                self.old_ver = None
                self.new_ver = None

                self.create_con()


        def create_con(self):
                self.db_con = pymysql.connect(host=self.mysql_host,
                              port=self.mysql_port,
                              user=self.mysql_user,
                              password=self.mysql_passwd,
                              db=self.mysql_db,
                              cursorclass=pymysql.cursors.DictCursor,
                              charset="utf8"
                               )



        def add_unit(self, data_unit):
                self.data_unit_list.append(data_unit)

        def set_versions(self, old_ver,new_ver):
                self.old_ver = old_ver
                self.new_ver = new_ver


        def do(self):
                self.data_unit_list = sorted(self.data_unit_list, cmp = lambda x,y:cmp(x.from_ver, y.from_ver))
                T0 = time.time()
                try:
                        for unit in self.data_unit_list:
                                print "checking data unit:last_ver=%s,old_ver=%s,new_ver=%s"%(unit.last_ver,self.old_ver, self.new_ver)
                                if (self.old_ver <=  unit.last_ver) and (self.new_ver > unit.last_ver):
                                        T1 = time.time()
                                        unit.set_connection(self.db_con)
                                        unit.do_upgrade()
                                        print "execute upgrade done:last_ver=%s, spend=%s"%(unit.last_ver, time.time() - T1)


                        self.db_con.commit()
                finally:
                        self.db_con.close()
                        print "DataUpgrade FAIL!"

                print "execute all done, spend=%s"%(time.time()-T0,)





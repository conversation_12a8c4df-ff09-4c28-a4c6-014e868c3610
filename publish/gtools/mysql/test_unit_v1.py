# -*- coding: utf8 -*-
import time
from data_unit import DataUnit

class DataUnitV1(DataUnit):
        """docstring for DataUnitV1"""
        def __init__(self,last_ver=0):
                super(DataUnitV1, self).__init__(last_ver)

        def do_modify(self):
                #整表修改
                for row in self.get_rows("account"):
                        row["auth_chat"] = row["auth_chat"] + 1
                        self.save_row("account", row)

                #部分数据修改
                for row in self.get_rows("friend", where = "id < 20000"):
                        row["own"] = time.time()
                        self.save_row("friend", row)

                #修改背包数据
                for row in self.get_rows("bag", fields=["id","grids"]):
                        v = self.parse_erl(row["grids"])

                        for item in v:
                            if self.is_erl_tuple(item) and  item[1] == "item":
                                    item[-1] = int(time.time())

                        row["grids"] = self.str_erlang(v)
                        self.save_row("bag", row)



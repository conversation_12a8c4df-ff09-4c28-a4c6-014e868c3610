# -*- coding: utf8 -*-

import os
import re
import sys
import platform
import time
import traceback
import pdb

import gtools.util.string2 as string2
import gtools.util.os2     as os2
import gtools.util.file    as file

def modifyPackage(in_dir, ou_dir, from_package = None, to_package = None, fix = None, skip_files = None):
        split_char = string2.dir_split_char()

        if not from_package or not to_package:
                from_package = in_dir.split(split_char)[-1]
                to_package = ou_dir.split(split_char)[-1]

        for root, dirs, files in os.walk(in_dir):
                for filename in files:
                        fileext = filename.split(".")[-1]
                        if fileext != "as" and fileext != "mxml":
                                continue

                        in_full_file = root + split_char + filename
                        ou_full_dir = ou_dir + root.replace(in_dir, "")
                        ou_full_file = ou_full_dir + split_char + filename

                        script = file.read_file(in_full_file)

                        if skip_files is None or in_full_file not in skip_files:
                                script = string2.str_repl(r"[^\w\"]{1}%s[^\w]{1}" % (from_package,), script, "%s" % (to_package,), (1,1))

                                if fix:
                                        script = fix(filename, script)

                        if not os.path.exists(ou_full_dir):
                                os2.system_mkdir(ou_full_dir)

                        file.write_file(ou_full_file, script)

#modifyPackage("G:\\svn\\engine\\trunk\\client\\framework\\asgame\\src\\asgame", "asgameLA")
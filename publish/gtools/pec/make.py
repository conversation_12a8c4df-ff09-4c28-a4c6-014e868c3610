# -*- coding: utf8 -*-

import os
import re
import sys
import platform
import time
import traceback
import pdb
import json

import compile

import gtools.util.string2 as string2
import gtools.util.os2     as os2
import gtools.util.file    as file

import config

def parse_src_dirs(release = False):
        makefile = "Emakefile_d"

        if release:
                makefile = "Emakefile_r"

        Emakefile = file.read_file(makefile)

        src_dirs = re.findall("\[[^\]]*\]", Emakefile)[0]
        src_dirs = eval(string2.str_wipe_break(string2.str_wipe_space(src_dirs)))

        src_dirs2 = []
        for src_dir in src_dirs:
                src_dir = src_dir.replace("/*", "")
                if src_dir not in src_dirs2:
                        src_dirs2.append(src_dir)

        return src_dirs2

def erl_make(release = False, cmd = "erl -make"):
        makefile = "Emakefile_d"

        if release:
                makefile = "Emakefile_r"

        os2.system_cp(makefile, "Emakefile")
        content = os2.psystem(cmd)
        os2.system_rm("Emakefile")

        return content

def compile2(release = False, src_dirs = None):
        if os.path.exists("Pmakefile"):
                return compile.compile(srcs = src_dirs)

        makefile = "Pmakefile_d"

        if release:
                makefile = "Pmakefile_r"

        os2.system_cp(makefile, "Pmakefile")
        success = compile.compile(srcs = src_dirs)
        os2.system_rm("Pmakefile")

        return success

def has_error(content, release):
        error_flags = []
        error_flags.extend(config.ERROR_FLAGS)

        # if not release:
        #         error_flags.append("Warning")

        for flag in error_flags:
                if content.find(flag) != -1:
                        return True

        return False

def update_module_line(content):
        module_lines = re.findall("\n[\w|/|.]+:\d+:", content)

        for module_line in module_lines:
                module_line = module_line[1:-1]

                tokens = module_line.split(":")

                module = tokens[0].split("/")[-1][0:-4]
                line   = int(tokens[1])

                extends_module_line = compile.extends_module_line(module, line)

                if not extends_module_line:
                        continue

                if tokens[0] == extends_module_line[1]:
                        new_module_line = "%s:%s" % (tokens[0], extends_module_line[2])
                else:
                        new_module_line = "%s:(%s:%s)" % (tokens[0], extends_module_line[1], extends_module_line[2])

                content = content.replace(module_line, new_module_line)

        return content

def make(program_dir, release = False, force_del_beam = False, make_cmd = "erl -make", dialyze = False, plt = ""):
        work_dir = os.getcwd()

        os.chdir(program_dir)

        if force_del_beam:
                del_beam = True
        else:
                ret = raw_input("Do you need incremental compilation? (y/n)\n")

                del_beam = (not len(ret)) or (ret[0] not in ("Y", "y"))

        print "del_beam ==>", del_beam
        
        if del_beam:
                os2.system_rm("%s/*.beam" % (config.EBIN_DIR))

        compile.reset()
        compile.clean_all(True)
        
        if config.CHECKING:
                #首次编译查错
                print "erlang start checking..."

                T0 = time.time()

                content = erl_make(release, "erl -make")

                T1 = time.time()

                #输出编译信息
                print content
                print "erlang end checking.\n"

                #有错误,退出
                if has_error(content, release):
                        os.chdir(work_dir)

                        return False
        else:
                T0 = T1 = time.time()

        print "PEC start compiling..."

        src_dirs = parse_src_dirs(release)
        
        success = compile2(release, src_dirs)

        T2 = time.time()

        if not success:
                os.chdir(work_dir)

                return False

        print "PEC end compiling."

        if dialyze:
                print "PEC start dialyzing..."

                try:
                        makefile = "Emakefile_d"

                        if release:
                                makefile = "Emakefile_r"

                        makefile = file.read_file(makefile)

                        erls = re.findall("\[[\s\S]+\],", makefile)[0]
                        erls = string2.str_wipe_break(erls)
                        erls = string2.str_wipe_space(erls)
                        erls = erls.replace("'", "")
                        erls = erls.replace("*", "*.erl")
                        erls = erls.replace(",", " ")
                        erls = erls[1:-2]

                        incs = re.findall("\{i,\s*\".*\"\}", makefile)
                        incs2 = ""
                        for inc in incs:
                                incs2 += "-I %s " % (string2.str_wipe_space(inc)[4:-2])

                        if plt != "":
                                diaz_cmd = "dialyzer --plt %s --src %s-c %s" % (plt, incs2, erls)
                        else:
                                diaz_cmd = "dialyzer --src %s-c %s" % (incs2, erls)

                        diaz_ret = os2.psystem(diaz_cmd)

                        diaz_ret = string2.str_repl("\n.+The .+ can never match the .+", diaz_ret, "")
                        diaz_ret = string2.str_repl("\n.+The .+ can never match since previous clauses completely covered the .+", diaz_ret, "")
                        diaz_ret = string2.str_repl("\n.+does not have an opaque term of type dict\:dict\(_,_\) as .+", diaz_ret, "")

                        open("dialyze.log", "w").write(diaz_ret)
                except:
                        traceback.print_exc()

                        if __debug__:
                                pdb.post_mortem()

                print "PEC end dialyzing."

        print "erl start making..."

        #重编译改变的模块
        content = erl_make(release, make_cmd)

        T3 = time.time()

        #输出编译信息
        print update_module_line(content)
        print "erl end making."

        compile.restore()
        compile.clean_all()

        if has_error(content, release):
                return False
                
        os.chdir(work_dir)

        print "T1(erl)=%s\nT2(py)=%s\nT3(erl)=%s\nTTotal=%s"%(T1-T0, T2-T1, T3-T2, T3-T0)

        return True

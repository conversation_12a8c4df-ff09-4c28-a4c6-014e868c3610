# -*- coding: utf8 -*-

import os
import platform
import copy
import random
import re
import cPickle
import sys
import traceback
import pdb

import gtools.util.string2 as string2
import gtools.util.os2     as os2
import gtools.util.encrypt as encrypt
import gtools.util.file    as file

import config

makefile = {}

modules = {}

module_srcs = {}

module_seq = "--"

module_extends_map = {}

extended_modules = []

backup_erls = []

rename_count = -1

module_line_map = {}

mlmap_info = [None, ""]

keep_mlmap = True

def reset():
        global makefile, modules, module_srcs, module_seq, module_extends_map, \
               extended_modules, backup_erls, rename_count, module_line_map, mlmap_info

        makefile.clear()

        modules.clear()

        module_srcs.clear()

        module_seq = "--"

        module_extends_map.clear()

        extended_modules = []

        backup_erls = []

        rename_count = 0

        module_line_map.clear()

        mlmap_info[0] = None
        mlmap_info[1] = ""

#统计参数数目
def stat_argn(args):
        if args == "":
                return 0

        args = string2.str_repl("%<.*>%", args, "")
        args = string2.str_repl2("\[[^\[\]]*\]", args, "t")
        args = string2.str_repl("<<.*>>", args, "t")
        args = string2.str_repl("[(,]\w*=#\w*\{", args, "", 1)
        args = string2.str_repl2("\{[^\{\}]*\}", args, "t")
        args = string2.str_repl("[(,]\w*\(", args, "", 1)
        args = string2.str_repl2("\([^\(\)]*\)", args, "t")
        args = string2.str_repl2("'[^']*'", args, "t")
        args = string2.str_repl(",", args, "','")
        args = "['" + args + "']"

        args = eval(args)

        return len(args)

def gen_fun_key(fun_info):
        fun_info = string2.str_wipe_break(fun_info)
        fun_info = string2.str_wipe_space(fun_info)

        open_idx = fun_info.find("(")

        fun_name = fun_info[0:open_idx]
        fun_args = fun_info[open_idx + 1:-1]
        fun_args = string2.str_repl("when[\s\S]*", fun_args, "")

        fun_argn = stat_argn(fun_args)

        return fun_name + "/" + str(fun_argn), fun_name, fun_argn

#更新脚本中函数引用
def replace_script_fun_ref(script, old_fun_name, new_fun_name, fun_argn):
        out_script = script

        for curr_ref_fun_info in re.findall("[^:\w]{1,1}%s\(.*\)" % (old_fun_name,), script):
                curr_ref_fun_info2 = string2.str_wipe_break(curr_ref_fun_info)
                curr_ref_fun_info2 = string2.str_wipe_space(curr_ref_fun_info2)

                curr_fun_key, curr_fun_name, curr_fun_argn = gen_fun_key(curr_ref_fun_info2)

                if curr_fun_argn == fun_argn:
                        new_ref_fun_info = curr_ref_fun_info.replace(old_fun_name + "(", new_fun_name + "(")

                        out_script = out_script.replace(curr_ref_fun_info, new_ref_fun_info)

        return out_script

#更新模块函数引用
def update_module_fun_ref(module, old_fun_name, new_fun_name, fun_argn):
        global modules

        old_fun = old_fun_name + "/" + str(fun_argn)
        new_fun = new_fun_name + "/" + str(fun_argn)

        module_fun_decl = module[-3]
        module_fun_dict = module[-2]

        if not module_fun_dict.has_key(old_fun):
                return

        if old_fun in module_fun_decl:
                index = module_fun_decl.index(old_fun)

                module_fun_decl[index] = new_fun

        fun_script = module_fun_dict[old_fun]

        del module_fun_dict[old_fun]

        module_fun_dict[new_fun] = replace_script_fun_ref(fun_script, old_fun_name, new_fun_name, fun_argn)

#扩展
def extends_module(module):
        global modules, module_extends_map, extended_modules, rename_count

        if (not module_extends_map.has_key(module)) or \
           (module in extended_modules):
                return

        child   = module
        parents = module_extends_map[module]

        r_parents = []
        r_parents.extend(parents)

        r_parents.reverse()

        for parent in r_parents:
                extends_module(parent)

        child_module = modules[child]

        child_fun_decl = child_module[-3]
        child_fun_dict = child_module[-2]
        child_rcd_dict = child_module[-1]

        parent_module = copy.deepcopy(modules[parents[0]])

        parent_fun_decl = parent_module[-3]
        parent_fun_dict = parent_module[-2]
        parent_rcd_dict = parent_module[-1]

        rename_map = {}

        #扩展函数
        for fun_key in child_fun_dict.iterkeys():
                fun_script = child_fun_dict[fun_key]

                #查找是否引用父模块函数
                for parent in parents:
                        script = fun_script

                        while True:
                                ref_idx = script.find(parent + ":")

                                if ref_idx == -1:
                                        break

                                chk_char = script[ref_idx - 1]

                                script = script[(ref_idx + len(parent)):len(script)]

                                #排除前一个字符
                                if string2.is_exc_char(chk_char):
                                        continue

                                def search_close(script):
                                        open_idx = script.find("(")

                                        return string2.str_repl3("\([^\(\)]*\)", script[open_idx+1:],
                                                                 "*", (0,0), True).find(")") + open_idx + 2

                                #确认有引用父模块函数
                                #查找函数名
                                ref_fun_info = script[1:search_close(script)]

                                ref_fun_key, ref_fun_name, ref_fun_argn = gen_fun_key(ref_fun_info)

                                if child_fun_dict.has_key(ref_fun_key):
                                        #函数重名，表示子模块已重载父模块函数
                                        #此时将父模块函数更名，重新定义于子模块中，且不用导出
                                        if rename_map.has_key((parent, ref_fun_key)):
                                                override_fun_name = rename_map[(parent, ref_fun_key)]
                                        else:
                                                rename_count += 1
                                                override_fun_name = ref_fun_name + config.FUN_RENAME_SPACE + str(rename_count)

                                                rename_map[(parent, ref_fun_key)] = override_fun_name

                                        override_fun_key  = override_fun_name + "/" + str(ref_fun_argn)

                                        override_fun_info = override_fun_name + ref_fun_info[ref_fun_info.find("("):len(ref_fun_info)]

                                        #修改函数名
                                        update_module_fun_ref(parent_module, ref_fun_name, override_fun_name, ref_fun_argn)

                                        fun_script = fun_script.replace(parent + ":" + ref_fun_info, override_fun_info)
                                else:
                                        fun_script = fun_script.replace(parent + ":" + ref_fun_info, ref_fun_info)

                #更新为修改以后的脚本
                child_fun_dict[fun_key] = fun_script

        #更新结构
        for rcd_name, rcd_content in parent_rcd_dict.iteritems():
                if not child_rcd_dict.has_key(rcd_name):
                        child_rcd_dict[rcd_name] = []

                for element in rcd_content:
                        if element not in child_rcd_dict[rcd_name]:
                                child_rcd_dict[rcd_name].append(element)

        #更新函数导出声明
        for fun_decl in parent_fun_decl:
                if fun_decl not in child_fun_decl:
                        child_fun_decl.append(fun_decl)

        #更新函数字典
        for fun_key, fun_script in parent_fun_dict.iteritems():
                if not child_fun_dict.has_key(fun_key):
                        child_fun_dict[fun_key] = fun_script

        #更新export信息
        export_info = "-export(["

        fun_decl_num = len(child_fun_decl)

        for i in range(fun_decl_num):
                fun_decl = child_fun_decl[i]

                export_info += fun_decl

                if i < fun_decl_num - 1:
                        export_info += ","
                else:
                        export_info += "])." + string2.line_break_char()

        child_module.insert(1, export_info)

        rset = 2

        rcd_num = len(child_rcd_dict.keys())

        for i in range(rcd_num):
                rcd_name    = child_rcd_dict.keys()[i]
                rcd_content = child_rcd_dict[rcd_name]

                record_info = "-record(" + rcd_name + ", {" + ",".join(rcd_content) + "})." + string2.line_break_char()

                child_module.insert(rset, record_info)

                rset += 1

        for i in range(len(parent_module) - 4):
                parent_line = parent_module[i + 1]

                if parent_line.find("-export(") != -1 or \
                   parent_line.find("-record") != -1:
                        continue

                child_module.insert(rset, parent_line)

                rset += 1

        extended_modules.append(child)

def judge_if_line_valid(line):
        line2 = string2.str_wipe_space(line)

        return len(line2) != 0 and line2[0] != "%"

def judge_if_section_end(line):
        line = string2.str_wipe_space(line)

        line = string2.str_repl("'[^']*'", line, "")
        line = string2.str_repl("\"[^\"]*\"", line, "")
        line = string2.str_repl("%.*", line, "")

        return len(line) and line[-1] == "."

def concat_section_headline(section):
        new_section = []

        line_num = len(section)

        if section[0][0][0] == "-":
                end_char = "."
        else:
                end_char = ">"

        head_line_no = section[0][1]

        head_line = ""

        index = 0

        while index < line_num:
                line, no = section[index]

                index += 1

                line2 = string2.str_wipe_comments(line)
                line2 = string2.str_wipe_border_space(line2)

                head_line += line2

                last_char = line2[-1]

                #非逗号或连接符，首行结束
                if last_char == end_char:
                        break

                if last_char == "\\":
                        #去掉连接符
                        head_line = head_line[0:-1]

        for i in range(index):
                section.pop(0)

        section.insert(0, (head_line, head_line_no))

def is_lib(src):
        return len(src) > 4 and src[-4:len(src)] == ".lib"

def is_mlmap(src):
        return src.split(string2.dir_split_char())[-1].split(".")[0] == config.MLMAP

def compile_module(src):
        global modules, module_srcs, module_seq, mlmap_info

        fcontent = file.read_file(src)

        if is_lib(src):
                fcontent = encrypt.decode(fcontent, config.ENCRYPT_KEY)

        lines = fcontent.split("\n")

        if is_mlmap(src):
                mlmap_info[0] = {}
                for line in lines[3:-1]:
                        module = line.split(",")[0][4:]
                        if not mlmap_info[0].has_key(module):
                                mlmap_info[0][module] = []
                        mlmap_info[0][module].append(line)

                return True, None

        sections = []

        section = []

        for line_no in range(len(lines)):
                line = lines[line_no]

                line2 = string2.str_wipe_break(line)

                if judge_if_line_valid(line2):
                        section.append((line2, line_no))

                        if judge_if_section_end(line2):
                                concat_section_headline(section)

                                sections.append(section)

                                section = []

        sem_list = []
        fun_dict = {}
        fun_expt = []
        rcd_dict = {}

        cmodule = ""
        pmodule = None

        for section in sections:
                is_fun_decl = False

                for line, no in section:
                        line2 = line.replace(" ", "")

                        if not len(line2):
                                continue

                        if line2.find("-module") != -1:
                                if cmodule != "":
                                        continue

                                cmodule = line2[8:-2]

                                sem_list.append(line + string2.line_break_char())

                                if modules.has_key(cmodule):
                                        print "duplicate module %s." % (cmodule,)
                                        
                                        return False, None
                                        
                                modules[cmodule] = sem_list

                                module_srcs[cmodule] = src

                                #添加到模块序列表
                                module_seq += cmodule + "--"
                        elif line2.find("-extends") != -1:
                                pmodule = line2[9:-2]

                                print "compile -extends: %s(%s)." % (cmodule, pmodule)

                                #更新继承关系
                                module_seq = string2.str_repl("[-,\,]%s[-,\,]" % (cmodule,), module_seq, cmodule + "," + pmodule, 1)
                        elif line2.find("-export(") != -1:
                                str_fun_expt = ""

                                for line3, no in section:
                                        str_fun_expt += line3.replace(" ", "")

                                fun_expt.extend(str_fun_expt[9:-3].split(","))
                        elif line2.find("-record") != -1:
                                str_record = line2[8:-2]

                                split_idx = str_record.find(",")

                                rcd_name    = str_record[0:split_idx]
                                rcd_content = str_record[(split_idx + 1):len(str_record)]

                                if rcd_content == "{}":
                                        rcd_content = "[]"
                                else:
                                        kvs = []
                                        temp = []
                                        i = 0
                                        for erl_list in re.findall("\[.*\]", rcd_content):
                                                if erl_list not in temp:
                                                        k = "erl_list%s" % (i,)
                                                        kvs.append((k,erl_list))
                                                        rcd_content = rcd_content.replace(erl_list, k)
                                                        temp.append(erl_list)
                                        for erl_tuple in re.findall("\{.*\}", rcd_content[1:-1]):
                                                if erl_tuple not in temp:
                                                        k = "erl_tuple%s" % (i,)
                                                        kvs.append((k,erl_tuple))
                                                        rcd_content = rcd_content.replace(erl_tuple, k)
                                                        temp.append(erl_tuple)
                                        kvs.reverse()
                                        rcd_content = rcd_content.replace("{", "['")
                                        rcd_content = rcd_content.replace("}", "']")
                                        rcd_content = rcd_content.replace(",", "','")
                                        for k, v in kvs:
                                                rcd_content = rcd_content.replace(k, v)

                                rcd_dict[rcd_name] = eval(rcd_content)
                        elif line2.find("-define") != -1 or \
                             line2.find("-include") != -1:
                                sem_list.append(line2 + string2.line_break_char())
                        elif not is_fun_decl:
                                if len(re.findall("\w+\(.*\)->|\w+\(.*\)when.+->", line2)):
                                        is_fun_decl = True

                                        fun_key, fun_name, fun_argn = gen_fun_key(line2[0:line2.find(re.findall("\)->|\)when.+->", line2)[0]) + 1])

                                        fun_script = ""

                                        for line3, no in section:
                                                fun_script += string2.str_combin_spaces(string2.str_count_space(line3)) + \
                                                              "%<source: " + src +":" + str(no + 1) + ">%" + \
                                                              string2.line_break_char() + line3 + string2.line_break_char()

                                        fun_dict[fun_key] = fun_script

                                        break

                                content = ""

                                for line3, no in section:
                                        content += line3 + string2.line_break_char()

                                sem_list.append(content)
                        else:
                                pass

        sem_list.append(fun_expt)
        sem_list.append(fun_dict)
        sem_list.append(rcd_dict)

        if cmodule == "":
                raise Exception("invalid code file")

        return True, (cmodule, pmodule)

def modify(module):
        global modules, module_srcs, backup_erls, module_line_map, mlmap_info

        module_src = module_srcs[module]

        #备份
        bak_module_src = module_src + ".bak"

        os2.system_cp(module_src, bak_module_src)

        #记录备份信息
        backup_erls.append((module_src, bak_module_src))

        #修改
        fp = open(module_src, "w")

        fp.write("%%auto generated by extends make" + string2.line_break_char())

        out_line_no = 1

        line_num = len(modules[module])

        for line_idx in range(line_num - 1):
                line = modules[module][line_idx]

                if type(line) == type(""):
                        fp.write(line)

                        out_line_no += 1
                elif type(line) == type({}):
                        if mlmap_info[0] is None:
                                mlmap_info[0] = {}
                        mlmap_info[0][module] = []

                        fp.write(string2.line_break_char())

                        out_line_no += 1

                        for key, content in line.iteritems():
                                script_lines = content.split(string2.line_break_char())[0:-1]

                                for i in range(len(script_lines) / 2):
                                        comment = script_lines[i * 2]
                                        script  = script_lines[i * 2 + 1]

                                        out_line_no += 2

                                        comment = string2.str_wipe_space(comment)[9:-2]

                                        filename, line_no = comment.split(":")

                                        module_line_map[(module, out_line_no)] = ("get(%s, %s) -> {%s, [{file, \"%s\"}, {line, %s}]};" % \
                                                                                  (module, out_line_no, filename.split(string2.dir_split_char())[-1][0:-4], 
                                                                                   filename, line_no))
                                        mlmap_info[0][module].append(module_line_map[(module, out_line_no)])

                                fp.write(content + string2.line_break_char())

                                out_line_no += 1

        fp.close()

        #删除beam文件
        os2.system_rm(config.EBIN_DIR + string2.dir_split_char() + module + ".beam")

def restore():
        global backup_erls

        for module_src, bak_module_src in backup_erls:
                if config.OUTPUT_O and (not is_lib(module_src)):
                        os2.system_cp(module_src, module_src[0:-4] + "_o.erl")

                os2.system_rm(module_src)

                #恢复文件
                os2.system_cp(bak_module_src, module_src)

                os2.system_rm(bak_module_src)

def gen_mlmap_code():
        global module_srcs, module_line_map, mlmap_info

        code  = "-module(mlmap)." + string2.line_break_char()
        code += "-export([fun_rename_space/0, get/2])." + string2.line_break_char()
        code += "fun_rename_space() -> \"" + config.FUN_RENAME_SPACE + "\"." + string2.line_break_char()

        if mlmap_info[1] == "":
                for module, lines in mlmap_info[0].iteritems():
                        mlmap_info[1] += "\n".join(lines) + "\n"

        code += mlmap_info[1]
        code += "get(_Module, _Line) -> undef."
        
        return code

def export_libs():
        global makefile, module_srcs, backup_erls

        if not makefile.has_key("export_libs"):
                return False

        os2.system_rm(config.LIB_DIR + string2.dir_split_char() + "*.lib")

        makefile["export_libs"].append(config.MLMAP)

        for module in makefile["export_libs"]:
                if module == config.MLMAP:
                        source = gen_mlmap_code()

                        source = encrypt.encode(source, config.ENCRYPT_KEY)
                else:
                        module_src = module_srcs[module]

                        bak_module_src = module_src + ".bak"

                        if (module_src, bak_module_src) in backup_erls:
                                source = file.read_file(bak_module_src)
                        else:
                                source = file.read_file(module_src)

                        if not is_lib(module_src):
                                source = encrypt.encode(source, config.ENCRYPT_KEY)

                file.write_file(config.LIB_DIR + string2.dir_split_char() + module + ".lib", source)

        return True

def gen_mlmap_file():
        file.write_file(config.SRC_DIR + string2.dir_split_char() + config.MLMAP + ".erl", gen_mlmap_code())

def clean_all(rm_o = False):
        global keep_mlmap

        for root, dirs, files in os.walk(config.SRC_DIR):
                for filename in files:
                        full_path = root + string2.dir_split_char() + filename

                        if os.path.isfile(full_path) and \
                           full_path.find(".svn") == -1:
                                if (not keep_mlmap) and full_path.find(config.MLMAP + ".erl") != -1:
                                        os2.system_rm(full_path)
                                elif full_path.find("_o.erl") != -1:
                                        if rm_o:
                                                os2.system_rm(full_path)
                                        elif not config.OUTPUT_O:
                                                os2.system_rm(full_path)
                                        else:
                                                pass
                                elif full_path.find(".erl.bak") != -1:
                                        src_file = full_path[0:-4]

                                        os2.system_rm(src_file)
                                        os2.system_mv(full_path, full_path[0:-4])
                                else:
                                        pass

def is_erl(src):
        if len(src) <= 4:
                return False

        ext_name = src[-4:len(src)]

        return ext_name == ".erl" or ext_name == ".lib"

def compile(modules = [], srcs = []):
        global makefile, module_srcs, module_seq, module_extends_map, backup_erls, module_line_map, mlmap_info, keep_mlmap

        try:
                makefile = eval(string2.str_wipe_break(string2.str_wipe_space(file.read_file("Pmakefile"))))

                import_lib_dir = makefile.get("import_lib_dir", "")
                import_bin_dir = makefile.get("import_bin_dir", "")

                keep_mlmap = makefile.get("keep_mlmap", True)
        except:
                traceback.print_exc()

                if __debug__:
                        pdb.post_mortem()

                return False

        def compile2(full_path):
                if os.path.isfile(full_path) and full_path.find(".svn") == -1:
                        if not is_erl(full_path):
                                return True, None
                                
                        try:
                                return compile_module(full_path)
                        except:
                                traceback.print_exc()

                                if __debug__:
                                        pdb.post_mortem()

                                restore()

                                return False, None
                else:
                        return True, None

        def walk(dir):
                def walk_dir(dir, fun):
                        modules = {}
                        if type(dir) is str:
                                for root, dirs, files in os.walk(dir):
                                        for filename in files:
                                                full_path = root + string2.dir_split_char() + filename

                                                ret, module = fun(full_path)

                                                if ret:
                                                        if module is not None:
                                                                modules[module[0]] = module[1]
                                                else:
                                                        return False, None
                        else:
                                for dir2 in dir:
                                        for filename in os.listdir(dir2):
                                                full_path = os.path.abspath(dir2) + string2.dir_split_char() + filename

                                                ret, module = fun(full_path)

                                                if ret:
                                                        if module is not None:
                                                                modules[module[0]] = module[1]
                                                else:
                                                        return False, None

                        return True, modules

                return walk_dir(dir, compile2)

        src_dir = config.SRC_DIR

        if len(srcs):
                src_dir = srcs

        ret, make_modules = walk(src_dir)

        if not ret:
                return False

        mlmap_erl = os.path.abspath(config.SRC_DIR) + string2.dir_split_char() + config.MLMAP + ".erl"

        if mlmap_info[0] is None and os.path.exists(mlmap_erl):
                compile2(mlmap_erl)

        def walk_libs(import_lib_dir, make_modules):
                lib_modules2 = []
                for cmodule, pmodule in make_modules.iteritems():
                        if pmodule is not None and not make_modules.has_key(pmodule) and pmodule not in lib_modules2:
                                lib_modules2.append(pmodule)

                if not len(lib_modules2):
                        return True

                for module in lib_modules2:
                        lib_module = os.path.abspath(import_lib_dir) + string2.dir_split_char() + module + ".lib"
                        if not os.path.exists(lib_module):
                                print "dependency lib %s not exists" % (lib_module,)

                                return False

                        ret, module = compile2(lib_module)
                        
                        if ret:
                                if module is not None:
                                        make_modules[module[0]] = module[1]
                        else:
                                return False

                return walk_libs(import_lib_dir, make_modules)

        if import_lib_dir != "":
                if not walk_libs(import_lib_dir, make_modules):
                        return False

                #导入so文件
                os2.system_cp(import_lib_dir + string2.dir_split_char() + "*.so", config.LIB_DIR + string2.dir_split_char())

                if mlmap_info[0] is None:
                        mlmap_lib = os.path.abspath(import_lib_dir) + string2.dir_split_char() + config.MLMAP + ".lib"

                        if os.path.exists(mlmap_lib):
                                compile2(mlmap_lib)

        module_seq = module_seq[2:len(module_seq)]

        module_families = module_seq.split("--")

        for module_family in module_families:
                family = module_family.split(",")

                generation = len(family)

                if generation > 1 and family[0] in make_modules:
                        module_extends_map[family[0]] = family[1:generation]

        if modules == []:
                for module in module_extends_map.keys():
                        try:
                                print "make -extends: %s(%s)" % (module, ",".join(module_extends_map[module]))

                                extends_module(module)

                                modify(module)
                        except:
                                traceback.print_exc()

                                if __debug__:
                                        pdb.post_mortem()

                                restore()

                                return False

                if (not export_libs()) or keep_mlmap:
                        gen_mlmap_file()

                if import_bin_dir != "":
                        #导入beam文件
                        os2.system_cp(import_bin_dir + string2.dir_split_char() + "*.beam", config.EBIN_DIR + string2.dir_split_char())
        else:
                for module_compile in modules:
                        for module in module_extends_map[module_compile]:
                                try:
                                        print "make -extends: %s(%s)" % (module, ",".join(module_extends_map[module]))

                                        extends_module(module)

                                        modify(module)
                                except:
                                        traceback.print_exc()

                                        restore()

                                        return False

                        extends_module(module_compile)

                        modify(module_compile)

        return True

def extends_module_line(module, line):
        global module_line_map

        return module_line_map.get((module, line), None)
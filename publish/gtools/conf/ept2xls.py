# -*- coding: utf-8 -*-

import os
import re
import io
import sys
import codecs
import csv
import glob
import cStringIO
from collections import OrderedDict

import xlwt

import xlsx_lib

import gtools.util.string2 as string2

EPT_DIR = ""
XLS_DIR = ""

DIR_SPLIT_CHAR = string2.dir_split_char()

def get_module(text):
        return re.findall(r"-module\((\w+)\).", text)[0]

def get_fields(text):
        fields = OrderedDict()

        for m in re.finditer(r"\%+(\w+)\s*:\s*(\w+)\s*:(.+)", text):
                key = m.group(1)
                type = m.group(2)
                name = m.group(3)

                fields[key] = (type, name)

        return fields

def get_vars(text):
        varis = set()

        for m in re.finditer(r"\$(\w+)", text):
                varis.add(m.group(1))

        return varis

def gen_csv(text):
        module = get_module(text)
        fields = get_fields(text)
        varis = get_vars(text)

        cont = []
        cont.append([u"ERL_FILE_NAME", "%s.erl" % module])
        cont.append([u"CSV_FILE_NAME"])
        cont.append([])

        keys = fields.keys() + [v for v in varis if v not in fields.keys()]
        types = [type for type, _ in fields.values()]
        names = [name for _, name in fields.values()]

        cont.append([u"OUT_ERL"] + ["YES"] * len(keys))
        cont.append([u"OUT_CSV"])
        cont.append([u"NAME"] + names)
        cont.append([u"KEY"] + keys)
        cont.append([u"TYPE"] + types)

        return cont

def make_xls(filename):
        global XLS_DIR, DIR_SPLIT_CHAR

        print("make %s..." % (filename,))

        with io.open(filename, encoding='utf8') as fp:
                cs = gen_csv(fp.read())

        xlsx_lib.cover_header(XLS_DIR + DIR_SPLIT_CHAR + filename.split(DIR_SPLIT_CHAR)[-1][0:-4] + ".xls", cs)

        print("ok.")

def export(ept_dir, xls_dir):
        global EPT_DIR, XLS_DIR, DIR_SPLIT_CHAR

        EPT_DIR = ept_dir
        XLS_DIR = xls_dir

        for fn in glob.glob("%s%s*.ept" % (EPT_DIR, DIR_SPLIT_CHAR)):
                make_xls(fn)

if __name__ == "__main__":
        export(".", ".")
# -*- coding: utf-8 -*-

import os

from collections import OrderedDict
import xlrd
import xlwt

class Xlsx(object):
        """解析Excel文件"""
        def __init__(self, filename=None):
                super(Xlsx, self).__init__()
                if filename:
                        self.xlsx_filename = filename
                        self.book = xlrd.open_workbook(filename)
                self.erl_filename = None
                self.csv_filename = None
                self.no_warning = None
                self.erl_mode = "RECORD"
                self.erl_template = None
                self.erl_id = None
                self.erl_index = set()
                self.csv_id = None
                self.merge = False
                self.sheet_as_col = None
                self.keys = []
                self.data = []
                self.types = dict()
                self.out_erl = set()
                self.out_csv = set()

        def use_sheet(self, sheet):
                if isinstance(sheet, xlrd.sheet.Sheet):
                        self.sheet = sheet
                elif isinstance(sheet, int):
                        self.sheet = self.book.sheets()[sheet]
                else:
                        raise Exception("Invalid sheet %s" % sheet)

        def parse(self, sheet=None):
                if not sheet:
                        sheet = self.sheet
                else:
                        self.xlsx_filename = sheet.xlsx_filename

                checker_py = "%s_chk.py" % (self.xlsx_filename[0:-4],)

                if os.path.exists(checker_py):
                        execfile(checker_py)

                        if "check" in dir():
                                check_fun = locals()["check"]
                        else:
                                check_fun = None
                else:
                        check_fun = None

                sheet_name = sheet.name
                sheet_data = []
                sheet_keys = []
                sheet_types = []
                sheet_index = []
                sheet_merge = self.merge  # 使当前页的MERGE不影响本页读取

                # 开始读表
                for r in range(len(sheet.col(0))):
                        row = sheet.row(r)
                        if len(row) < 2:
                                continue

                        tag = row[0].value
                        if tag == "":
                                continue

                        rowb = row[1].value

                        if not self.merge:  # 对于MERGE模式, 这部分仅读首页的信息
                                if tag == 'CSV_FILE_NAME' and rowb:
                                        self.csv_filename = rowb
                                elif tag == 'ERL_FILE_NAME' and rowb:
                                        self.erl_filename = rowb
                                elif tag == 'MERGE' and rowb == "YES":
                                        sheet_merge = True
                                elif tag == 'SHEET_AS_COL' and rowb:
                                        self.sheet_as_col = rowb
                                        self.erl_index.add(rowb)
                                elif tag == 'ERL_MODE' and rowb:
                                        self.erl_mode = rowb
                                elif tag == 'ERL_TEMPLATE' and rowb:
                                        self.erl_template = rowb
                                elif tag == 'ERL_ID' and rowb:
                                        self.erl_id = rowb
                                elif tag == 'CSV_ID' and rowb:
                                        self.csv_id = rowb

                                elif tag == 'NO_WARNING' and rowb == 'YES':
                                        self.no_warning = True

                        # 无论Merge与否都要读的:
                        if tag == 'KEY':
                                sheet_keys = [c.value for c in row[1:]]
                        elif tag == 'OUT_ERL':
                                out_erl = row[1:]
                        elif tag == 'OUT_CSV':
                                out_csv = row[1:]
                        elif tag == 'TYPE':
                                sheet_types = [c.value for c in row[1:]]
                        elif tag == 'INDEX':
                                sheet_index = row[1:]
                        elif tag == 'VALUE':
                                rowdata = [try_int(c.value) for c in row[1:]]
                                rowdict = OrderedDict(zip(sheet_keys, rowdata))

                                if check_fun:
                                        for i in range(len(sheet_keys)):
                                                check_fun(sheet_keys[i], rowdata[i])

                                if self.sheet_as_col:
                                        rowdict[self.sheet_as_col] = sheet_name

                                sheet_data.append(rowdict)

                # end_for 结束当前页的读取
                if self.erl_filename:
                        for k, v in zip(sheet_keys, out_erl):
                                if v.value == 'YES':
                                        self.out_erl.add(k)

                        for k, v in zip(sheet_keys, sheet_index):
                                if v.value == 'YES':
                                        self.erl_index.add(k)

                        self.module = self.erl_filename.split('.')[0]
                        if self.erl_filename.startswith("xg_"):
                                self.record_name = self.erl_filename.split('.')[0][3:] # 去掉xg_
                        else:
                                self.record_name = self.erl_filename.split('.')[0]

                if self.csv_filename:
                        for k, v in zip(sheet_keys, out_csv):
                                if v.value == 'YES':
                                        self.out_csv.add(k)

                if self.sheet_as_col:
                        self.out_csv.add(self.sheet_as_col)
                        self.out_erl.add(self.sheet_as_col)
                        if self.sheet_as_col not in self.keys:
                                self.keys.insert(0, self.sheet_as_col)

                if sheet_types:
                        for k, v in zip(sheet_keys, sheet_types):
                                if k not in self.types:
                                        self.types[k] = unicode(v)

                for i in range(len(sheet_keys)):
                        if sheet_keys[i] and sheet_keys[i] in sheet_keys[i+1:]:
                                print(u"duplicate key in %s ===> %s" % (self.xlsx_filename, sheet_keys[i]))

                self.merge = sheet_merge
                self.data.extend(sheet_data)
                self.keys.extend([k for k in sheet_keys if k not in self.keys])

        def sheet_names(self):
                return self.book.sheet_names()

        def sheets(self):
                for sh in self.book.sheets():
                        sh.xlsx_filename = self.xlsx_filename
                        yield sh

def try_int(x):
        try:
                if abs(int(x) - x) < 0.00001:
                        return int(x)  # int
                else:
                        return x  # float
        except Exception:
                return x.strip()  # string

def parse_xlsx(filename):
        xlsx = Xlsx(filename)
        xs = []

        merge = False
        for sheet in xlsx.sheets():
                if not merge:
                        x = Xlsx()
                        x.parse(sheet)
                        xs.append(x)
                        merge = x.merge
                else:
                        x.parse(sheet)

        return xs

def cover_header(filename, header):
        data = {}

        if os.path.exists(filename):
                table = xlrd.open_workbook(filename).sheets()[0]

                rows = [table.row(i) for i in range(table.nrows)]

                keys = [cell.value for cell in rows[6]]
                keys = keys[1:len(keys)]

                rows = [[cell.value for cell in row] for row in rows[8:len(rows)]]

                if rows[0][0] != "NOTE":
                        rows.insert(0, ["NOTE"] + [""] * len(keys))

                data = {}

                for i in range(len(keys)):
                        data[keys[i]] = [row[i+1] for row in rows]

                data["KEY"] = [row[0] for row in rows]
        else:
                data["KEY"] = ["NOTE"]

        borders = xlwt.Borders()
        borders.left = 1
        borders.right = 1
        borders.top = 1
        borders.bottom = 1
        borders.bottom_colour = 0x3A

        style = xlwt.easyxf('align: wrap on')
        style.borders = borders

        book = xlwt.Workbook()

        sheet = book.add_sheet("Sheet1")

        for i in range(len(header)):
                for j in range(len(header[i])):
                        value = header[i][j]

                        sheet.write(i, j, value, style)

                        if i == 6 and data.has_key(value):
                                for k in range(len(data[value])):
                                        sheet.write(8 + k, j, data[value][k], style)

        book.save(filename)

def cover_data(filename, data, override = False):
        if not os.path.exists(filename):
                return

        table = xlrd.open_workbook(filename).sheets()[0]

        rows = [table.row(i) for i in range(table.nrows)]

        keys = [cell.value for cell in rows[6]]
        keys = keys[1:len(keys)]

        header = [[cell.value for cell in row] for row in rows[0:8]]

        rows = [[cell.value for cell in row] for row in rows[8:len(rows)]]

        data2 = {}

        for i in range(len(keys)):
                data2[keys[i]] = [row[i+1] for row in rows]

        data2["KEY"] = [row[0] for row in rows]

        data["KEY"] = ["VALUE"] * len(data["id"])

        borders = xlwt.Borders()
        borders.left = 1
        borders.right = 1
        borders.top = 1
        borders.bottom = 1
        borders.bottom_colour = 0x3A

        style = xlwt.easyxf('align: wrap on')
        style.borders = borders

        book = xlwt.Workbook()

        sheet = book.add_sheet("Sheet1")

        for i in range(len(header)):
                for j in range(len(header[i])):
                        value = header[i][j]

                        sheet.write(i, j, value, style)

                        if i == 6 and data2.has_key(value):
                                update_rows = []

                                old_row_num = len(data2[value])

                                for k in range(old_row_num):
                                        value2 = data2[value][k]

                                        try:
                                                k2 = data["id"].index(data2["id"][k])

                                                update_rows.append(k2)

                                                if value2 == "" or override:
                                                        try:
                                                                value2 = data[value][k2]
                                                        except:
                                                                pass
                                        except:
                                                pass

                                        sheet.write(8 + k, j, value2, style)

                                if data.has_key(value):
                                        rs = 8 + old_row_num

                                        for k in range(len(data[value])):
                                                if k in update_rows:
                                                        continue

                                                sheet.write(rs, j, data[value][k], style)

                                                rs += 1

        book.save(filename)
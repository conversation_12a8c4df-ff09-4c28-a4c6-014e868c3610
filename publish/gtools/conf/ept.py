# -*- coding: utf-8 -*-

import re
import string

MATCH_GET = re.compile(r"\s+get(\d{0,1})\s*\(\s*[a-zA-Z0-9_$]+\s*\)[^@]*@.+?;", re.DOTALL)
MATCH_TAIL = re.compile(r"\s+get(\d{0,1})\s*\([a-zA-Z0-9_$]+\)\s*@[^;]+undef\s*\.", re.DOTALL)
MATCH_IDENTS = re.compile(r"\s+gets(\d{0,1})\s*\(\s*\)\s*@\s*\[([a-zA-Z0-9_$ ]+)\]\s*\.", re.DOTALL)
GET_HOLDER = "&&GET_\g<1>&&"
TAIL_HOLDER = "&&TAIL_\g<1>&&"
IDENTS_HOLDER = "&&IDENTS_\g<1>&&"

class Ept(object):
        """Erlang数据模板"""
        def __init__(self):
                super(Ept, self).__init__()

        def feed(self, content):
                self.content = content

        def parse(self):
                self.content = self.content.replace("->", "@")

                cont = self.content

                self.tail_parts = MATCH_TAIL.finditer(cont)
                cont = MATCH_TAIL.sub(TAIL_HOLDER, cont)

                self.idents_parts = MATCH_IDENTS.finditer(cont)
                cont = MATCH_IDENTS.sub(IDENTS_HOLDER, cont)

                self.get_parts = MATCH_GET.finditer(cont)
                cont = MATCH_GET.sub(GET_HOLDER, cont)

                self.parsed_cont = cont

        def format(self, data):
                cont = self.parsed_cont
                for part in self.get_parts:
                        temp = string.Template(part.group(0))

                        get_arr = []
                        for line in data:
                                d = {}
                                for k,v in line.items():
                                        if v == "":
                                                v = "null"
                                        d[k] = v

                                get_arr.append(temp.substitute(d))

                        holder = GET_HOLDER.replace("\g<1>", part.group(1))
                        cont = cont.replace(holder, "\n\n".join(get_arr))

                for part in self.tail_parts:
                        holder = TAIL_HOLDER.replace("\g<1>", part.group(1))
                        cont = cont.replace(holder, part.group(0))

                for part in self.idents_parts:
                        id_arr = []
                        for line in data:
                                id_arr.append(string.Template(part.group(2)).substitute(line))
                        holder = IDENTS_HOLDER.replace("\g<1>", part.group(1))
                        part_text = part.group(0).replace(part.group(2), ",\n\t".join(id_arr))
                        cont = cont.replace(holder, part_text)

                cont = cont.replace("@", "->")
                cont = cont.replace("\n\n\n", "\n")

                self.formated_cont = cont

                return cont


def format(content, data):
        e = Ept()
        e.feed(content)
        e.parse()

        return e.format(data)
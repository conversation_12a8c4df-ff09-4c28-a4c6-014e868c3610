# -*- coding: utf-8 -*-

import io
import os
import string
import glob
import ept
import xlsx_lib
import sys
import traceback
from collections import OrderedDict

import gtools.util.string2 as string2

EXPORT_DIR = u'../后端导出'
XLSX_DIR = u'../../配置表'
TEMPLATE_DIR = u'../../ept'

DIR_SPLIT_CHAR = string2.dir_split_char()

def write_to_file(xlsx, dir='.'):
        if xlsx.erl_filename:
                text = gen_erl(xlsx)
                fn = os.path.join(dir, xlsx.erl_filename)
                with io.open(fn, "w", encoding='utf-8') as fp:
                        fp.write(text)
                return True
        else:
                return False

def gen_erl(xlsx):
        eptfile = os.path.join(TEMPLATE_DIR, u"%s.ept" %
                               xlsx.erl_filename.split("cfg_")[-1].split('.')[0])

        if os.path.isfile(eptfile):
                with io.open(eptfile, encoding='utf-8') as fp:
                        template = fp.read()
                text = ept.format(template, xlsx.data)
        else:
                text = gen_comment(xlsx) + gen_head(xlsx) + gen_export(xlsx) + \
                       gen_get_ids(xlsx) + gen_get(xlsx) + gen_get_by_index(xlsx)

        return text


def gen_comment(xlsx):
        global DIR_SPLIT_CHAR

        text = u"""
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%% 源文件: %s
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
""" % (xlsx.xlsx_filename.decode("gbk").split(DIR_SPLIT_CHAR)[-1],)
        return text

def gen_head(xlsx):
        return '-module(%s).\n-include("config.hrl").\n' % xlsx.module

def gen_export(xlsx):
        e1 = "-export([gets/0]).\n"
        e2 = "-export([get/1]).\n"
        if xlsx.erl_mode == 'RECORD':
                e3 = "-export([get_by_index/2]).\n"
        else:
                e3 = ""
        return e1 + e2 + e3

def gen_get_ids(xlsx):
        ids = [gen_row_id(xlsx, r) for r in xlsx.data]
        idstr = ",\n\t".join([i for i in ids if i])
        return "gets() ->\n\t[%s].\n\n" % idstr

def gen_get(xlsx):
        if xlsx.erl_mode == "MULTILINE":
                return gen_get_multiline(xlsx) + gen_get_warn(xlsx)
        else:
                return gen_get_cause(xlsx) + gen_get_warn(xlsx)

def gen_get_cause(xlsx):
        gets = []

        for row in xlsx.data:
                rid = gen_row_id(xlsx, row)

                if xlsx.erl_template:
                        get = gen_by_template(xlsx, row, xlsx.erl_template)
                elif xlsx.erl_mode == "KEY_VALUE":
                        cause = gen_row_n(xlsx, row, 1)
                        get = u"get(%s) -> %s;\n\n" % (rid, cause)
                else:
                        cause = gen_record(xlsx, row)
                        get = u"get(%s) -> %s;\n\n" % (rid, cause)

                gets.append(get)

        return u"".join(gets)


def gen_get_multiline(xlsx):
        key = xlsx.keys[0]

        groups = []
        for row in xlsx.data:
                if row.get(key):
                        group = []
                        groups.append((format_cell(xlsx, xlsx, row.get(key)), group))
                        row = OrderedDict(row)
                        del(row[key])
                group.append(gen_record(xlsx, row))

        gets = []
        for (key, group) in groups:
                group_str = ",\n".join(group)
                gets.append(u"get(%s) -> [%s];\n\n" % (key, group_str))

        return u"".join(gets)

def gen_get_warn(xlsx):
        if xlsx.erl_mode == "MULTILINE":
                ret = "[]"
        else:
                ret = "undef"

        if xlsx.no_warning:
                warn = ""
        else:
                warn = """?WARNING_LOG("Cannot get ~p", [_ID]),\n"""

        return u"get(_ID) ->\n\t%s\t%s.\n\n" % (warn, ret)

def gen_get_by_index(xlsx):
        if xlsx.erl_mode != "RECORD":
                return ""

        M = OrderedDict()
        for index in xlsx.erl_index:
                for row in xlsx.data:
                        ri = row[index]
                        if (index, ri) in M:
                                M[(index, ri)].append(gen_row_id(xlsx, row))
                        else:
                                M[(index, ri)] = [gen_row_id(xlsx, row)]

        texts = []
        for ((index, ri), ids) in M.items():
                idstr = ",\n\t".join(ids)
                texts.append("get_by_index(#%s.%s, %s) ->\n\t[%s];" % (
                             xlsx.record_name, index, ri, idstr))

        for index in xlsx.erl_index:
                texts.append("get_by_index(#%s.%s, _) -> [];" % (xlsx.record_name, index))

        warn = """?WARNING_LOG("Cannot get by index ~p, ~p", [_Index, _ID]),\n"""
        texts.append("get_by_index(_Index, _ID) ->\n\t%s []." % warn)
        return "\n\n".join(texts)

def gen_row_id(xlsx, row):
        if xlsx.erl_id:
                return string.Template(xlsx.erl_id).substitute(row)
        else:
                return gen_row_n(xlsx, row, 0)

def gen_row_n(xlsx, row, col):
        key = xlsx.keys[col]
        typ = xlsx.types.get(key)
        return format_cell(xlsx, typ, row[key])

def gen_record(xlsx, row):
        fields = []
        for k, v in row.items():
                if v == "":
                        continue
                if k in xlsx.out_erl:
                        typ = xlsx.types.get(k)
                        v = format_cell(xlsx, typ, v)
                        fields.append("%s = %s" % (k, v))
        fields = u",\n\t".join(fields)
        record = u"#{rec}{{\n\t{fields}\n}}".format(rec=xlsx.record_name, fields=fields)
        return record

def gen_by_template(xlsx, row, template):
        return string.Template(template).substitute(row)

def format_cell(xlsx, typ, x):
        if x == "":
                return None

        if isinstance(x, int):
                return unicode(x)
        elif typ == 'FLOAT' and isinstance(x, float):
                return unicode(x)

        try:
                return unicode(int(x))
        except:
                pass

        if typ == "ATOM":
                return x
        elif typ == "BINARY":
                return u'<<"%s">>' % x
        elif x.startswith("["):
                return x
        elif x.startswith("{"):
                return x
        else:
                print("Invalid value in %s: %s " % (os.path.split(xlsx.xlsx_filename)[-1], x))

def export_file(filename, erl_dir=".", ept_dir="."):
        global EXPORT_DIR, TEMPLATE_DIR

        EXPORT_DIR = erl_dir
        TEMPLATE_DIR = ept_dir

        print "export %s..." % (filename,)

        xs = xlsx_lib.parse_xlsx(filename)
        for x in xs:
                write_to_file(x, erl_dir)

        print "ok."

def export_all(filters = (),includes = None):
        pass_cond = tuple([False] * len(filters))
        if includes is not None:
                pass_cond2 = tuple([False] * len(includes))
        else:
                pass_cond2 = None

        for fn in glob.glob(os.path.join(XLSX_DIR, "*.xls")):
                if "~$" in fn or \
                   tuple([(filter in fn) for filter in filters]) != pass_cond or \
                   (pass_cond2 is not None and tuple([(include+"." in fn) for include in includes]) == pass_cond2):
                        continue
                try:
                        export_file(fn, EXPORT_DIR, TEMPLATE_DIR)
                except Exception:
                        traceback.print_exc(file=sys.stdout)
                        print("\n=====>>> Cannot export %s\n" % fn)

def export(xls_dir, erl_dir, ept_dir, filters = (),includes = None):
        global XLSX_DIR, EXPORT_DIR, TEMPLATE_DIR

        XLSX_DIR = xls_dir
        EXPORT_DIR = erl_dir
        TEMPLATE_DIR = ept_dir

        export_all(filters,includes)

if __name__ == "__main__":
        export(".", ".", ".")
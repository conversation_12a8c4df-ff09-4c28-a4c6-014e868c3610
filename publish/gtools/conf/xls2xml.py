# -*- coding: utf8 -*-

import os
import re
import sys

import xlrd

def export_file(filepath, xls_dir, xml_dir, filters = (), includes = None):
        filename = re.findall("[^/\\\\]+\.xls$", filepath)[0].split(".")[0]
        
        if filename + ".xls" in filters or (includes is not None and filename not in includes):
                return

        print "export %s..." % (filename,)

        checker_py = "%s/%s_chk.py" % (xls_dir, filename)

        if os.path.exists(checker_py):
                execfile(checker_py)

                if "check" in dir():
                        check_fun = locals()["check"]
                else:
                        check_fun = None
        else:
                check_fun = None

        tables = xlrd.open_workbook(filepath).sheets()

        merge = False

        outcsv = None

        keys = []

        for i in range(tables[0].nrows):
                row = tables[0].row(i)
                tag = row[0].value
                rowb = row[1].value
                if tag == "MERGE" and rowb == "YES":
                        merge = True
                        continue
                elif tag == "VALUE":
                        break
                elif tag == "OUT_CSV":
                        outcsv = [row[i + 1].value for i in range(len(row) - 1)]
                        continue
                elif tag == "KEY":
                        keys = [row[i + 1].value for i in range(len(row) - 1)]
                        continue
                else:
                        continue

        if not merge:
                tables = [tables[0]]

        outkeys = []

        for i in range(len(keys)):
                try:
                        if outcsv[i] == "YES" or outcsv[i] == "":
                                outkeys.append(keys[i])
                except:
                        outkeys.append(keys[i])

        if not len(outkeys):
                return

        outrows = []

        for table in tables:

                rows = [table.row(i) for i in range(table.nrows)]

                keys = [cell.value for cell in rows[6]]
                keys = keys[1:len(keys)]

                rows2 = []

                for row in rows[9:len(rows)]:
                        if row[0].value == "VALUE":
                                rows2.append([cell.value for cell in row[1:len(row)]])

                outrows += rows2

        fp = open("%s/%s.conf" % (xml_dir, filename), "w")

        fp.write("<%ss>\n" % filename)

        for row in outrows:
                xml = "        <%s " % filename

                for i in range(len(keys)):
                        if re.match("^\s*$", keys[i]):
                                continue

                        if type(row[i]) == float and int(row[i]) == row[i]:
                                row[i] = int(row[i])
                        elif type(row[i]) == unicode:
                                row[i] = row[i].encode("utf8")

                        key = keys[i].encode("utf8")
                        value = row[i]

                        if (key not in outkeys) or value == "":
                                continue

                        if check_fun:
                                check_fun(key, value)

                        xml += "%s=\"%s\" " % (key, value)

                xml += "/>\n"

                fp.write(xml)

        fp.write("</%ss>" % filename)

        fp.close()

        print "ok."

def export(xls_dir, xml_dir, filters = (), includes = None):
        for root, dirs, files in os.walk(xls_dir):
                for file in files:
                        filepath = root + "/" + file

                        if re.findall("\.xls$", filepath) != []:
                                export_file(filepath, xls_dir, xml_dir, filters, includes)

if __name__ == "__main__":
        export(".", ".")
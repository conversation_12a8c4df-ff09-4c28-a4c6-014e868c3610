# -*- coding: utf8 -*-

import os
import sys

import datetime

import gtools.util.file as file
import gtools.util.svn  as svn
import gtools.util.git  as git
import gtools.util.scp  as scp
import gtools.util.string2 as string2
import gtools.util.os2 as os2

def parse_revision(version):
        version = version.split(".")

        if len(version) == 3:
                return int(version[-1])

        return int(version[-2]) + int(version[-1])

def equal_version(version1, version2):
        revision1 = parse_revision(version1)
        revision2 = parse_revision(version2)

        version1 = version1.split(".")
        version2 = version2.split(".")

        return version1[0] == version2[0] and \
               version1[1] == version2[1] and \
               revision1 == revision2

def patch_version(this_version, last_version):
        this_revision = int(this_version.split(".")[-1])
        last_revision = parse_revision(last_version)

        last_version2 = last_version.split(".")

        if len(last_version2) == 3:
                return this_version.split("-")[0], "%s.%s" % (last_version, this_revision - last_revision)

        return this_version.split("-")[0], "%s.%s.%s.%s" % (last_version2[0], last_version2[1], last_version2[2],
                                                            int(last_version2[-1]) + this_revision - last_revision)

class CPackFlow:
        def __init__(self, filename, level = "..", dist = "dist", tool = "svn", lang = "zh-cn"):
                self._str_work_dir = file.file_dir(filename)

                self._str_level = level
                
                self._str_dist = dist

                self._str_tool = tool

                self._str_lang = lang

                self._list_ex_vsn_dirs = []

                self._str_fr_change_log = ""

                self._list_custom_update_list = ()

                self._b_like_patch = False

                self._b_test_beta = False

                self._b_verify_revision = True

                self._b_pack_zip = False

                self._str_last_version = ""

                self._n_last_revision = 1

                self._str_svn_username = None
                self._str_svn_password = None
                self._str_pub_passwd = None

                self._str_changelog_dir = ""
                self._str_changelog_path = ""

        def update_changelog_path(self):
                if os.path.exists("changelog") and os.path.isfile("changelog"):
                        self._str_changelog_dir = ""
                        self._str_changelog_path = "changelog"
                else:
                        split_char = string2.dir_split_char()

                        self._str_changelog_dir = "changelog" + split_char + self._str_lang
                        self._str_changelog_path = self._str_changelog_dir + split_char + "changelog"

        def set_svn_passwd(self, username, password):
                self._str_svn_username = username
                self._str_svn_password = password

                svn.SVN_USERNAME = username
                svn.SVN_PASSWORD = password

        def set_pub_passwd(self, value):
                self._str_pub_passwd = value

        def set_ex_vsn_dirs(self, value):
                self._list_ex_vsn_dirs = value

        def set_fr_change_log(self, value):
                self._str_fr_change_log = value

        def set_custom_update_list(self, value):
                self._list_custom_update_list = value

        def set_like_patch(self, value):
                self._b_like_patch = value

        def set_test_beta(self, value):
                self._b_test_beta = value

        def set_verify_revision(self, value):
                self._b_verify_revision = value

        def set_pack_zip_type(self, value):
                self._b_pack_zip = value

        def parse_fr_update_list(self):
                if self._str_fr_change_log == "":
                        return []

                lines = file.read_file_lines(self._str_fr_change_log)

                if not len(lines):
                        return []

                lines.pop(0)

                return [string2.str_repl("^\d+\.\s*", line, "") for line in lines]

        def parse_ap_update_list(self, this_revision):
                update_list = []

                vsn_dirs = ["."]

                vsn_dirs.extend(self._list_ex_vsn_dirs)

                if self._str_tool == "svn":
                        update_list.extend(svn.update_list(vsn_dirs,
                                                           self._n_last_revision + 1,
                                                           this_revision,
                                                           self._list_custom_update_list))

                return update_list

        def parse_last_update_list(self):
                if not os.path.exists(self._str_changelog_path):
                        return "", "", "", []

                content = file.read_file(self._str_changelog_path)
                if sys.platform == "win32":
                        content2 = content.split("\n\n")[-1]

                        lines = content2.split("\n")
                else:
                        content2 = content.split("\r\n\r\n")[-1]

                        lines = content2.split("\r\n")

                last_version = lines.pop(0).split(": ")[1].replace("\r", "")

                self._n_last_revision = parse_revision(last_version)

                lines = [string2.str_wipe_break(line) for line in lines]

                return content, content2, last_version, [string2.str_repl("^\d+\.\s*", line, "") for line in lines]

        def gen_update_list(self, this_revision):
                fr_update_list = self.parse_fr_update_list()
                _, _, _, last_update_list = self.parse_last_update_list()
                ap_update_list = self.parse_ap_update_list(this_revision)

                for line in fr_update_list:
                        if line not in ap_update_list:
                                ap_update_list.append(line)

                content = ""
                index = 1

                for line in ap_update_list:
                        if line not in last_update_list:
                                if sys.platform == "Win32":
                                        content += str(index) + ". " + line + "\n"
                                else:
                                        content += str(index) + ". " + line + "\r\n"

                                index += 1

                if content == "":
                        return content

                return content[0:-2]

        def work(self, tmp_dir, ident, version):
                pass

        def work_like_patch(self, tmp_dir, ident, version, patch_list):
                pass

        def verify(self, version_mod):
                revision = version_mod.get_revision(".")

                for ex_svn_dir in self._list_ex_vsn_dirs:
                        if version_mod.get_revision(ex_svn_dir) >= revision:
                                return False

                return True

        def patch_list(self, this_revision):
                return svn.patch_list(".", self._n_last_revision, this_revision)

        def fmt_pkg_name(self, alias, lang, version):
                if lang == "zh-cn":
                        return "%s-%s" % (alias, version)
                else:
                        return "%s-%s-%s" % (alias, lang, version)

        def pack(self):
                if len(sys.argv) < 4:

                        return False

                try:
                        alias = sys.argv[1]

                        if self._b_test_beta:
                                alias += "_beta"

                        generation = int(sys.argv[2])
                        upload_dir = sys.argv[3]
                except:
                        traceback.print_exc()

                        return False

                os.chdir(self._str_work_dir)
                os.chdir(self._str_level)

                self.update_changelog_path()

                if sys.platform == "win32":
                        os.system("del " + self._str_changelog_path)
                else:
                        os.system("rm " + self._str_changelog_path)

                version_mod = None

                if self._str_tool == "svn":
                        if self._str_svn_username is None:
                            os.system("svn update")
                        else:
                            os.system("svn update --username %s --password %s" % (self._str_svn_username, self._str_svn_password))

                        version_mod = svn
                elif self._str_tool == "git":
                        os.system("git pull")

                        version_mod = git
                else:

                        return False

                if self._b_verify_revision and (not self.verify(version_mod)):
                        return False

                ident, version = version_mod.get_version(generation, ".")

                if version is None:

                        return False

                version2 = version.split("-")[-1]

                patch_list = None

                if ident == "alpha":
                        update_list = ""

                        this_revision = int(version2.split(".")[-1])

                        update_list2 = self.gen_update_list(this_revision)

                        if update_list2 != "":
                                if sys.platform == "Win32":
                                        update_list += "\n%s" % (update_list2,)
                                else:
                                        update_list += "\r\n%s" % (update_list2,)

                        changelog, last_update_list, self._str_last_version, _ = self.parse_last_update_list()

                        if self._str_last_version == "" or (not equal_version(self._str_last_version, version2)):
                                if self._b_test_beta:

                                        return False

                                if self._b_like_patch:
                                        _, version4 = patch_version(version, self._str_last_version)

                                        update_list = "%s: %s: %s" % (str(datetime.date.today()), version4, version2) + update_list

                                        version = "%s-%s" % (ident, version4)

                                        patch_list = self.patch_list(this_revision)
                                else:
                                        update_list = "%s: %s" % (str(datetime.date.today()), version2) + update_list

                                if changelog != "":
                                        if sys.platform == "win32":
                                                changelog += "\n\n"
                                        else:
                                                changelog += "\r\n\r\n"

                                changelog += update_list

                                file.write_file(self._str_changelog_path, changelog)
                        else:
                                update_list = last_update_list
                else:
                        _, update_list, self._str_last_version, _ = self.parse_last_update_list()
                        
                tmp_pkg_name = self.fmt_pkg_name(alias, self._str_lang, version)

                if sys.platform == "win32":
                        tmp_dir = "%s:\\%s" % (self._str_work_dir[0], tmp_pkg_name)
                        os.system("rd /s /q %s" % (tmp_dir,))
                        os.system("md %s" % (tmp_dir,))
                else:
                        tmp_dir = "%s/%s/%s" % (self._str_work_dir, self._str_dist, tmp_pkg_name)
                        os.system("rm -rf %s" % (tmp_dir,))
                        os.system("mkdir %s" % (tmp_dir,))

                if patch_list is None:
                        if not self.work(tmp_dir, ident, version):
                                return False
                else:
                        if not self.work_like_patch(tmp_dir, ident, version, patch_list):
                                return False

                if sys.platform == "win32":
                        if self._str_changelog_dir != "":
                                os2.system_mkdir("%s\\%s" % (tmp_dir, self._str_changelog_dir))
                        file.write_file("%s\\%s" % (tmp_dir, self._str_changelog_path), update_list)

                        filelist = os2.stat_file_list(tmp_dir, tmp_dir + "\\")
                        file.write_file("%s\\file_list" % (tmp_dir,), "\n".join(["%s %s" % (size, filename) for size, filename in filelist]))
                        tar_gz = "%s\\%s\\%s.zip" % (self._str_work_dir, self._str_dist, tmp_pkg_name)

                        os.chdir("%s:\\" % (self._str_work_dir[0],))
                        os.system("7z a %s %s" % (tar_gz, tmp_pkg_name))
                        os.system("rd /s /q %s" % (tmp_dir,))
                        os.chdir("%s\\%s" % (self._str_work_dir, self._str_dist))
                else:
                        if self._str_changelog_dir != "":
                                os2.system_mkdir("%s/%s" % (tmp_dir, self._str_changelog_dir))
                        file.write_file("%s/%s" % (tmp_dir, self._str_changelog_path), update_list)

                        filelist = os2.stat_file_list(tmp_dir, tmp_dir + "/")
                        file.write_file("%s/file_list" % (tmp_dir,), "\n".join(["%s %s" % (size, filename) for size, filename in filelist]))
                        os.chdir("%s/%s" % (self._str_work_dir, self._str_dist))
                        tar_gz = tmp_pkg_name
                        
                        os.system("chmod 777 -R %s" % (tmp_pkg_name,))
                        if self._b_pack_zip:
                                tar_gz = "%s.zip" % (tar_gz,)
                                os.system("zip -rq %s %s" % (tar_gz, tmp_pkg_name))
                        else:
                                tar_gz = "%s.tar.gz" % (tar_gz,)
                                os.system("tar -zcvf %s %s" % (tar_gz, tmp_pkg_name))
                        os.system("chmod 777 %s" % (tar_gz,))

                        os.system("rm -rf %s" % (tmp_dir,))

                if upload_dir != "none":
                        retry = 0

                        while retry < 5:
                                try:
                                        if retry:
                                                print("please retry.")

                                        scp.upload(tar_gz, upload_dir, password = self._str_pub_passwd)

                                        break
                                except:
                                        pass

                                retry += 1

                return True

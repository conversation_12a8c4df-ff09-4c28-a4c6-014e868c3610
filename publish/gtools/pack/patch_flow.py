# -*- coding: utf8 -*-

import os
import sys

import datetime

import gtools.util.file as file
import gtools.util.svn  as svn
import gtools.util.git  as git
import gtools.util.scp  as scp
import gtools.util.string2 as string2
import gtools.util.os2 as os2

import pack_flow

class CPatchFlow:
        def __init__(self, filename, level = "..", dist = "dist", tool = "svn", lang = "zh-cn"):
                self._str_work_dir = file.file_dir(filename)

                self._str_level = level

                self._str_dist = dist

                self._str_tool = tool

                self._str_lang = lang

                self._str_from_version = ""
                self._str_to_version = ""

                self._n_from_revision = 1
                self._n_to_revision = 1

                self._pub_passwd = None

                self._str_changelog_dir = ""
                self._str_changelog_path = ""

        def update_changelog_path(self):
                if os.path.exists("changelog") and os.path.isfile("changelog"):
                        self._str_changelog_dir = ""
                        self._str_changelog_path = "changelog"
                else:
                        split_char = string2.dir_split_char()

                        self._str_changelog_dir = "changelog" + split_char + self._str_lang
                        self._str_changelog_path = self._str_changelog_dir + split_char + "changelog"

        def set_pub_passwd(self, value):
                self._pub_passwd = value

        def set_version(self, from2, to):
                from3 = from2.split(".")
                to2 = to.split(".")

                if len(from3) != 3 or from3[0] != to2[0] or from3[1] != to2[1]:
                        raise Exception("from_version or to_version invalid.")

                self._str_from_version = from2
                self._str_to_version = to

                self._n_from_revision = pack_flow.parse_revision(from2)
                self._n_to_revision = pack_flow.parse_revision(to)

        def parse_update_list(self):
                if not os.path.exists(self._str_changelog_path):
                        return False, ""

                valid1 = False
                valid2 = False

                update_list = None

                changelog_content = file.read_file(self._str_changelog_path)

                if sys.platform == "win32":
                        contents = changelog_content.split("\n\n")
                else:
                        contents = changelog_content.split("\r\n\r\n")

                for content in contents:
                        if sys.platform == "win32":
                                revision = pack_flow.parse_revision(content.split("\n")[0].split(": ")[1])
                        else:
                                revision = pack_flow.parse_revision(content.split("\r\n")[0].split(": ")[1])

                        if revision == self._n_from_revision:
                                valid1 = True

                                update_list = content
                        elif update_list is not None:
                                if sys.platform == "win32":
                                        update_list += "\n\n" + content
                                else:
                                        update_list += "\r\n\r\n" +content

                                if revision == self._n_to_revision:
                                        valid2 = True

                                        break

                if valid1 and valid2:
                        return True, update_list

                return False, ""

        def work(self, tmp_dir, version):
                pass

        def patch_list(self):
                return svn.patch_list(".", self._n_from_revision, self._n_to_revision)

        def pack(self):
                if len(sys.argv) < 4:
                        print "arguments error."

                        return False

                try:
                        alias = sys.argv[1]
                        generation = int(sys.argv[2])
                        upload_dir = sys.argv[3]
                except:
                        traceback.print_exc()

                        return False

                os.chdir(self._str_work_dir)
                os.chdir(self._str_level)

                self.update_changelog_path()

                version_mod = None

                if self._str_tool == "svn":
                        print "svn update."
                        os.system("svn update -r %s" % (self._n_to_revision,))

                        version_mod = svn
                elif self._str_tool == "git":
                        print "git pull"
                        os.system("git pull")

                        version_mod = git
                else:
                        print "tool(%s) not support." % (self._str_tool,)

                        return False

                _, version = version_mod.get_version(generation, ".")

                if version is None:
                        print "get version fail."

                        return False

                version = version.split("-")[-1].split(".")
                version2 = self._str_to_version.split(".")

                if version[0] != version2[0] or version[1] != version2[1]:
                        print "from_revision or to_revision invalid.", version, version2

                        return False

                os.system("svn update " + self._str_changelog_path)

                valid, update_list = self.parse_update_list()

                if not valid:
                        print "from_revision or to_revision invalid."

                        return False

                version = "patch-%s.%s.%s" % (version[0], version[1], self._n_to_revision)

                _, version = pack_flow.patch_version(version, self._str_from_version)

                version = "patch-" + version

                if sys.platform == "win32":
                        tmp_dir = "%s:\\%s-%s" % (self._str_work_dir[0], alias, version)

                        os.system("md %s" % (tmp_dir,))
                else:
                        tmp_dir = "%s/%s/%s-%s" % (self._str_work_dir, self._str_dist, alias, version)

                        os.system("mkdir %s" % (tmp_dir,))

                if not self.work(tmp_dir, self.patch_list(), version):
                        return False

                if self._str_changelog_dir != "":
                        os2.system_mkdir("%s\\%s" % (tmp_dir, self._str_changelog_dir))

                if sys.platform == "win32":
                        file.write_file("%s\\%s" % (tmp_dir, self._str_changelog_path), update_list)
                        filelist = os2.stat_file_list(tmp_dir, tmp_dir + "\\")
                        file.write_file("%s\\file_list" % (tmp_dir,), "\n".join(["%s %s" % (size, filename) for size, filename in filelist]))
                        tar_gz = "%s\\%s\\%s-%s.zip" % (self._str_work_dir, self._str_dist, alias, version)
                        os.chdir("%s:\\" % (self._str_work_dir[0],))
                        os.system("7z a %s %s-%s" % (tar_gz, alias, version))
                        os.system("rd /s /q %s" % (tmp_dir,))
                        os.chdir("%s\\%s" % (self._str_work_dir, self._str_dist))
                else:
                        file.write_file("%s/%s" % (tmp_dir, self._str_changelog_path), update_list)
                        filelist = os2.stat_file_list(tmp_dir, tmp_dir + "/")
                        file.write_file("%s/file_list" % (tmp_dir,), "\n".join(["%s %s" % (size, filename) for size, filename in filelist]))
                        tar_gz = "%s-%s.tar.gz" % (alias, version)
                        os.chdir("%s/%s" % (self._str_work_dir, self._str_dist))
                        os.system("tar -zcvf %s %s-%s" % (tar_gz, alias, version))
                        os.system("rm -rf %s" % (tmp_dir,))

                retry = 0

                while retry < 5:
                        try:
                                if retry:
                                        print "please retry."

                                scp.upload(tar_gz, upload_dir, password = self._pub_passwd)

                                break
                        except:
                                pass

                        retry += 1

                return True
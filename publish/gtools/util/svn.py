# -*- coding: utf8 -*-
import os
import re
import sys

import os2

import gtools.util.file as file
import gtools.util.string2 as string2

SVN_USERNAME = None
SVN_PASSWORD = None

def svn_suffix():
        global SVN_USERNAME, SVN_PASSWORD

        if SVN_USERNAME is None:
                return ""

        return " --username %s --password %s" % (SVN_USERNAME, SVN_PASSWORD)

def parse_revision(content):
        revision = re.findall("Last Changed Rev: \d{1,10}", content)

        if not len(revision):
                revision = re.findall("最后修改的版本: \d{1,10}", content)

        return revision[0].split(": ")[1]

def get_revision(svn_dir):
        global SVN_USERNAME, SVN_PASSWORD

        cwd = os.getcwd()
        os.chdir(svn_dir)

        suffix = svn_suffix()

        os.system("svn update%s" % (suffix,))

        content = os2.psystem("svn info%s" % (suffix,))

        urls = re.findall("URL: .{1,200}", content)

        if not len(urls):
                return 0

        url = urls[0]
        url = url[5:len(url)]

        revision = parse_revision(content)

        os.chdir(cwd)

        return int(revision)

def get_version(generation, svn_dir):
        cwd = os.getcwd()
        os.chdir(svn_dir)

        suffix = svn_suffix()

        os.system("svn update%s" % (suffix,))

        content = os2.psystem("svn info%s" % (suffix,))

        urls = re.findall("URL: .{1,200}", content)

        if not len(urls):
                return "alpha", "alpha-1.0.0"

        url = urls[0]
        url = url[5:len(url)]

        revision = parse_revision(content)

        ident = ""

        if url.find("trunk") == -1:
                #分支命名规范,形如br_1.1
                rets = re.findall("/br_%d.\d{1,2}" % (generation,), url)
                if not len(rets):
                        #tag命名规范,形如r_1.1.1234
                        rets = re.findall("/r_%d.\d{1,2}.\d{1,10}" % (generation,), url)

                        if not len(rets):
                                ident = "alpha"

                                version = ident + "-%d.0.%s" % (generation, revision)
                        else:
                                ident = "beta"

                                version = ident + "-%s" % (rets[0][3:len(rets[0])])
                else:
                        ident = "alpha"

                        version = ident + "-%s.%s" % (rets[0][4:len(rets[0])], revision)
        else:
                ident = "alpha"

                version = ident + "-%d.0.%s" % (generation, revision)

        os.chdir(cwd)

        return ident, version

def encoder():
        if sys.platform == "win32":
                return "gbk"

        return "utf8"

def get_logs(svn_dir, last_revision, this_revision):
        global SVN_USERNAME, SVN_PASSWORD

        cwd = os.getcwd()
        os.chdir(svn_dir)

        logs = os2.psystem("svn log -r r%d:r%d%s" % (last_revision, this_revision, svn_suffix()))

        os.chdir(cwd)

        return logs

def parse_logs(logs):
        ret = []

        for log in re.findall("r\d+ \| \w+ \| \d+-\d+-\d+[^-]+\n-", logs):
                username = re.findall("r\d+ \| \w+ \|", log)[0].split("| ")[1][0:-2]

                for log2 in re.findall((u"\[发布\][^\r\n]*").encode(encoder()), log):
                        ret.append("[" + username +"]" + log2.replace((u"[发布]").encode(encoder()), ""))

        return list(set(ret))

def update_list(svn_dirs, last_revision, this_revision, customs = ()):
        logs = ""

        for svn_dir in svn_dirs:
                logs += get_logs(svn_dir, last_revision, this_revision)

        if len(customs):
                for custom in customs:
                        key = (u"发布").encode(encoder())

                        logs += "\r\n[%s]%s" % (key, custom)

        return parse_logs(logs)

def patch_list(svn_dir, last_revision, this_revision, chk_ext_dir = None, lang = None):
        global SVN_USERNAME, SVN_PASSWORD

        cwd = os.getcwd()
        os.chdir(svn_dir)

        patches = []

        suffix = svn_suffix()

        files = os2.psystem("svn diff -r %s:%s --summarize%s" % (last_revision, this_revision, suffix)).split("\n")

        exts = []

        if chk_ext_dir and lang:
                split_char = string2.dir_split_char()

                os.chdir("%s%s%s" % (chk_ext_dir, split_char, lang))
                exts2 = os2.psystem("svn propget svn:externals%s" % (suffix,)).split("\n")
                os.chdir("..%s.." % (split_char,))

                for ext in exts2:
                        if ext != "":
                                src, dir = ext.split(" ")

                                if src.find("svn://") == -1:
                                        src = ("%s/%s/%s" % (chk_ext_dir, lang, src)).replace("/", split_char)
                                else:
                                        src = (chk_ext_dir + "/" + src.split("/%s/" % (chk_ext_dir.split(split_char)[-1],))[-1]).replace("/", split_char)

                                exts.append(("%s%s%s%s%s" % (chk_ext_dir, split_char, lang, split_char, dir), src))

        for file in files:
                if not len(file):
                        continue

                file = string2.str_wipe_break(file)

                file_arr = file.split("    ")

                file = string2.str_wipe_space(file_arr[0])

                if file[0] == "D":
                        continue

                file = string2.str_wipe_space(file_arr[1])

                if os.path.isfile(file):
                        for ext, src in exts:
                                if src in file:
                                        file = (file.replace(src, ext), file)

                                        break
                                        
                        patches.append(file)

        return patches

if __name__ == "__main__":
        #temp = patch_list("H:\\svn\\x1-cli-trunk\\cgame\\bin-release", 31032, 31091, "asset", "zh-cn_qq")
        print update_list(["G:\\svn\\x1-cli\\trunk\\cgame\\bin-debug\\asset\\zh-cn\\conf"], 28066, 28126)

        #print temp

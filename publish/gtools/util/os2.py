# -*- coding: utf8 -*-

import os
import platform
import gtools.util.string2 as string2

SPEC_CHARS = (" ", "(", ")", "[", "]", "{", "}")

def system(command, extra = None):
        if not os.system(command):
                return

        if extra:
                raise Exception("SystemError: %s %s" % (command, extra))
        else:
                raise Exception("SystemError: %s" % (command,))

def psystem(command):
        pipe = os.popen(command)
        content = pipe.read()
        pipe.close()

        return content

def system_mkdir(dir, rec = False):
        def mkdir(dir):
                if platform.system() == "Windows":
                        os.system("md \"%s\"" % (dir.replace("/", "\\"),))
                else:
                        os.system("mkdir -p \"%s\"" % (dir,))

        if not rec:
                mkdir(dir)
        else:
                dir = dir.replace("\\", "/")

                pdir = ""

                for dir in dir.split("/"):
                        if pdir == "":
                                pdir = dir
                        else:
                                pdir += "/" + dir

                        if not os.path.exists(pdir):
                                mkdir(pdir)
                                
def system_rmdir(dir):
        if dir.find("*") == -1 and \
           not os.path.exists(dir):
                return

        if platform.system() == "Windows":
                os.system("rd /s /q \"%s\"" % (dir.replace("/", "\\"),))
        else:
                os.system("rm -rf \"%s\"" % (dir,))

def system_rm(filename):
        if filename.find("*") == -1 and \
           not os.path.exists(filename):
                return

        if platform.system() == "Windows":
                os.system("del \"%s\"" % (filename.replace("/", "\\"),))
        else:
                os.system("rm -rf \"%s\"" % (filename,))

def system_cp(src, des):
        if src.find("*") == -1 and \
           not os.path.exists(src):
                return

        if platform.system() == "Windows":
                if os.path.isdir(src):
                        src = src.replace("/", "\\")
                        if src[-1] == "\\":
                                src = src[:-1];

                        os.system("xcopy /c/e/q/i/y %s %s" % (src.replace("/", "\\"), des.replace("/", "\\")))
                else:
                        os.system("copy /y \"%s\" \"%s\"" % (src.replace("/", "\\"), des.replace("/", "\\")))
        else:
                if not os.path.isdir(src):
                        os.system("cp -R %s %s" % (src, des))
                else:
                        os.system("rsync -az %s %s" % (src, des))

def system_mv(src, des):
        if src.find("*") == -1 and \
           not os.path.exists(src):
                return

        if platform.system() == "Windows":
                os.system("move %s %s" % (src.replace("/", "\\"), des.replace("/", "\\"),))
        else:
                os.system("mv %s %s" % (src, des,))

def del_files(dir):
        for root, dirs, files in os.walk(dir):
                for filename in files:
                        full_path = root + string2.dir_split_char() + filename

                        if os.path.isfile(full_path):
                                system_rm(full_path)

def del_links(dir):
        for item in os.listdir(dir):
                if item == ".svn":
                        continue

                path = dir + "/" + item

                print(path)

                if os.path.islink(path):
                        os.system("rm \"%s\"" % (path,))
                elif os.path.isdir(path):
                        del_links(path)
                else:
                        pass

def system_cp2(src, des, skip_dirs = ()):
        global SPEC_CHARS

        if src == ".svn":
                return

        abs_src = os.path.abspath(src)

        for dir in skip_dirs:
                abs_dir = os.path.abspath(dir)

                if abs_src == abs_dir:
                        return

        if not os.path.exists(des):
                os.system("mkdir \"%s\"" % (des,))

        for item in os.listdir(src):
                if item == ".svn":
                        continue

                path = (src + "/" + item)

                for char in SPEC_CHARS:
                        path = path.replace(char, "\\" + char)

                if os.path.islink(path):
                        pass
                elif os.path.isfile(path):
                        os.system("cp %s \"%s\"/" % (path, des))
                elif os.path.isdir(path):
                        system_cp2(path, des + "/" + item, skip_dirs)
                else:
                        pass

def rpc(host, port, username, password, command):
        return os.system("sshpass -p '%s' ssh -o StrictHostKeyChecking=no -p%d %s@%s %s" % \
                         (password, port, username, host, command))

def clear_dir(dir_path, skip_dirs = ()):
        abs_dir_path = os.path.abspath(dir_path)
        Result = True

        for dir in skip_dirs:
                abs_dir = os.path.abspath(dir)
                if abs_dir == abs_dir_path:
                    return False

        for item in os.listdir(dir_path):
                path = dir_path + "/" + item

                if os.path.islink(path) or \
                   os.path.isfile(path):
                        os.system("rm -f \"%s\""%(path))

                elif os.path.isdir(path):
                        if clear_dir(path, skip_dirs) == False:
                                Result = False
                        else:
                                os.system("rm -rf \"%s\""%(path))
                else:
                    pass

        return Result

def is_dir_empty(dir_path):
        for _, _, files in os.walk(dir_path):
                if len(files):
                        return False

        return True

def stat_file_list(dir_path, header = ""):
        ret = []

        for root, _, files in os.walk(dir_path):
                for filename in files:
                        full_path = root.replace("\\", "/") + "/" + filename

                        size = os.path.getsize(full_path) / 1024.0

                        ret.append((size, full_path.replace(header.replace("\\", "/"), "")))

        return ret
# -*- coding: utf8 -*-

import re
import copy

transaction = False
module = None
copies = {}

def begin(mod):
        global transaction, module, copies

        if transaction:
                return

        transaction = True
        module = mod
        copies.clear()

        for key in mod.__dict__.iterkeys():
                if re.findall("^g_.*", key) == []:
                        continue

                copies[key] = copy.deepcopy(mod.__dict__[key])

def commit():
        global transaction, module, copies

        transaction = False
        module = None
        copies.clear()

def rollback():
        global transaction, module, copies

        if not transaction:
                return

        for key, value in copies.iteritems():
                module.__dict__[key] = value

        transaction = False
        module = None
        copies.clear()
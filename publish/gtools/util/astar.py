#-*- coding: utf-8 -*-

import sys
import json

import gtools.util.file as file

_2dmap     = []
open_list  = {}
close_list = {}
block_list = []
map_border = ()
start = None
end = None

TRANS_TYPE_JUMP = 0 #跳跃
TRANS_TYPE_TELE = 1 #瞬移
TRANS_TYPE_TRAC = 2 #轨迹

class Node:
        def __init__(self, father, x, y):
                global map_border, end
                if x < 0 or x >= map_border[0] or y < 0 or y >= map_border[1]:
                        raise Exception(str(x) + " " + str(y) + " node position can't beyond the border!")

                self.father = father
                self.x = x
                self.y = y
                if father != None:
                        G2father = calc_G(father, self)
                        if not G2father:
                                raise Exception("father is not valid!")
                        self.G = G2father + father.G
                        self.H = calc_H(self, end)
                        self.F = self.G + self.H
                else:
                        self.G = 0
                        self.H = 0
                        self.F = 0

        def reset_father(self, father, new_G):
                if father != None:
                        self.G = new_G
                        self.F = self.G + self.H

                self.father = father

def calc_G(node1, node2):
        x1 = abs(node1.x-node2.x) 
        y1 = abs(node1.y-node2.y) 
        if (x1== 1 and y1 == 0):
                return 10 # same row
        if (x1== 0 and y1 == 1):
                return 10 # same col
        if (x1== 1 and y1 == 1):
                return 14 # cross
        else:
                return 0

def calc_H(cur, end):
        return abs(end.x-cur.x) + abs(end.y-cur.y)

# NOTE 这个地方可能成为性能瓶颈
def min_F_node():
        global open_list, start
        if len(open_list) == 0:
                raise Exception("not exist path!")

        _min = 9999999999999999
        _k = (start.x, start.y)
        for k,v in open_list.items():
                if _min > v.F:
                        _min = v.F
                        _k = k
        return open_list[_k]

def is_closed(x, y):
        global map_border, close_list
        if x < 0 or x >= map_border[0] or y < 0 or y >= map_border[1]:
                return True
        return (x, y) in close_list

# 把相邻节点加入open list, 如果发现终点说明找到了路径
def addAdjacentIntoOpen(node):
        global open_list, close_list, end
        # 将该节点从开放列表移到关闭列表当中。
        open_list.pop((node.x, node.y))
        close_list[(node.x, node.y)] = node

        _adjacent = []
        # 相邻节点要注意边界的情况
        if not is_closed(node.x, node.y - 1):
                _adjacent.append(Node(node , node.x     , node.y - 1))
        if not is_closed(node.x + 1 , node.y):
                _adjacent.append(Node(node , node.x + 1 , node.y))
        if not is_closed(node.x, node.y + 1):
                _adjacent.append(Node(node , node.x     , node.y + 1))
        if not is_closed(node.x - 1 , node.y):
                _adjacent.append(Node(node, node.x - 1 , node.y))

        if not is_closed(node.x - 1 , node.y - 1) and not is_closed(node.x, node.y - 1) and not is_closed(node.x - 1, node.y):
                _adjacent.append(Node(node , node.x - 1 , node.y - 1))
        if not is_closed(node.x + 1 , node.y - 1) and not is_closed(node.x + 1, node.y) and not is_closed(node.x, node.y - 1):         
                _adjacent.append(Node(node , node.x + 1 , node.y - 1))
        if not is_closed(node.x + 1 , node.y + 1) and not is_closed(node.x + 1, node.y) and not is_closed(node.x, node.y + 1):    
                _adjacent.append(Node(node , node.x + 1 , node.y + 1))
        if not is_closed(node.x - 1 , node.y + 1) and not is_closed(node.x - 1, node.y) and not is_closed(node.x, node.y + 1):    
                _adjacent.append(Node(node , node.x - 1 , node.y + 1))

        for a in _adjacent:
                if (a.x,a.y) == (end.x, end.y):
                        new_G = calc_G(a, node) + node.G
                        end.reset_father(node, new_G)
                        print("find path finish!")
                        return True
                if (a.x,a.y) in close_list:
                        continue
                if (a.x,a.y) not in open_list:
                        open_list[(a.x,a.y)] = a
                else:
                        exist_node = open_list[(a.x,a.y)]
                        new_G = calc_G(a, node) + node.G
                        if new_G < exist_node.G:
                                exist_node.reset_father(node, new_G)

        return False

def init_map(width, height, pass_info):
        global _2dmap, map_border, block_list
        _2dmap = [[0] * width for i in range(height)]
        map_border = [width, height]

        for index in pass_info.iterkeys():
                x = int(index) % width
                y = int(index) / width
                _2dmap[y][x] = 1

        for y in range(height):
                for x in range(width):
                        if _2dmap[y][x] == 0:
                                block_list.append((x,y))

def mark_path(node, results = None):
        if results is None:
                results = []
        if node.father == None:
                results.reverse()
                return results
        results.append((node.x, node.y))
        return mark_path(node.father, results)

def find_path(x1, y1, x2, y2):
        global open_list, close_list, block_list, start, end
        start = Node(None, x1, y1)
        end = Node(None, x2, y2)
        close_list = {}
        for coord in block_list:
                close_list[coord] = Node(None, coord[0], coord[1])
        open_list = {}
        open_list[(start.x, start.y)] = start

        the_node = start
        try:
                while not addAdjacentIntoOpen(the_node):
                        the_node = min_F_node()
        except Exception as e:
                # path not exist
                pass
                return []

        return mark_path(end.father)

#=======================================================================
def print_map(pathes = []):
        for i in range(len(_2dmap)):
                print(i),
        print
        row = 0
        for l in _2dmap:
                col = 0
                line = ""
                for i in l:
                        if (col, row) in pathes:
                                line += "# "
                        elif i == 0:
                                line += "X " 
                        else:
                                line += ". "
                        col += 1
                row = row+1

def preset_map():
        global _2dmap
        _2dmap.append('. X . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. X . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. X . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . . . . . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X X X X .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X X X'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . . . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X . .'.split())
        _2dmap.append('. . . . . . . . . . . . . . . X . X . .'.split())
        width = len(_2dmap[0])
        height = len(_2dmap)

        pass_info = {}
        row_index = 0
        for row in _2dmap:
                col_index = 0
                for n in row:
                        if n == ".":
                                pass_info[row_index * width + col_index] = -1
                        col_index = col_index + 1
                row_index = row_index + 1

        init_map(width, height, pass_info)

if __name__=='__main__':
        preset_map()

        print("orignal map:")
        print_map()

        print("found road as follow:")
        print_map(find_path(0,0,19,15))
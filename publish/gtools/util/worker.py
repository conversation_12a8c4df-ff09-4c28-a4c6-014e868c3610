#-*- coding: utf8 -*-

import os
import sys

import traceback

import time

import multiprocessing
from multiprocessing import Process, Queue

QUEUE_MAX_SIZE = 100000

BATCH_NUM = 100

FPS = 100

class CWorker(object):
        def __init__(self, name):
                self._dict_msg_form = {}

                self._c_queue = None

                self._c_process = None

                self._str_name = name

        def register_handler(self, msg_id, handler):
                if not handler:
                        return

                self._dict_msg_form[msg_id] = handler

        def unregister_handler(self, msg_id):
                if not self._dict_msg_form.has_key(msg_id):
                        return

                del self._dict_msg_form[msg_id]

        def start(self, extra = None):
                global QUEUE_MAX_SIZE

                self._c_queue = Queue(QUEUE_MAX_SIZE)

                self._c_process = Process(target = self.main_loop,
                                          args = ({"queue":self._c_queue,
                                                   "host_pid":os.getpid(),
                                                   "extra":extra}, self._str_name))

                self._c_process.start()

        def stop(self):
                self._c_queue.put_nowait("exit")

                print ("wait process(%s) exit ..." % (self._str_name,))

                while self._c_process.is_alive():
                        time.sleep(1)

                print("process(%s) exited." % (self._str_name,))

        def main_loop(self, env, name):
                global BATCH_NUM, FPS

                while True:
                        terminate = False

                        for i in range(BATCH_NUM):
                                        try:
                                                data = env["queue"].get_nowait()
                                        except:
                                                data = None

                                        if data:
                                                if data == "exit":
                                                        print("terminated.")

                                                        terminate = True

                                                        break

                                                [msg_id, handler, content] = data

                                                if handler:
                                                        try:
                                                                handler(env, content)
                                                        except:
                                                                traceback.format_exc()

                        if terminate:
                                break

                        time.sleep(1 / float(FPS))

        def cast(self, msg_id, content):
                self._c_queue.put_nowait([msg_id, self._dict_msg_form.get(msg_id, None), content])

def freeze_support():
        multiprocessing.freeze_support()

if __name__ == "__main__":
        procs = CWorkProcess("test_procs")
        procs.start()
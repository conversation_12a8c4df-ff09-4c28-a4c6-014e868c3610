# -*- coding: utf8 -*-

import os
import gtools.util.os2 as os2
import math
import gtools.util.encrypt as encrypt
import struct
import sys
import gtools.util.file as file
import zlib
import json

import gtools.util.file as file_util
import gtools.util.string2 as string_util

def split(src, tilewidth, tileheight, des = "", fileext = "", png2atf = ""):
        from PIL import Image

        img = Image.open(src)
        width, height = img.size

        if tilewidth is None or tilewidth > width:
                tilewidth = width
        if tileheight is None or tileheight > height:
                tileheight = height

        src_path = os.path.split(src)

        fn = src_path[1].split('.')
        filename = fn[0]
        if fileext == "":
                fileext = fn[1]

        if des == "":
                des = src_path[0]

        num = 0

        if tilewidth == width and tileheight == height:
                des_file = des + "\\" + filename + "_0_0." + fileext

                os2.system_cp(src, des_file)

                if png2atf != "" and fileext == "png":
                        os2.system("%s -c d -r -q 15 -n 0,0 -i %s -o %s.atf" % (png2atf, des_file, des_file[:-4]))

                try:
                        print("split " + des_file + " ok.")
                except:
                        pass

                rows = 1
                cols = 1

                num = 1
        else:
                if height % tileheight or width % tilewidth:
                        raise Exception("tile spec invalid.")

                rows = height / tileheight
                cols = width / tilewidth

                num = 0

                for r in range(rows):
                        for c in range(cols):
                                box = (c * tilewidth, r * tileheight, (c + 1) * tilewidth, (r + 1) * tileheight)
                                des_file = os.path.join(des, filename + "_" + str(c) + "_" + str(r) + "." + fileext)
                                img2 = img.crop(box)
                                if not is_full_transparent(img2):
                                        img2.save(des_file)
                                        if png2atf != "" and fileext == "png":
                                                os2.system("%s -c d -r -q 0 -n 0,0 -i %s -o %s.atf" % (png2atf, des_file, des_file[:-4]))
                                                os2.system_rm(des_file)
                                        try:
                                                print("split " + des_file + " ok.")
                                        except:
                                                pass
                                num += 1

        try:
                print("split all(%s) ok." % num)
        except:
                pass

        return rows, cols, tilewidth, tileheight, fn[1]

def resize(src, des, width, height, keep_rate = True, png2atf = ""):
        from PIL import Image

        if not os.path.exists(src):
                img = dumps(None, (width, 1), (height, 1))
        else:
                img = Image.open(src)
                width2, height2 = img.size

                if keep_rate:
                        height = int(float(width) * float(height2) / float(width2))

                img = img.resize((width, height), Image.ANTIALIAS)

        if des:
                img.save(des)

                if des[-4:] == ".png" and png2atf != "":
                        os2.system("%s -c d -r -q 0 -n 0,0 -i %s -o %s.atf" % (png2atf, des, des[:-4]))
                        os2.system_rm(des)

        return img

def replace(src, des):
        from PIL import Image

        if os.path.exists(des):
                Image.open(src).resize(Image.open(des).size).save(des)

def del_alpha(src, des):
        import numpy
        from PIL import Image

        if type(src) is str or type(src) is unicode:
                img  = Image.open(src)
        else:
                img = src
        img = img.convert("RGBA")
        colors = [numpy.array(color) for color in img.split()]
        w, h = img.size

        for x in range(w):
                for y in range(h):
                        if colors[3][x][y] == 0:
                                colors[0][x][y] = 255
                                colors[1][x][y] = 255
                                colors[2][x][y] = 255

        colors = [Image.fromarray(numpy.array(color)).convert('L') for color in colors]

        img = Image.merge("RGB", tuple(colors[:-1])).convert("P")
        
        if des:
                img.save(des)

        return img

KEY = "loc@lt3s"

def genName(in_file):
        names = in_file.split(".")
        if not len(names):
                name = names[0]
        else:
                name = ".".join(names[0:-1])
        return name

def insert_len(bin, len2):
        s = struct.pack("I", len2)
        for i in range(4):
                bin.insert(0, ord(s[i]))

def bin2png(in_file, ou_file = None):
        global KEY
        import numpy
        from PIL import Image

        bin = open(in_file, "rb").read()
        bin = encrypt.encode(bin, KEY)
        bin_len = len(bin)
        bin = [ord(bin[i]) for i in range(bin_len)]
        dummy_num = 3 - (bin_len + 4) % 3
        bin.extend([0] * dummy_num)
        insert_len(bin, bin_len)
        bin = numpy.array(bin).reshape(3, len(bin) / 3)

        bin_len_0 = len(bin[0])
        size = int(math.ceil(math.sqrt(bin_len_0 + 4)))
        row_dummy_num = size * size - bin_len_0 - 4

        colors = []
        for i in range(3):
                bini = bin[i].tolist()
                insert_len(bini, bin_len_0)
                bini.extend([0] * row_dummy_num)
                color = Image.fromarray(numpy.array(bini).reshape(size, size)).convert('L')
                colors.append(color)

        image = Image.merge("RGB", tuple(colors))
        if ou_file is None:
                image.save(genName(in_file) + ".png", 'png')
        else:
                image.save(ou_file, 'png')

def pop_len(bin):
        len2 = [chr(bin.pop(0)) for i in range(4)]
        len2.reverse()
        return struct.unpack("I", "".join(len2))[0]

def png2bin(in_file, ou_file = None):
        global KEY
        import numpy
        from PIL import Image

        bin = []
        bin_len = 0

        image = Image.open(in_file)
        colors = image.split()
        size = None
        for i in range(3):
                color = numpy.array(colors[i])
                if size is None:
                        size = len(color)
                color = color.reshape(size * size).tolist()

                bin_len_i = pop_len(color)

                if i == 0:
                        bin_len = pop_len(color)
                        bin_len_i -= 4

                bin_cnt_i = 0
                break1 = break2 = False
                for value in color:
                        bin.append(value)
                        bin_cnt_i += 1

                        break1 = len(bin) == bin_len
                        break2 = break1 or (bin_cnt_i == bin_len_i)

                        if break2:
                                break
                if break1:
                        break
        bin = encrypt.decode("".join([chr(asi) for asi in bin]), KEY)
        if ou_file is None:
                f = open(genName(in_file) + ".bin", "wb")
        else:
                f = open(ou_file, "wb")
        f.write(bin)
        f.close()

def dumps(des, w, h, data = {}, color = (0, 0, 0, 128)):
        import numpy
        from PIL import Image

        lgwidth, tilewidth = w
        lgheight, tileheight = h
        width = lgwidth * tilewidth
        height = lgheight * tileheight
        r, g, b, a = color
        colors = [Image.fromarray(numpy.array([r] * width * height).reshape(height, width)).convert('L'),
                  Image.fromarray(numpy.array([g] * width * height).reshape(height, width)).convert('L'),
                  Image.fromarray(numpy.array([b] * width * height).reshape(height, width)).convert('L')]
        alphas = [a] * width * height
        for index in data.iterkeys():
                x = index % lgwidth * tilewidth
                y = index / lgwidth * tileheight
                for i in range(tileheight):
                        for j in range(tilewidth):
                                alphas[(y + i) * width + x + j] = 0
        colors.append(Image.fromarray(numpy.array(alphas).reshape(height, width)).convert('L'))
        image = Image.merge("RGBA", tuple(colors))

        if des:
                image.save(des)

        return image

def paste(src, des, clip, left = 0, top = 0, alpha_blend = False):
        import numpy
        from PIL import Image
        
        if type(src) is str or type(src) is unicode:
                src_img  = Image.open(src)
        else:
                src_img = src
        src_img  = src_img.convert("RGBA")
        src_colors = [numpy.array(color) for color in src_img.split()]
        src_w, src_h = src_img.size
        if type(clip) is str or type(clip) is unicode:
                clip_img = Image.open(clip)
        else:
                clip_img = clip
        clip_img  = clip_img.convert("RGBA")
        clip_colors = [numpy.array(color) for color in clip_img.split()]
        clip_w, clip_h = clip_img.size

        x = left
        while x < src_w and x < left + clip_w:
                y = top
                while y < src_h and y < top + clip_h:
                        r = src_colors[0][y][x]
                        g = src_colors[1][y][x]
                        b = src_colors[2][y][x]
                        r2 = clip_colors[0][y - top][x - left]
                        g2 = clip_colors[1][y - top][x - left]
                        b2 = clip_colors[2][y - top][x - left]
                        a2 = clip_colors[3][y - top][x - left]

                        if alpha_blend:
                                scale = (255.0 - float(a2)) / 255.0

                                src_colors[0][y][x] = int(float(r) * scale)
                                src_colors[1][y][x] = int(float(g) * scale)
                                src_colors[2][y][x] = int(float(b) * scale)
                        else:
                                src_colors[0][y][x] = r2
                                src_colors[1][y][x] = g2
                                src_colors[2][y][x] = b2

                        y += 1
                x += 1

        src_colors = [Image.fromarray(numpy.array(color)).convert('L') for color in src_colors]

        if des:
                if des[-4:] == ".jpg":
                        img = Image.merge("RGB", tuple(src_colors[:-1]))
                else:
                        img = Image.merge("RGBA", tuple(src_colors))

                img.save(des)

                return img
        else:
                return Image.merge("RGBA", tuple(src_colors))

def pack_atlas(src, tex, des):
        src_data = None
        try:
                if os.path.exists(src):
                        src_data = file.read_file(src)
                        atlas = zlib.compress(src_data)
                else:
                        src_data = src
                        atlas = zlib.compress(src)
        except:
                src_data = src
                atlas = zlib.compress(src)

        if src_data.find(".png") != -1:
                tex += ".png"
        elif src_data.find(".atf") != -1:
                tex += ".atf"
        else:
                raise Exception("forbid type")

        data = file.read_file(tex, "rb")

        data2 = struct.pack(">H%ss%ss" % (len(atlas), len(data)), len(atlas), atlas, data)

        if des:
                file.write_file(des, data2, "wb")

        return data2

def unpack_atlas(src, des, key):
        try:
                if os.path.exists(src):
                        data = file.read_file(src, "rb")
                else:
                        data = src
        except:
                data = src

        atlas_len, data = struct.unpack(">H%ss" % (len(data) - 2,), data)

        atlas, tex = struct.unpack("%ss%ss" % (atlas_len, len(data) - atlas_len), data)

        atlas = zlib.decompress(atlas)
        
        if des:
                file.write_file(des + ".atlas", atlas)
                if atlas.find(".png") != -1:
                        file.write_file(des + ".png", tex, "wb")
                elif atlas.find(".atf") != -1:
                        file.write_file(des + ".atf", tex, "wb")
                else:
                        raise Exception("forbid type")
                                
        return atlas, tex

def is_full_transparent(image):
        if len(image.getpixel((0,0))) != 4:
                return False

        for y in range(image.height):
                for x in range(image.width):
                        if image.getpixel((x,y))[-1] != 0:
                                return False

        return True

def gen_ios_icons(iconname, png1024, conf_pat, ouput_dir):
        conf = json.loads(file_util.read_file(conf_pat))
        for image_info in conf["images"]:
                w, h = image_info["size"].split("x")
                if image_info["scale"] != "1x":
                        image_info["filename"] = "%s-%s@%s.png" % (iconname, w, image_info["scale"])
                else:
                        image_info["filename"] = "%s-%s.png" % (iconname, w)
                scale = int(image_info["scale"][0:-1])
                img = resize(png1024, None, int(float(w) * scale), int(float(h) * scale), False)
                try:
                    del_alpha(img, ouput_dir + "/" + image_info["filename"])
                except:
                    img.save(ouput_dir + "/" + image_info["filename"])
        file_util.write_file(ouput_dir + "/Contents.json",  json.dumps(conf))

def gen_ios_launches(launchname, png512, conf_pat, ouput_dir):
        conf = json.loads(file_util.read_file(conf_pat))
        for image_info in conf["images"]:
                scale = int(image_info["scale"][0:-1])
                if scale == 1:
                        image_info["filename"] = launchname + ".png"
                else:
                        image_info["filename"] = "%s-%d.png" % (launchname, scale - 1)

                if image_info["scale"] == "1x":
                        w, h = [750,  1334]
                elif image_info["scale"] == "2x":
                        w, h = [1242, 2208]
                elif image_info["scale"] == "3x":
                        w, h = [1125, 2436]

                resize(png512, ouput_dir + "/" + image_info["filename"], w, h, False)
        file_util.write_file(ouput_dir + "/Contents.json",  json.dumps(conf))
# -*- coding: utf8 -*-

import sys
import os2
import os

def parse_remote(remote):
        tokens = remote.split("/")

        host, port = tokens[0].split(":")

        path = host + ":/" + "/".join(tokens[1:len(tokens)])

        return [port, path]

def download(remote_file, local_dir, password = None):
        port, path = parse_remote(remote_file)

        if sys.platform == "win32":
                if password:
                        os2.system("pscp -scp -r -P %s -pw %s %s %s" % (port, password, path, local_dir))
                else:
                        os2.system("pscp -scp -r -P %s %s %s" % (port, path, local_dir))
        elif not password:
                os2.system("scp -r -P %s %s %s" % (port, path, local_dir))
        else:
                return os.system("sshpass -p '%s' scp -o StrictHostKeyChecking=no -r -P %s %s %s" %
                                 (password, port, path, local_dir))

def upload(local_file, remote_dir, password = None):
        port, path = parse_remote(remote_dir)

        if sys.platform == "win32":
                if password:
                        os2.system("pscp -scp -P %s -pw %s %s %s" % (port, password, local_file, path))
                else:
                        os2.system("pscp -scp -P %s %s %s" % (port, local_file, path))
        elif not password:
                os2.system("scp -r -P %s %s %s" % (port, local_file, path))
        else:
                return os.system("sshpass -p '%s' scp -o StrictHostKeyChecking=no -r -P %s %s %s" %
                                 (password, port, local_file, path))
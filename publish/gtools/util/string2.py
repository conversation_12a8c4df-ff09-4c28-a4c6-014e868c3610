# -*- coding: utf8 -*-

import re
import platform
import random
import string

def line_break_char():
        if platform.system() == "Windows":
                return "\r\n"
        else:
                return "\n"

def dir_split_char():
        if platform.system() == "Windows":
                return "\\"
        else:
                return "/"

#排除字符，字母或数字，或冒号
def is_exc_char(char):
        return len(re.findall("[:\w]{1,1}", char)) > 0

#正则表达式替换字符串
def str_repl(regex, string, des_str, bcount = (0,0)):
        ou_string = string

        re_strings = re.findall(regex, ou_string)

        src_strs = []

        if type(bcount) == int:
                head_bcount = bcount
                tail_bcount = bcount
        else:
                head_bcount, tail_bcount = bcount

        re_strings.sort(key=lambda re_string : len(re_string))
        re_strings.reverse()

        for re_string in re_strings:
                if re_string in src_strs:
                        continue

                ou_string = ou_string.replace(re_string, re_string[0:head_bcount] + des_str + re_string[(len(re_string) - tail_bcount):])

                src_strs.append(re_string)

        return ou_string

def str_repl2(regex, string, des_str, bcount = (0,0)):
        ou_string = string

        re_strings = re.findall(regex, ou_string)

        if not len(re_strings):
                return ou_string

        head_bcount, tail_bcount = bcount

        re_string = re_strings[0]

        ou_string = ou_string.replace(re_string, re_string[0:head_bcount] + des_str + re_string[(len(re_string) - tail_bcount):])

        return str_repl2(regex, ou_string, des_str, bcount)

def str_repl3(regex, string, des_char, bcount = (0,0), rec = False):
        ou_string = string

        re_strings = re.findall(regex, ou_string)

        if not len(re_strings):
                return ou_string

        src_strs = []

        if type(bcount) == int:
                head_bcount = bcount
                tail_bcount = bcount
        else:
                head_bcount, tail_bcount = bcount

        for re_string in re_strings:
                if re_string in src_strs:
                        continue

                ou_string = ou_string.replace(re_string,
                                              re_string[0:head_bcount] +
                                              "".join([des_char] * (len(re_string) - head_bcount - tail_bcount)) +
                                              re_string[(len(re_string) - tail_bcount):])

                src_strs.append(re_string)

        return str_repl3(regex, ou_string, des_char, bcount, rec)

def str_wipe_break(string):
        string = string.replace("\r", "")
        return string.replace("\n", "")

def str_wipe_space(string):
        string1 = string.replace(" ", "")
        return string1.replace("\t", "")

def str_wipe_border_space(string):
        length = len(string)

        first_idx = 0
        end_idx   = length

        for i in range(length):
                if string[i] != " " and string[i] != "\t":
                        first_idx = i

                        break

        for i in range(length):
                index = length - i - 1

                if string[index] != " " and string[index] != "\t":
                        end_idx = index + 1

                        break

        return string[first_idx:end_idx]

def str_wipe_comments(string, flag="%"):
        start = string.find("%")

        if start == -1:
                return string

        return string[0:start]

def str_count_space(string):
        for i in range(len(string)):
                if string[i] != " ":
                        break

        return i

def str_combin_spaces(count):
        return "".join([" " for i in range(count)])

def random_str():
        return ''.join(random.sample(string.ascii_letters + string.digits, 8))
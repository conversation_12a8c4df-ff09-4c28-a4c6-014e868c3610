# -*- coding: utf8 -*-

import collections
import xml.dom.minidom

def identify_string(item):
        if item.isdigit():
                return int(item)

        subitems = item.split("-")

        subitem_num = len(subitems)

        subitem = None

        if subitem_num == 1:
                subitem = item
        elif subitem_num == 2:
                if subitems[0] != "":
                        return item
                else:
                        subitem = subitems[1]
        else:
                return item

        subitems = subitem.split(".")

        subitem_num = len(subitems)

        if subitem_num == 1:
                if subitems[0].isdigit():
                        return int(item)

                return item
        elif subitem_num > 2:
                return item

        for subitem in subitems:
                if not subitem.isdigit():
                        return item

        return float(item)

def parse_node(node, content):
        for attr in node.attributes.keys():
                content[attr] = identify_string(node.getAttribute(attr))
        children = node.childNodes

        if not len(children):
                return

        for child in children:
                if child.nodeType != child.ELEMENT_NODE:
                        continue

                child_name = child.nodeName

                if not content.has_key(child_name):
                        content[child_name] = {}

                subcontent = content[child_name]

                id = -1

                if child.hasAttribute("id"):
                        value = child.getAttribute("id")

                        if value.isdigit():
                                id = int(value)
                        else:
                                continue

                if id != -1:
                        subcontent[id] = collections.OrderedDict()

                        subcontent = subcontent[id]

                parse_node(child, subcontent)

def toDict(root):
        content = collections.OrderedDict()

        parse_node(root, content)

        return content

def toString(root, space_num = 0, tab = 8):
        if root.nodeType != root.ELEMENT_NODE:
                return ""

        space = "".join([" "] * space_num)

        content = space + "<%s" % (root.nodeName)

        attrs = root.attributes.keys()
        attrs.sort()

        for attr in attrs:
                content += " %s=\"%s\"" % (attr, root.getAttribute(attr))

        if not len(root.childNodes):
                content += "/>"

                return content

        childContents = ""

        for node in root.childNodes:
                childContent = toString(node, space_num + tab, tab)

                if childContent == "":
                        continue

                childContents += "\n" + childContent

        if childContents == "":
                content += "/>"
        else:
                content += ">%s\n%s</%s>" % (childContents, space, root.nodeName)

        return content

def parse(file):
        return xml.dom.minidom.parse(file)
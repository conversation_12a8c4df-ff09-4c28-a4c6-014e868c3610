# -*- coding: utf8 -*-
import os
import sys
import gtools.util.os2 as os2
import gtools.util.string2 as string2

def read_file(filename, flag = "r"):
        try:
                fp = open(filename, flag)
        except:
                return ""

        content = fp.read()

        fp.close()

        return content

def read_file_lines(filename):
        try:
                fp = open(filename, "r")
        except:
                return []

        lines = fp.readlines()
        fp.close()

        lines2 = []

        for line in lines:
                lines2.append(string2.str_wipe_break(line))

        return lines2

def write_file(filename, content, flag = "w"):
        filedir = "/".join(filename.split("/")[0:-1])
        if not os.path.exists(filedir):
                os2.system_mkdir(filedir, True)
        fp = open(filename, flag)
        fp.write(content)
        fp.close()

def file_dir(filename):
        if sys.platform == "win32":
                return "\\".join(os.path.abspath(filename).split("\\")[0:-1])

        return "/".join(os.path.abspath(filename).split("/")[0:-1])
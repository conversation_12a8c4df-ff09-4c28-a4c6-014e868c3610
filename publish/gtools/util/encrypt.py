#-*- coding: utf-8 -*-

import os
import gtools.util.file as file


DATA_MIN_LEN = 4  #需要加密数据的最小长度

def encode_file(src, des, key):
        try:
                if os.path.exists(src):
                        data = file.read_file(src, "rb")
                else:
                        data = src
        except:
                data = src
        data2 = encode(data[:100], key) + data[100:] + "WJ"
        if des:
                file.write_file(des, data2, "wb")
        return data2

def decode_file(src, des, key):
        try:
                if os.path.exists(src):
                        data = file.read_file(src, "rb")
                else:
                        data = src
        except:
                data = src
        data2 = decode(data[:100], key) + data[100:-2]
        if des:
                file.write_file(des, data2, "wb")
        return data2

def encode(data, key):
        '''
        #给数据加密
        @param data:  原始数据
        @param key:   加密的key，长度为8的字符串
        '''
        if len(data) < DATA_MIN_LEN:
                return data
        else:
                return _encode(data, key)
        
def decode(data, key):
        '''
        #给数据解密
        @param data:  加密后的数据
        @param key:   解密的key，长度为8的字符串
        '''
        if len(data) < DATA_MIN_LEN:
                return data
        else:
                return _decode(data, key)


def _encode(data, key):
        '''
        #给数据加密
        @param data:  原始数据
        @param key:   加密的key，长度为8的字符串
        '''
        en_data = map(ord, data)
        key = map(ord, key)
        en_data[0] = en_data[0] ^ key[0]
        count = len(data)
        for i in range(1, count):
                en_data[i] = en_data[i] ^ en_data[i-1] ^ key[i&7]
        en_data[3] = en_data[3]^key[2];
        en_data[2] = en_data[2]^en_data[3]^key[3]
        en_data[1] = en_data[1]^en_data[2]^key[4]
        en_data[0] = en_data[0]^en_data[1]^key[5]
        return "".join(map(chr, en_data))
        
def _decode(data, key):
        '''
        #给数据解密
        @param data:  加密后的数据
        @param key:   解密的key，长度为8的字符串
        '''
        de_data = map(ord, data)
        key = map(ord, key)
        de_data[0] = de_data[0]^de_data[1]^key[5]
        de_data[1] = de_data[1]^de_data[2]^key[4]
        de_data[2] = de_data[2]^de_data[3]^key[3]
        de_data[3] = de_data[3]^key[2]
        count = len(data) - 1
        for i in range(count, 0, -1):
                de_data[i] =  de_data[i] ^ de_data[i-1] ^ key[i&7]
        de_data[0] = de_data[0] ^ key[0]
        return "".join(map(chr, de_data))

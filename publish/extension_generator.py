import random
from typing import List, Dict

class ExtensionGenerator:
    def __init__(self):
        self.property_templates = [
            '@property (nonatomic, strong) UIView *{name}ContentView;',
            '@property (nonatomic, strong) UILabel *{name}TitleLabel;',
            '@property (nonatomic, strong) UIButton *{name}Button;',
            '@property (nonatomic, strong) UIImageView *{name}ImageView;',
            '@property (nonatomic, strong) NSArray *{name}Items;',
            '@property (nonatomic, strong) NSDictionary *{name}Info;',
            '@property (nonatomic, assign) NSInteger {name}Count;',
            '@property (nonatomic, assign) CGFloat {name}Progress;',
            '@property (nonatomic, copy) void(^{name}Callback)(void);',
            '@property (nonatomic, strong) NSString *{name}Identifier;'
        ]

    def generate_extension_header(self, class_name: str, base_name: str) -> str:
        """生成扩展头文件"""
        header = f'#import "{class_name}.h"\n'
        header += f'#import "{base_name}Protocol.h"\n'
        header += f'#import "{base_name}Model.h"\n\n'
        header += f'@interface {class_name} ({base_name}) <{base_name}Protocol>\n\n'
        header += '@end'
        return header

    def generate_extension_implementation(self, class_name: str, base_name: str, protocol_methods: List[str]) -> str:
        """生成扩展实现文件，包含Model交互"""
        impl = f'#import "{base_name}View+{base_name}.h"\n\n'
        
        # 添加私有类别
        impl += f"@interface {base_name}View ({base_name}Private)\n\n"
        
        # 添加Model属性
        impl += f"@property (nonatomic, strong) {base_name}Model *model;\n\n"
        
        # 随机选择2-4个不重复的属性
        num_properties = random.randint(2, 4)
        selected_properties = random.sample(self.property_templates, num_properties)
        
        # 添加私有属性
        impl += "// Private Properties\n"
        for prop in selected_properties:
            impl += prop.format(name=base_name.lower()) + "\n"
        impl += "\n@end\n\n"
        
        # 实现部分
        impl += f"@implementation {base_name}View ({base_name})\n\n"
        
        # 实现协议方法
        for method in protocol_methods:
            if method.endswith(';'):
                method = method[:-1]
                
            # 根据方法名添加简单的实现
            if "process" in method and "Data" in method:
                impl += f"{method} {{\n"
                impl += f"    self.model = [[{base_name}Model alloc] init];\n"
                impl += "    if (completion) {\n"
                impl += "        completion(YES);\n"
                impl += "    }\n"
                impl += "}\n\n"
            elif "fetch" in method and "Params" in method:
                impl += f"{method} {{\n"
                impl += "    if (success) {\n"
                impl += f"        success([self.model toDictionary]);\n"
                impl += "    }\n"
                impl += "}\n\n"
            elif "update" in method:
                impl += f"{method} {{\n"
                impl += f"    self.model = [[{base_name}Model alloc] init];\n"
                impl += "    [self setNeedsLayout];\n"
                impl += "}\n\n"
            elif "configure" in method:
                impl += f"{method} {{\n"
                impl += "    [self setNeedsLayout];\n"
                impl += "}\n\n"
            elif "handle" in method:
                impl += f"{method} {{\n"
                impl += f"    self.model = [[{base_name}Model alloc] init];\n"
                impl += "    [self setNeedsLayout];\n"
                impl += "}\n\n"
            else:
                impl += f"{method} {{\n"
                impl += "    // Default implementation\n"
                impl += "}\n\n"
        
        impl += "@end"
        return impl

    def generate_view_header(self, name: str) -> str:
        """生成视图头文件"""
        header = '#import <UIKit/UIKit.h>\n\n'
        header += f'@interface {name}View : UIView\n\n'
        header += '@end'
        return header

    def generate_view_implementation(self, name: str) -> str:
        """生成视图实现文件"""
        impl = f'#import "{name}View.h"\n\n'
        impl += f'@implementation {name}View\n\n'
        
        # 添加初始化方法
        impl += '- (instancetype)initWithFrame:(CGRect)frame {\n'
        impl += '    self = [super initWithFrame:frame];\n'
        impl += '    if (self) {\n'
        impl += '        // Initialization code\n'
        impl += '    }\n'
        impl += '    return self;\n'
        impl += '}\n\n'
        
        impl += '@end'
        return impl 
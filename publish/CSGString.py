# -*- coding: utf8 -*-
import random
import string

class CSGString:
    _instance = None
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(CSGString, cls).__new__(cls)
            cls._initialized = False
        return cls._instance

    def __init__(self):
        if getattr(self, '_initialized', False):
            return
        self.word_pool = set()  # 存储所有词
        self.used_words = set()  # 存储已使用的词
        self.verb_pool = set()  # 存储动词
        self.used_verbs = set()  # 存储已使用的动词
        self.black_words = set()
        self.white_words = set()
        self.white_dirs = set()
        self.element_key = set()
        self.load_white_words()
        self.load_white_dirs()
        self.load_element_key()
        self.load_black_words()
        self.load_words()
        self.load_verbs()
        self._initialized = True

    def load_white_words(self):
        """加载白名单词库"""
        try:
            with open('white_word.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:  # 忽略空行
                        self.white_words.add(word)
            print(f"Loaded {len(self.white_words)} white words from white_words.txt")
        except Exception as e:
            print(f"Error loading white_words.txt: {str(e)}")
            self.white_words = set(['default_white_word'])

    def load_white_dirs(self):
        """加载白名单文件夹"""
        try:
            with open('white_dir.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    dir = line.strip()
                    if dir:  # 忽略空行
                        self.white_dirs.add(dir)
            print(f"Loaded {len(self.white_dirs)} white dirs from white_dirs.txt")
        except Exception as e:
            print(f"Error loading white_dirs.txt: {str(e)}")
            self.white_dirs = set(['default_white_dir'])

    def load_element_key(self):
        """加载元素关键字"""
        try:
            with open('white_element.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    key = line.strip()
                    if key:  # 忽略空行
                        self.element_key.add(key)
            print(f"Loaded {len(self.element_key)} element key from white_element.txt")
        except Exception as e:
            print(f"Error loading white_element.txt: {str(e)}")
            self.element_key = set(['default_element_key'])

    def load_black_words(self):
        """加载黑名单词库"""
        try:
            with open('sensitive.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:  # 忽略空行
                        self.black_words.add(word)
            print(f"Loaded {len(self.black_words)} black words from black_words.txt")
        except Exception as e:
            print(f"Error loading black_words.txt: {str(e)}")
            self.black_words = set(['default_black_word'])

    def load_words(self):
        """加载词库"""
        try:
            with open('PaChong01.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:  # 忽略空行
                        # 检查是否在黑名单中
                        if word not in self.black_words:
                            self.word_pool.add(word)
            print(f"Loaded {len(self.word_pool)} words from PaChong01.txt")
        except Exception as e:
            print(f"Error loading PaChong01.txt: {str(e)}")
            self.word_pool = set(['default', 'backup', 'fallback'])  # 默认词库

    

    def load_verbs(self):
        """加载动词库"""
        try:
            with open('verbs.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    verb = line.strip()
                    if verb:  # 忽略空行
                        # 检查是否在黑名单中
                        if verb not in self.black_words:
                            self.verb_pool.add(verb)
            print(f"Loaded {len(self.verb_pool)} verbs from verbs.txt")
        except Exception as e:
            print(f"Error loading verbs.txt: {str(e)}")
            self.verb_pool = set(['do', 'make', 'get'])  # 默认动词库

    def get_random_word(self) -> str:
        """获取一个随机且未使用过的词"""
        available_words = self.word_pool - self.used_words
        if not available_words:
            # 如果所有词都用完了，重置已使用集合
            self.used_words.clear()
            available_words = self.word_pool

        word = random.choice(list(available_words))
        self.used_words.add(word)
        return word

    def generate_random_str(self, randomlength=2):
        """生成指定长度的随机字母字符串"""
        letters = string.ascii_letters
        return ''.join(random.choice(letters) for _ in range(randomlength))

    def getRandomStr(self):
        """获取随机字符串"""
        return self.get_random_word()


    def get_random_verb(self) -> str:
        """获取一个随机且未使用过的动词"""
        available_verbs = self.verb_pool - self.used_verbs
        if not available_verbs:
            # 如果所有动词都用完了，重置已使用集合
            self.used_verbs.clear()
            available_verbs = self.verb_pool

        verb = random.choice(list(available_verbs))
        self.used_verbs.add(verb)
        return verb

# 创建单例实例
_instance = CSGString()

# 暴露公共方法
def generate_random_str(randomlength=2):
    """生成指定长度的随机字母字符串"""
    return _instance.generate_random_str(randomlength)

def getRandomStr():
    return _instance.getRandomStr()

def getRandomVerb():
    """获取随机动词"""
    return _instance.get_random_verb()





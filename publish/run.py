# -*- coding: utf8 -*-
import modify_sdk
import os
from CSGString import CSGString
import Test
import Encrypted_File
import argparse
import CSGFile
import subprocess
from pbxproj.pbxextensions import FileOptions
from pbxproj import *
import shutil
import json
import urllib.request
import category
import random
import zipfile
from protocol_generator import ProtocolGenerator
from extension_generator import ExtensionGenerator
from model_generator import ModelGenerator
import traceback
# 混淆主目录 & 工程名
sdk_proj_path = "JeniceGame"
# 新的SDK名称前缀
new_sdk_name = CSGString().generate_random_str().upper() + CSGString().getRandomStr()
# 还原的工程名
restore_proj_name = "TestGameCock"
# SDK版本号
sdk_version = ""
# 项目名
job_name = ""
# 0混淆，1还原
mix_or_restore_tag = 0
# 还原图片名json文件
restore_images_name_path = "../config/__image_config.json"
# 垃圾代码还原json文件
restore_trash_code_path = "../config/__trash_config.json"
# 正常代码中的垃圾代码配置文件路径
add_code_trash_config_path = "../config/__add_code_trash_config.json"
# 还原的垃圾代码文件配置路径
restore_trash_file_path = "../config/ReFiles"
# 垃圾代码文件还原的目标路径
restore_trash_file_target_path = ""
# 代码映射json文件
restore_obfuse_code_path = "../config/__obfuscator.json"
# 事业部ID
business_division = "1"
# json配置表
json_config_param = None
# 还原模式中是否重新混淆，除开公开的接口
new_obfuse_or_not = '0'
# 混淆配置表
config_params_dic = None

white_word_pool = ','.join(list(CSGString().white_words))
white_dir_pool = ','.join(list(CSGString().white_dirs))
white_element_pool = ','.join(list(CSGString().element_key))


def obfuseOrRestore(is_restore):
    root_sdk_path = '../Mock/%s' % sdk_proj_path
    root_sdk_proj_path = root_sdk_path + '/' + '%s.xcodeproj' % sdk_proj_path
    str_dict = str(config_params_dic)
    if is_restore:
        subprocess.call(['python3', 'rebuild_oc_obfuscator.py',
                     '-p', root_sdk_path,
                     '-proj', root_sdk_proj_path,
                     '-root', root_sdk_path,
                     '-code_restore_trash_config', restore_trash_code_path,
                     '-add_code_trash_config', add_code_trash_config_path,
                     '-code_restore_obfuse_config', restore_obfuse_code_path,
                     '-restore_or_obfuse', new_obfuse_or_not,
                     '-job_name', job_name,
                     '-config_params_dic', str_dict,
                     '-code_restore_mode', '1', 
                     '-restore_trash_file_path', restore_trash_file_path,
                     '-sdk_ver',sdk_version,
                     '-ek', white_word_pool,
                     '-ep', white_dir_pool,
                     '-element_key',white_element_pool,
                     '-encrypt', '2',
                     '-en_lexiconA', 'lexiconA.txt',
                     '-en_lexiconB', 'lexiconB.txt',
                     '-code_file_num', '5',
                     '-code_num_min', '5',
                     '-code_num_max', '20',
                     '-code_file_fun_num', '10',
                     '-code_rate', '50',
                     '-code_file_target', sdk_proj_path])
    else:
        subprocess.call(['python3', 'rebuild_oc_obfuscator.py',
                     '-p', root_sdk_path,
                     '-proj', root_sdk_proj_path,
                     '-root', root_sdk_path,
                     '-sdk_ver',sdk_version,
                     '-ek', white_word_pool,
                     '-ep', white_dir_pool,
                     '-element_key', white_element_pool,
                     '-encrypt', '2',
                     '-en_lexiconA', 'lexiconA.txt',
                     '-en_lexiconB', 'lexiconB.txt',
                     '-code_file_num', '15',
                     '-code_num_min', '5',
                     '-code_num_max', '20',
                     '-code_file_fun_num', '15',
                     '-code_rate', '30',
                     '-config_params_dic', str_dict,
                     '-code_file_target', sdk_proj_path])
        
def linkBundle(project, bundleName, bundle_path):
    project.remove_group_by_name(bundleName)
    project.save()
    parentGroup = project.get_or_create_group(bundleName)
    fileOptions = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False, create_build_files=True)
    print(f"---bundle_path:{bundle_path}")
    for dir, root, files in os.walk(bundle_path):
        for fold in root:
            if fold == "__MACOSX":
                continue
            path_fold = bundleName + "/" +fold
            print(f"---path:{path_fold}")
            project.add_file(path_fold, parent=parentGroup, tree=u'SOURCE_ROOT', force=True, file_options=fileOptions, target_name=bundleName)
        for file in files:
            if file == "SDKConfig.plist" or file == "Info.plist" or file == ".DS_Store":
                continue
            path_file = os.path.join(bundleName, file)
            print(f"---path:{path_file}")
            project.add_file(path_file, parent=parentGroup, tree=u'SOURCE_ROOT', force=False, file_options=FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False, header_scope=u''), target_name=bundleName)
        break
    project.save()
        
        


# 加密json文件，并替换工程中的宏及output_dir
def encrypt_json_file():
    copyed_json_path = "../Mock/%s/%sSDKBundle/json" % (sdk_proj_path, sdk_proj_path)
    target_json_path = "json"
    output_folder = CSGString().getRandomStr().lower()
    shutil.copytree(copyed_json_path, target_json_path)
    AESKey, IVKey, Config_dat_path = Encrypted_File.encode_dir_files(target_json_path, output_folder)
    destination_folder = '../Mock/%s/%sSDKBundle/' % (sdk_proj_path, sdk_proj_path) + output_folder
    if os.path.exists(destination_folder):
        # 先删除已存在的目标文件夹
        shutil.rmtree(destination_folder)
    shutil.copytree(output_folder, destination_folder)
    # 删除旧的
    shutil.rmtree(output_folder)
    shutil.rmtree(target_json_path)
    shutil.rmtree(copyed_json_path)
    
    bundleName = "%sSDKBundle" % sdk_proj_path
    bundle_path = "../Mock/%s/%sSDKBundle" % (sdk_proj_path ,sdk_proj_path)
    
    xcodeproj_path = "../Mock/%s/%s.xcodeproj" % (sdk_proj_path, sdk_proj_path)
    project = XcodeProject.load(xcodeproj_path + '/project.pbxproj')
    project.remove_files_by_path(bundleName + "/" + "json", u"<group>")
    project.save()
    
    linkBundle(project, bundleName, bundle_path)
    
    project.remove_files_by_path("json", u"<group>")
    project.save()
    return AESKey, IVKey, Config_dat_path, output_folder
        
        

# SDK配置化参数替换  - 返回值为是否需要重新混淆，1需要，0不需要
def replace_proj_config(AESKey, IVKey, Config_dat_path, output_folder):
    if json_config_param != 'None':
        json_dic = json.loads(json_config_param)
        if json_dic != None:
            # 将解密json的宏加进字典中一起替换掉
            json_dic['JsonDecodeKey'] = AESKey
            json_dic['JsonDecodePath'] = Config_dat_path
            json_dic['JsonDecodeIVKey'] = IVKey
            json_dic['JsonOutputDir'] = output_folder
            macro_path = ""
            if job_name == "iOS_vTD":
                macro_path = '../Mock/%s/%sSDK/MiddleComponent/Common/Macro/Macro.h' % (sdk_proj_path, sdk_proj_path)
            elif job_name == "iOS_vGN":
                macro_path = '../Mock/%s/%sSDK/MiddleComponent/Public/Macro.h' % (sdk_proj_path, sdk_proj_path)
            else:
                macro_path = '../Mock/%s/%sSDK/Public/Macro.h' % (sdk_proj_path, sdk_proj_path)
            if not os.path.exists(macro_path):
                print('Macro文件路径找不到')
                return
            f = open(macro_path, 'r')
            file_ext = CSGFile.get_file_ext(macro_path)
            filename = CSGFile.get_filename(macro_path)
            tmp_file_path = macro_path + ".tmp" + file_ext
            f_new = open(tmp_file_path, 'w')
            for line in f:
                for key in json_dic.keys():
                    config_key = 'Config_' + key
                    if line.startswith('#define Config_') and config_key in line:
                        old_line = line
                        new_line = '#define %s @\"%s\"\n' % (config_key, json_dic.get(key))
                        line = line.replace(old_line, new_line)
                # 替换SDK版本号
                if 'vOS2.0.0' in line:
                    old_line = line
                    new_line = '#define MFSVersion @\"%s\"\n' % sdk_version
                    line = line.replace(old_line, new_line)
                elif 'SDKVersion @"v1.0.0' in line:
                    old_line = line
                    new_line = '#define SDKVersion @\"%s\"\n' % sdk_version
                    line = line.replace(old_line, new_line)
                
                f_new.write(line)
            f.close()
            f_new.close()
            CSGFile.remove_file(macro_path)
            CSGFile.file_rename(tmp_file_path, filename)

def replace_iap_swift_value():
    config_obfuse_json_path = '../Mock/%s/%sSDK/__obfuscator.json' % (new_sdk_name, new_sdk_name)
    if os.path.exists(config_obfuse_json_path):
        obfuse_json_data = json.load(open(config_obfuse_json_path))
        required_obfuse_code = ['MAToastHUDView','showFailedToast','MAMultiLanguageMgr','getLanguageForWordNum','MAPayManage','sharedInstance','deliverProduct']
        find_result_dic = {}
        target_swift_file_path = '../Mock/%s/%sSDK/Modules/MAPay/Pay/Manage/IAPManager.swift' % (new_sdk_name, new_sdk_name)
        for key, value in obfuse_json_data.items():
            if key in required_obfuse_code:
                find_result_dic[key] = value
        if len(find_result_dic) > 0 and os.path.exists(target_swift_file_path):
            with open(target_swift_file_path, 'rb') as f:
                continuer = f.read()
                for key, value in find_result_dic.items():
                    continuer = continuer.replace(key.encode("utf8"), value.encode("utf8"))
                with open(target_swift_file_path, 'wb') as f:
                    f.write(continuer)



# 混淆
def mix_proj():
    # 混淆Bundle中的配置文件
    Test.modify_bundle_files(sdk_proj_path, restore_images_name_path, mix_or_restore_tag, new_obfuse_or_not)
    # 混淆
    obfuseOrRestore(0)
    # 加swift垃圾代码
    add_swift_trach_code()
    # 加oc垃圾代码
    add_oc_trash_code()
    # 修改SDK名称、文件名称、bundle名称
    modify_sdk.modify_current_projname(sdk_proj_path, new_sdk_name)
    
    os.system('rm -rf ../Mock/%s' % sdk_proj_path)
    
    # 拷贝、移除旧的配置文件并压缩成zip包
    if os.path.exists('../config'):
        print('移除旧的配置路径')
        os.system('rm -rf ../config')
    os.system('mkdir ../config')
    os.system('cp -R ../Mock/%s/%sSDK/ReFiles ../config' % (new_sdk_name, new_sdk_name))
    os.system('cp -R ../Mock/%s/%sSDK/__image_config.json ../config' % (new_sdk_name, new_sdk_name))
    os.system('cp -R ../Mock/%s/%sSDK/__obfuscator.json ../config' % (new_sdk_name, new_sdk_name))
    os.system('cp -R ../Mock/%s/%sSDK/__trash_config.json ../config' % (new_sdk_name, new_sdk_name))
    os.system('cp -R ../Mock/%s/%sSDK/__add_code_trash_config.json ../config' % (new_sdk_name, new_sdk_name))
    zip_folder('../config', '../config.zip')
    # 替换IAPManager.swift中的值
    replace_iap_swift_value()

        
        
def find_xcodeproj(start_path):
    """递归查找 .xcodeproj 文件，排除特定的xcodeproj"""
    excluded_xcodeproj = [
        'cocos2d_libs.xcodeproj',
        'fairygui.xcodeproj',
        'cocos2d_lua_bindings.xcodeproj',
        'libsimulator.xcodeproj',
        'simulator.xcodeproj',
        'cocos2d_tests.xcodeproj'
    ]
    
    for root, dirs, files in os.walk(start_path):
        for dir_name in dirs:
            if dir_name.endswith('.xcodeproj') and dir_name not in excluded_xcodeproj:
                return os.path.join(root, dir_name)
    return None

# 加swift垃圾代码
def add_swift_trach_code():
    """添加 Swift 垃圾代码"""
    sw_is_oc_use = config_params_dic.get("sw_is_oc_use")
    if sw_is_oc_use == False:
        return
        
    try:
        from swift_generator import SwiftGenerator
        
        # 从配置中获取最小和最大文件数量
        min_files = config_params_dic.get("sw_class_number_min", 5)
        max_files = config_params_dic.get("sw_class_number_max", 10)
        
        # 随机生成文件数量
        num_files = random.randint(min_files, max_files)
        print(f"🔄 Generating {num_files} Swift files...")
        
        generator = SwiftGenerator()
        
        # 创建 Swift 代码目录
        swift_dir_name = CSGString().getRandomStr()
        target_path = os.path.join("../Mock", sdk_proj_path, sdk_proj_path + 'SDK', swift_dir_name)
        
        # 生成 Swift 文件
        generated_files = generator.generate_files(target_path, num_files)
        
        # 查找 .xcodeproj 目录
        mock_path = os.path.join("../Mock", sdk_proj_path)
        xcodeproj_path = find_xcodeproj(mock_path)
                
        if not xcodeproj_path:
            print("❌ Could not find .xcodeproj directory")
            return
            
        # 查找 project.pbxproj 文件
        pbxproj_path = os.path.join(xcodeproj_path, "project.pbxproj")
        if not os.path.exists(pbxproj_path):
            print(f"❌ Could not find project.pbxproj at {pbxproj_path}")
            return
            
        print(f"📂 Found project file: {pbxproj_path}")
        
        # 加载项目文件
        project = XcodeProject.load(pbxproj_path)
        
        main_group = project.get_groups_by_name(sdk_proj_path + 'SDK')
        # 创建 Swift 代码组
        sub_group = project.get_or_create_group(swift_dir_name, parent=main_group[0])
        
        # 添加生成的文件到项目
        for file_name in generated_files:
            try:
                relative_path = os.path.join(swift_dir_name, file_name)
                
                # 添加文件到项目
                file_options = FileOptions(weak=False)
                project.add_file(
                    relative_path,
                    parent=sub_group,
                    tree='<group>',
                    target_name=sdk_proj_path + 'SDK',
                    force=True,
                    file_options=file_options
                )
                print(f"✅ Added Swift file: {file_name}")
                
            except Exception as e:
                print(f"❌ Error adding Swift file {file_name}: {str(e)}")
                continue
        
        # 保存项目文件
        try:
            project.save()
            print(f"✅ Saved project file successfully")
        except Exception as e:
            print(f"❌ Error saving project file: {str(e)}")
            
        print(f"✅ Added {num_files} Swift files successfully!")
        
    except Exception as e:
        print(f"❌ Error in add_swift_trash_code: {str(e)}")


def add_oc_trash_code():
    """添加 OC 垃圾代码"""
    try:
        generator = CodeGenerator()
        
        # 随机选择一个模块名称
        module_names = [
            "Core", "Foundation", "Network", "Storage", "Security",
            "Analytics", "Common", "Utils", "Service", "Manager"
        ]
        folder_name = random.choice(module_names)
        target_path = os.path.join("../Mock", sdk_proj_path, sdk_proj_path + 'SDK', "MutiClasses", folder_name)
        os.makedirs(target_path, exist_ok=True)
        
        # 生成组件
        generated_names = generator.generate_multiple_components(target_path, 10)
        
        # 查找 .xcodeproj 目录
        mock_path = os.path.join("../Mock", sdk_proj_path)
        xcodeproj_path = find_xcodeproj(mock_path)
        
        if not xcodeproj_path:
            print("❌ Could not find .xcodeproj directory")
            return
            
        # 查找 project.pbxproj 文件
        pbxproj_path = os.path.join(xcodeproj_path, "project.pbxproj")
        if not os.path.exists(pbxproj_path):
            print(f"❌ Could not find project.pbxproj at {pbxproj_path}")
            return
            
        print(f"📂 Found project file: {pbxproj_path}")
        
        # 加载项目文件
        project = XcodeProject.load(pbxproj_path)
        
        # 获取主 SDK 组
        main_group = project.get_groups_by_name(sdk_proj_path + 'SDK')[0]
        
        # 获取或创建 MutiClasses 组
        classes_group = project.get_or_create_group('MutiClasses', parent=main_group)
        
        # 创建模块组
        module_group = project.get_or_create_group(folder_name, parent=classes_group)
        
        # 为每个生成的组件创建子组并添加文件
        for component_name in generated_names:
            # 创建组件子组
            component_group = project.get_or_create_group(component_name, parent=module_group)
            
            # 定义需要添加的文件
            files = [
                f"{component_name}Protocol.h",
                f"{component_name}Model.h",
                f"{component_name}Model.m",
                f"{component_name}View.h",
                f"{component_name}View.m",
                f"{component_name}View+{component_name}.h",
                f"{component_name}View+{component_name}.m"
            ]
            
            # 添加文件到项目
            for file_name in files:
                try:
                    # 构建相对路径 (相对于项目根目录)
                    rel_path = os.path.join("MutiClasses", folder_name, component_name, file_name)
                    
                    # 添加文件到项目
                    file_options = FileOptions(weak=False)
                    project.add_file(
                        rel_path,
                        parent=component_group,
                        tree='<group>',
                        target_name=sdk_proj_path + 'SDK',
                        force=False,
                        file_options=file_options
                    )
                    print(f"✅ Added {file_name} to project")
                    
                except Exception as e:
                    print(f"❌ Error adding {file_name}: {str(e)}")
                    continue
        
        # 保存项目文件
        try:
            project.save()
            print("✅ Saved project file successfully")
        except Exception as e:
            print(f"❌ Error saving project file: {str(e)}")
            
        print(f"✅ Added {len(generated_names)} components to project")
        
    except Exception as e:
        print(f'❌ Error in add_oc_trash_code: {str(e)}')
        traceback.print_exc()

class CodeGenerator:
    def __init__(self):
        self.protocol_gen = ProtocolGenerator()
        self.extension_gen = ExtensionGenerator()
        self.model_gen = ModelGenerator()
        
        self.prefixes = [
            "CS", "NS", "MS", "FB", "GL", "AM", "AP", "GC", "TC", "YX", "QT",
            "Core", "Swift", "Cloud", "Smart", "Fast", "Easy", "Pro", "SDK"
        ]
        
        self.suffixes = [
            "Manager", "Controller", "Handler", "Service", "Provider", "Helper",
            "Utils", "Factory", "Builder", "Processor", "Engine", "Center"
        ]

    def generate_component_name(self) -> str:
        """生成组件名称"""
        prefix = random.choice(self.prefixes)
        suffix = random.choice(self.suffixes)
        return f"{prefix}{suffix}"

    def generate_multiple_components(self, target_path: str, count: int) -> list:
        """批量生成多个组件"""
        generated_names = []
        
        print(f"\n🚀 Starting to generate {count} components...")
        
        for i in range(count):
            while True:
                component_name = self.generate_component_name()
                if component_name not in generated_names:
                    break
            
            print(f"\n📦 Generating component {i+1}/{count}: {component_name}")
            
            try:
                # 创建组件目录
                component_path = os.path.join(target_path, component_name)
                os.makedirs(component_path, exist_ok=True)
                
                # 生成协议文件
                protocol_content = self.protocol_gen.generate_protocol(component_name)
                protocol_methods = self.protocol_gen.get_protocol_methods(component_name)
                
                with open(os.path.join(component_path, f"{component_name}Protocol.h"), 'w') as f:
                    f.write(protocol_content)
                
                # 生成模型文件
                model_header = self.model_gen.generate_model_header(f"{component_name}Model")
                with open(os.path.join(component_path, f"{component_name}Model.h"), 'w') as f:
                    f.write(model_header)
                
                model_implementation = self.model_gen.generate_model_implementation(f"{component_name}Model")
                with open(os.path.join(component_path, f"{component_name}Model.m"), 'w') as f:
                    f.write(model_implementation)
                
                # 生成视图文件
                view_header = self.extension_gen.generate_view_header(component_name)
                with open(os.path.join(component_path, f"{component_name}View.h"), 'w') as f:
                    f.write(view_header)
                
                view_implementation = self.extension_gen.generate_view_implementation(component_name)
                with open(os.path.join(component_path, f"{component_name}View.m"), 'w') as f:
                    f.write(view_implementation)
                
                # 生成扩展文件
                extension_header = self.extension_gen.generate_extension_header(f"{component_name}View", component_name)
                with open(os.path.join(component_path, f"{component_name}View+{component_name}.h"), 'w') as f:
                    f.write(extension_header)
                
                extension_implementation = self.extension_gen.generate_extension_implementation(
                    f"{component_name}View", 
                    component_name,
                    protocol_methods
                )
                with open(os.path.join(component_path, f"{component_name}View+{component_name}.m"), 'w') as f:
                    f.write(extension_implementation)
                
                generated_names.append(component_name)
                print(f"✅ Generated component: {component_name}")
                
            except Exception as e:
                print(f"❌ Error generating component {component_name}: {str(e)}")
                continue
                
        print(f"\n✨ Successfully generated {len(generated_names)} components")
        return generated_names



# zip压缩，输入文件夹和输出文件夹
def zip_folder(folder_path, output_path):
    with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                zipf.write(file_path, os.path.relpath(file_path, folder_path))




# 还原
def restore_proj():
    restore_trash_file_target_path = "../Mock/%s/%sSDK/ReFiles" % (sdk_proj_path, sdk_proj_path)
    if os.path.exists(restore_trash_file_path):
        # 还原垃圾代码到工程目录中
        shutil.copytree(restore_trash_file_path, restore_trash_file_target_path)
        # 还原bundle中的配置
        Test.modify_bundle_files(sdk_proj_path, restore_images_name_path, mix_or_restore_tag ,new_obfuse_or_not)
    # 混淆代码
    obfuseOrRestore(1)
    # 加swift垃圾代码
    add_swift_trach_code()
    # 加oc垃圾代码
    add_oc_trash_code()
    # 还原SDK名称、文件名称、bundle名称
    modify_sdk.modify_current_projname(sdk_proj_path, restore_proj_name)
    
    if new_obfuse_or_not == '1':
        # 拷贝、移除旧的配置文件并压缩成zip包
        if os.path.exists('../config'):
            print('移除旧的配置路径')
            os.system('rm -rf ../config')
        # 删除旧的
        if os.path.exists('../config.zip'):
            print('移除旧的config.zip')
            os.system('rm -rf ../config.zip')
        os.system('mkdir ../config')
        os.system('cp -R ../Mock/%s/%sSDK/ReFiles ../config' % (new_sdk_name, new_sdk_name))
        os.system('cp -R ../Mock/%s/%sSDK/__image_config.json ../config' % (new_sdk_name, new_sdk_name))
        os.system('cp -R ../Mock/%s/%sSDK/__obfuscator.json ../config' % (new_sdk_name, new_sdk_name))
        os.system('cp -R ../Mock/%s/%sSDK/__trash_config.json ../config' % (new_sdk_name, new_sdk_name))
        os.system('cp -R ../Mock/%s/%sSDK/__add_code_trash_config.json ../config' % (new_sdk_name, new_sdk_name))
        zip_folder('../config', '../config.zip')

# 获取混淆配置参数表
def load_config_params(task_key):
    #测试
    # task_key = "344a8967e1b19906068d8da908d4a562"
    #测试
    # url = "http://**************:9095/api/obconfig/sdkconfig?key=%s" % task_key
    # print('LIYC:开始加载混淆配制：%s' % url)
    # response = urllib.request.urlopen(url)
    # config_data = response.read().decode('utf-8')
    # tmp_config_params_dic = json.loads(config_data)
    tmp_config_params_dic = {"id":2,
                             "oc_class_number_min":5,
                             "oc_class_number_max":10,
                             "oc_class_head_upper":1,
                             "oc_class_name_ab":3,
                             "oc_func_number_min":8,
                             "oc_func_number_max":15,
                             "oc_func_name_ab":3,
                             "oc_func_content_min":5,
                             "oc_func_content_max":15,
                             "oc_func_map_arr_content_min":3,
                             "oc_func_map_arr_content_max":6,
                             "oc_attr_number_min":2,
                             "oc_attr_number_max":10,
                             "oc_attr_name_ab":1,
                             "oc_func_content_line_rame":10,
                             "oc_trash_use_rame":15,
                             "oc_init_attr_in_ori_file":0,
                             "oc_init_func_in_ori_file":0,
                             "oc_use_no_param_fun":1,
                             "oc_const_number_min":10,
                             "oc_const_number_max":30,
                             'oc_append_trash_code_to_code':0,
                             "sw_class_number_min":200,
                             "sw_class_number_max":500,
                             "sw_class_head_upper":1,
                             "sw_class_name_ab":1,
                             "sw_func_number_min":30,
                             "sw_func_number_max":60,
                             "sw_func_name_ab":1,
                             "sw_func_map_arr_content_min":5,
                             "sw_func_map_arr_content_max":15,
                             "sw_attr_number_min":15,
                             "sw_attr_number_max":30,
                             "sw_attr_name_ab":1,
                             "sw_func_content_line_rame":30,
                             "sw_is_oc_use":1,
                             "sw_is_cls_vc_dex":1}
    return tmp_config_params_dic


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Manual to this python script')
    parser.add_argument('-mode', default=1, type=int)
    parser.add_argument('-name', type=str, default='', help='proj_name')
    parser.add_argument('-main', type=str, default='', help='main_dir_name')
    parser.add_argument('-version', type=str, default='', help='sdk_version')
    parser.add_argument('-program', type=str, default='', help='job_name')
    parser.add_argument('-division', type=str, default='', help='division')
    parser.add_argument('-json', type=str, default='', help='json')
    parser.add_argument('-add', type=str, default='', help='json')
    parser.add_argument('-taskkey', type=str, default='', help='taskkey')
    args = parser.parse_args()
    
    mix_or_restore_tag = args.mode
    new_sdk_name = args.name
    restore_proj_name = args.name
    sdk_proj_path = args.main
    sdk_version = args.version
    job_name = args.program
    business_division = args.division
    json_config_param = args.json
    task_key = args.taskkey
    
    
    
    # 获取混淆配置表
    config_params_dic = load_config_params(task_key)
    
    
    # 拷贝、移除旧的
    print("当前路径：%s", os.getcwd())
    if os.path.exists('../Mock'):
        print('移除旧的')
        os.system('rm -rf ../Mock')
    os.system('mkdir ../Mock')
    dist_path = "../Mock/%s" % sdk_proj_path
    target_path = "../%s" % sdk_proj_path
    shutil.copytree(target_path, dist_path)
    
    print('新的SDK名称:%s' % new_sdk_name)
    print('混淆或还原:%s' % mix_or_restore_tag)
    print('混淆工程的主目录：%s' % sdk_proj_path)
    
    if job_name == "iOS_vGN" or job_name == "iOS_vTD" or job_name == "iOS_vST":
        # # 加密json文件并替换掉工程中的output_dir
        AESKey, IVKey, Config_dat_path, output_folder = encrypt_json_file()
        # 替换配置参数
        replace_proj_config(AESKey, IVKey, Config_dat_path, output_folder)
    
    if json_config_param != 'None':
        json_dic = json.loads(json_config_param)
        new_obfuse_or_not = json_dic.get("ObAgain")
    
    if mix_or_restore_tag == 0:
        mix_proj()
    else:
        restore_proj()
        
    # 将类拆分成类别
    if job_name == "iOS_vTD":
        ca_dist_path = "../Mock/%s" % new_sdk_name
        category.do_category_path(ca_dist_path)
    
    # 删除Pods
    os.system('rm -rf ../Mock/%s/Pods' % new_sdk_name)
    os.system('rm -rf ../Mock/%s/Podfile.lock' % new_sdk_name)
    original_podfile = '../%s/Podfile' % sdk_proj_path
    new_podfile = '../Mock/%s/Podfile' % new_sdk_name
    if not os.path.exists(new_podfile):
        os.system('cp %s %s' % (original_podfile, new_podfile))
        with open(original_podfile, 'rb') as rstream:
                    continuer = rstream.read()
                    with open(new_podfile, 'wb') as wstream:
                        continuer = continuer.replace(sdk_proj_path.encode("utf8"),new_sdk_name.encode("utf8"))
                        wstream.write(continuer)
        os.chdir("../Mock/%s" % new_sdk_name)
        os.system("pod install")
        os.chdir("../../publish")
        print("当前路径：%s", os.getcwd())
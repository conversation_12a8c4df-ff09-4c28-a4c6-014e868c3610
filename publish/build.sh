#!/bin/bash
type=$1
modify_sdk_name=$2
main_dir_name=$3
key=$4
sdk_version=$5
OB_CONFIG_ZIP=$6
JOB_Name=$7
BUSINESS_DIVISION=$8
SDK_RES_PATH=$9
BRANCH_NAME=${10}
DECODE_JSON=${11}

echo "build type:${type}"
echo "build SDK_TARGET_NAME:${modify_sdk_name}"
echo "build PROJECT_NAME:${main_dir_name}"
echo "build key:${key}"
echo "build VERSION_NAME:${sdk_version}"
echo "build config path:${OB_CONFIG_ZIP}"
echo "build JOB_NAME:${JOB_NAME}"
echo "build BUSINESS_DIVISION:${BUSINESS_DIVISION}"
echo "build SDK_RES_PATH:${SDK_RES_PATH}"
echo "build BRANCH_NAME:${BRANCH_NAME}"
echo "build DECODE_JSON:${DECODE_JSON}"



config_zip="config.zip"
sdk_zip="sdk_proj.zip"
bundle_zip="bundle.zip"
TEMP_DIR=""
CONFIG_TEMP_DIR=""
run(){
    if [ "$type" == "1" ]; then
        if [ -d "./config.zip" ]; then
            rm -R ./config.zip
        fi
        echo "开始还原SDK!"
        wget -O config.zip ${OB_CONFIG_ZIP}
        UNZIP_CONFIG_DIR="../config"
        unzip -d "${UNZIP_CONFIG_DIR}" -o config.zip
        rm -R ./config.zip
    fi


    if [ "$JOB_Name" == 'iOS_vTD' ];then
        echo "开始组装模板工程"
        python3 CreateOversea.py -i ${BRANCH_NAME} -j ${JOB_Name}
        echo "组装模板工程完成"
    fi 

    if [ "$JOB_Name" == 'iOS_vGN' ];then
        echo "开始组装国内组件模板工程"
        python3 main.py -i ${BRANCH_NAME} -j ${JOB_Name}
        echo "组装国内组件模板工程完成"
    fi

    # 替换bundle中的资源
    echo 'check bundle res.....'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  
    if [ "$SDK_RES_PATH" = "nores" ]
    then
        echo "前置--SDK_RES_PATH 为空不替换资源"
    else
        echo "前置--发现要替换的资源包，准备替换资源"
        echo ${SDK_RES_PATH}
        wget -O bundle_res.zip ${SDK_RES_PATH}
        PROJECT_PATH="../${main_dir_name}"
        BUNDLE_TARGET_NAME="${main_dir_name}SDKBundle"
        
        UNZIP_BUNDLE_DIR="${PROJECT_PATH}/${BUNDLE_TARGET_NAME}"
        echo ${UNZIP_BUNDLE_DIR}
        ls -l ${UNZIP_BUNDLE_DIR}
        if [ -d ${UNZIP_BUNDLE_DIR} ];then
            echo "删除旧资源"
            rm -rf ${UNZIP_BUNDLE_DIR}/*
        else
            echo "创建新资源"
        fi
        unzip -d "${UNZIP_BUNDLE_DIR}" -o bundle_res.zip
        ls -l ${UNZIP_BUNDLE_DIR}
        echo "打印目标目录结构完成"
        echo "资源模板替换成功"
    fi

    python3 run.py -mode ${type} -name ${modify_sdk_name} -main ${main_dir_name} -version ${sdk_version} -program ${JOB_Name} -division ${BUSINESS_DIVISION} -json ${DECODE_JSON} -taskkey ${key}
}


upload_sdk_zip(){
    zip -r ${sdk_zip} ./Mock
    ftp -i -v -n **************<<EOF
    user ios_pkg 123456
    binary
    cd /data/gonggong.tools.com/public/upload/ios_pkg
    mkdir ${TEMP_DIR}
    cd ${TEMP_DIR}
    prompt off
    put ${sdk_zip}
    by
EOF
}



upload_config_zip(){
    ftp -i -v -n **************<<EOF
    user ios_pkg 123456
    binary
    cd /data/gonggong.tools.com/public/upload/ios_pkg
    mkdir ${CONFIG_TEMP_DIR}
    cd ${CONFIG_TEMP_DIR}
    prompt off
    put ${config_zip}
    by
EOF
}



save_ob(){
    if [ -f $config_zip ]; then
        # 如果有新的config.zip则顶掉旧的 
        target_zip_path="${CONFIG_TEMP_DIR}/${config_zip}"
        g_url="http://**************:9095/api/ios/obveradd?key=${key}&config_zip=${target_zip_path}"
        curl -vv ${g_url}
        echo "执行save_ob_info 完成"
        echo "删除本地压缩包"
        rm -rf "${config_zip}"
    fi
}


#混淆|还原
run
cd ..

TEMP_DIR="${modify_sdk_name}.${sdk_version}.99.restore"
if [ -f $config_zip ]; then
    # 如果有新的config.zip 则顶替掉旧的
    echo "顶替config.zip链接地址:${modify_sdk_name}.${key}.obfuse/config.zip"
    # DTQOozingsSdk.96d65c81afa0be10d6b020ec88a0c734.obfuse/config.zip
    CONFIG_TEMP_DIR="${modify_sdk_name}.${key}.obfuse"
fi


# if [ "$type" == "0" ]; then
#     echo "上传混淆配置文件已经备份混淆SDK工程"
#     # date_value=$(date "+%Y.%m.%d_%H:%M:%S")
#     TEMP_DIR="${modify_sdk_name}.${sdk_version}.${key}.obfuse"
#     CONFIG_TEMP_DIR="${modify_sdk_name}.${sdk_version}.${key}.obfuse"
# else
#     # date_value=$(date "+%Y.%m.%d_%H:%M:%S")
#     echo "还原SDK以及备份还原后的SDK工程"
#     TEMP_DIR="${modify_sdk_name}.${sdk_version}.99.restore"
#     if [ -f $config_zip ]; then
#         # 如果有新的config.zip 则顶替掉旧的
#         echo "顶替config.zip链接地址:${modify_sdk_name}.${key}.obfuse/config.zip"
#         CONFIG_TEMP_DIR="${modify_sdk_name}.${key}.obfuse"
#     fi
# fi


#上传sdkzip
upload_sdk_zip
#上传config.zip
if [ -f $config_zip ]; then
    upload_config_zip
fi
#保存zip
save_ob

# 删除原始工程
if [ -d "${main_dir_name}" ]; then
    rm -rf "${main_dir_name}"
fi

if [ -d "./config" ]; then
    rm -R ./config
fi

# 复制混淆后的工程到主目录
if [ -d "./Mock" ]; then
    cp -r ./Mock/${modify_sdk_name}/ ./
    # rm -r ./Mock
fi

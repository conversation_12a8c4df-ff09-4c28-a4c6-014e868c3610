#!/usr/bin/env python
# _*_ coding:utf-8 _*_


import random
import os,sys
import CSGString

#属性类型
classArray = ['UILabel','UITableView','UISlider','UIScrollView','UIView','UIButton','UITextField','UITextView','UIImageView','UISwitch','UISegmentedControl','UIPageControl']
#创建.swift文件

def createSwift(fileNmae, propertyNumber, methodArray, dir_name, array, sw_config_params_dic):
    dir_path = sys.path[0] + '/%s/' % dir_name
    if not os.path.exists(dir_path):
        os.system('mkdir %s' % dir_name)


    full_path =  sys.path[0] + '/%s/' % dir_name + fileNmae + '.swift' 

    file = open(full_path, 'w')

    file.write('//\n//  '+fileNmae+'.swift\n//  LinkMall\n\n//  Created by <PERSON><PERSON> on 2018/9/17.\n//  Copyright © 2018年 OneThing Ltd. All rights reserved.\n//\n\n')

    file.write('import UIKit \n\n' + 'class '+fileNmae+': UIViewController {\n\n')
    
    
    sw_attr_name_ab = sw_config_params_dic.get("sw_attr_name_ab")
    propryNameArray = []

    for index in range(1,propertyNumber):

        tmp_attr_name = ""
        if sw_attr_name_ab:
            word_A = CSGString.getRandomStr()
            word_B = CSGString.getRandomStr()
            tmp_attr_name = word_A + word_B + 'Object'
        else:
            word_A = CSGString.getRandomStr()
            tmp_attr_name = word_A + "Object"
        propryNameArray.append(tmp_attr_name)

    propryNameArray = list(set(propryNameArray))

    for propertyName in propryNameArray:
        class_name = random.choice(classArray)
        item_name = CSGString.getRandomStr()
        first_line = "private lazy var %s:%s = {\n\t" % (propertyName, class_name)
        append_lin1 = "let %s = %s()\n\t" % (item_name, class_name)
        color_list = [".red",'.blue', '.green', '.yellow', '.gray', '.orange', '.purple', '.brown', '.clear']
        color_item = random.choice(color_list)
        append_line2 = "%s.backgroundColor = %s\n\t" % (item_name, color_item)
        append_lin3 = "%s.layer.cornerRadius = 5\n\t" % item_name
        append_lin4 = "%s.clipsToBounds = true\n\t" % item_name
        append_lin5 = "%s.alpha = 1\n\t" % item_name
        last_line = "return %s\n}()\n\n" % item_name
        file.write(first_line)
        file.write(append_lin1)
        file.write(append_line2)
        file.write(append_lin3)
        file.write(append_lin4)
        file.write(append_lin5)
        file.write(last_line)

    file.write('\n\n')
    
    file.write('    override func viewDidLoad() {\n        super.viewDidLoad()\n    }\n\n')
   
    generate_methods(file, methodArray, sw_config_params_dic, array)
    # for methodName in methodArray:

    #     file.write('    public func '+methodName+'() {\n\n       var realArr = Array<String>()\n')

    #     sw_func_map_arr_content_min = sw_config_params_dic.get("sw_func_map_arr_content_min")
    #     sw_func_map_arr_content_max = sw_config_params_dic.get("sw_func_map_arr_content_max")
    #     number = random.randint(sw_func_map_arr_content_min, sw_func_map_arr_content_max)

    #     for i in range(1,number):

    #         file.write('       realArr.append("'+random.choice(array)+'")\n')

    #     file.write('\n    }\n\n')

    file.write('}')

    file.close()



def generate_methods(file, methodArray, sw_config_params_dic, array):
    types = ['array', 'dictionary', 'set']
    
    for methodName in methodArray:
        file.write(f'    public func {methodName}() {{\n\n')

        # 随机选择生成的类型
        chosen_type = random.choice(types)

        if chosen_type == 'array':
            file.write('       var realArr = Array<String>()\n')
            sw_func_map_arr_content_min = sw_config_params_dic.get("sw_func_map_arr_content_min")
            sw_func_map_arr_content_max = sw_config_params_dic.get("sw_func_map_arr_content_max")
            number = random.randint(sw_func_map_arr_content_min, sw_func_map_arr_content_max)
            for i in range(number):
                file.write(f'       realArr.append("{random.choice(array)}")\n')
            file.write('\n')

        elif chosen_type == 'dictionary':
            file.write('       var realDict = [String: String]()\n')
            sw_func_map_arr_content_min = sw_config_params_dic.get("sw_func_map_arr_content_min")
            sw_func_map_arr_content_max = sw_config_params_dic.get("sw_func_map_arr_content_max")
            number = random.randint(sw_func_map_arr_content_min, sw_func_map_arr_content_max)
            for i in range(number):
                key = random.choice(array)
                value = random.choice(array)
                file.write(f'       realDict["{key}"] = "{value}"\n')
            file.write('\n')

        elif chosen_type == 'set':
            file.write('       var realSet = Set<String>()\n')
            sw_func_map_arr_content_min = sw_config_params_dic.get("sw_func_map_arr_content_min")
            sw_func_map_arr_content_max = sw_config_params_dic.get("sw_func_map_arr_content_max")
            number = random.randint(sw_func_map_arr_content_min, sw_func_map_arr_content_max)
            for i in range(number):
                file.write(f'       realSet.insert("{random.choice(array)}")\n')
            file.write('\n')

        file.write('    }\n\n')


def createClassName(sw_config_params_dic):
    array = []

    # 设置生成多少个类
    sw_class_number_min = sw_config_params_dic.get("sw_class_number_min")
    sw_class_number_max = sw_config_params_dic.get("sw_class_number_max")
    classNumber = random.randint(sw_class_number_min, sw_class_number_max)
    class_pre_header = CSGString.generate_random_str()
    for i in range(classNumber):

        final = ""
        #字符串长度
        
        word_A = CSGString.getRandomStr()
        word_B = CSGString.getRandomStr()
        sw_class_name_ab = sw_config_params_dic.get("sw_class_name_ab")
        sw_is_cls_vc_dex = sw_config_params_dic.get("sw_is_cls_vc_dex")
        if sw_class_name_ab:
            final = word_A + word_B
        else:
            final = word_A
        
        if sw_is_cls_vc_dex:
            final = final + "ViewController"
        
        final_str = final[0].lower() + final[1:]
        all_final_str = class_pre_header + final_str
        array.append(all_final_str)
    return array

def run(dir_name, config_params_dic):
    sw_config_params_dic = config_params_dic
    array = createClassName(sw_config_params_dic)

    array = list(set(array))

    for name in array:

        sw_attr_number_min = sw_config_params_dic.get("sw_attr_number_min")
        sw_attr_number_max = sw_config_params_dic.get("sw_attr_number_max")
        
        sw_func_number_min = sw_config_params_dic.get("sw_func_number_min")
        sw_func_number_max = sw_config_params_dic.get("sw_func_number_max")
        number = random.randint(sw_attr_number_min, sw_attr_number_max)

        methodArray = []

        sw_func_name_ab = sw_config_params_dic.get("sw_func_name_ab")
        for i in range(sw_func_number_min, sw_func_number_max):
            tmp_func_name = ""
            if sw_func_name_ab:
                word_A = CSGString.getRandomStr()
                word_B = CSGString.getRandomStr()
                tmp_func_name = word_A + word_B + 'Func'
            else:
                word_A = CSGString.getRandomStr()
                tmp_func_name = word_A + 'Func'
            methodArray.append(tmp_func_name)

        methodArray = list(set(methodArray))#数组去重
        
        createSwift(name, number, methodArray, dir_name ,array, sw_config_params_dic)
        
    print('Done')




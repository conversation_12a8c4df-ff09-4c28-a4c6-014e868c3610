import random
from typing import List, Dict

class ModelGenerator:
    def __init__(self):
        self.basic_types = [
            ('NSString *', 'copy'),
            ('NSInteger', 'assign'),
            ('BOOL', 'assign'),
            ('CGFloat', 'assign'),
            ('NSTimeInterval', 'assign'),
            ('NSUInteger', 'assign'),
            ('double', 'assign'),
            ('float', 'assign'),
            ('int', 'assign')
        ]
        
        self.collection_types = [
            ('NSArray *', 'copy'),
            ('NSDictionary *', 'copy'),
            ('NSSet *', 'copy'),
            ('NSMutableArray *', 'strong'),
            ('NSMutableDictionary *', 'strong'),
            ('NSMutableSet *', 'strong')
        ]
        
        self.custom_types = [
            ('UIImage *', 'strong'),
            ('NSData *', 'strong'),
            ('NSDate *', 'strong'),
            ('NSURL *', 'copy'),
            ('NSNumber *', 'copy'),
            ('NSAttributedString *', 'copy')
        ]
        
        self.property_names = {
            'basic': [
                'name', 'title', 'identifier', 'type', 'status', 'version',
                'index', 'count', 'length', 'size', 'offset', 'limit',
                'isEnabled', 'isSelected', 'isVisible', 'isValid', 'isLocked',
                'timestamp', 'duration', 'interval', 'timeout', 'delay',
                'progress', 'ratio', 'scale', 'alpha', 'width', 'height',
                'priority', 'level', 'score', 'rating', 'amount', 'price'
            ],
            'collection': [
                'items', 'data', 'list', 'array', 'dictionary', 'map',
                'parameters', 'options', 'attributes', 'properties',
                'settings', 'config', 'cache', 'buffer', 'stack', 'queue'
            ],
            'custom': [
                'image', 'icon', 'avatar', 'logo', 'banner',
                'date', 'time', 'deadline', 'schedule',
                'url', 'link', 'path', 'route',
                'value', 'result', 'output', 'response'
            ]
        }

    def generate_random_properties(self, count: int = 5) -> List[tuple]:
        """生成随机属性列表"""
        properties = []
        used_names = set()
        
        # 生成基本属性，确保类型末尾有空格
        basic_properties = [
            ('NSString *', 'identifier', 'copy'),
            ('NSTimeInterval ', 'timestamp', 'assign'),
            ('BOOL ', 'isEnabled', 'assign'),
            ('CGFloat ', 'progress', 'assign')
        ]
        
        # 添加基本属性
        for prop_type, prop_name, prop_attr in basic_properties:
            if len(properties) < count:
                properties.append((prop_type, prop_name, prop_attr))
                used_names.add(prop_name)
        
        # 生成剩余的属性
        remaining_count = count - len(properties)
        for _ in range(remaining_count):
            # 随机选择属性类型类别
            type_category = random.choice(['basic', 'collection', 'custom'])
            
            if type_category == 'basic':
                type_tuple = random.choice(self.basic_types)
                name_pool = self.property_names['basic']
            elif type_category == 'collection':
                type_tuple = random.choice(self.collection_types)
                name_pool = self.property_names['collection']
            else:
                type_tuple = random.choice(self.custom_types)
                name_pool = self.property_names['custom']
            
            # 选择未使用的属性名
            available_names = [n for n in name_pool if n not in used_names]
            if not available_names:
                continue
                
            prop_name = random.choice(available_names)
            used_names.add(prop_name)
            
            # 确保类型末尾有空格
            prop_type = type_tuple[0]
            if not prop_type.endswith(' '):
                prop_type += ' '
            
            properties.append((prop_type, prop_name, type_tuple[1]))
        
        return properties

    def generate_model_header(self, class_name: str) -> str:
        """生成模型头文件"""
        self.current_properties = self.generate_random_properties()
        
        header = f"#import <Foundation/Foundation.h>\n"
        header += f"#import <UIKit/UIKit.h>\n\n"
        header += f"NS_ASSUME_NONNULL_BEGIN\n\n"
        header += f"@interface {class_name} : NSObject\n\n"
        
        # 添加属性声明，确保类型和名称之间有空格
        header += "// Properties\n"
        for prop_type, prop_name, prop_attr in self.current_properties:
            # 确保类型末尾有空格
            if not prop_type.endswith(' '):
                prop_type += ' '
            
            # 判断是否为基本数据类型（不需要 nullable）
            is_primitive = any(t in prop_type for t in [
                'NSInteger ', 'BOOL ', 'CGFloat ', 'double ', 'float ', 
                'int ', 'NSTimeInterval ', 'NSUInteger ', 'long ', 'short '
            ])
            
            if is_primitive:
                header += f"@property (nonatomic, {prop_attr}) {prop_type}{prop_name};\n"
            else:
                header += f"@property (nonatomic, {prop_attr}, nullable) {prop_type}{prop_name};\n"
        
        header += "\n"
        
        # 生成初始化方法声明
        header += "// Initialization\n"
        header += "- (instancetype)initWithDictionary:(NSDictionary *)dictionary;\n"
        header += "+ (instancetype)modelWithDictionary:(NSDictionary *)dictionary;\n\n"
        
        # 生成转换方法声明
        header += "// Conversion\n"
        header += "- (NSDictionary *)toDictionary;\n\n"
        
        header += "@end\n\n"
        header += "NS_ASSUME_NONNULL_END"
        return header

    def generate_model_implementation(self, class_name: str) -> str:
        """生成模型实现文件"""
        # 使用之前生成的属性列表
        if not hasattr(self, 'current_properties'):
            self.current_properties = self.generate_random_properties()
        
        impl = f'#import "{class_name}.h"\n\n'
        impl += f'@implementation {class_name}\n\n'
        
        # 生成初始化方法实现
        impl += "// Initialization Methods\n"
        impl += "- (instancetype)initWithDictionary:(NSDictionary *)dict {\n"
        impl += "    self = [super init];\n"
        impl += "    if (self) {\n"
        
        # 添加属性初始化，只使用已声明的属性
        for prop_type, prop_name, _ in self.current_properties:
            if 'NSString' in prop_type:
                impl += f"        _{prop_name} = [dict[@\"{prop_name}\"] copy];\n"
            elif 'NSInteger' in prop_type:
                impl += f"        _{prop_name} = [dict[@\"{prop_name}\"] integerValue];\n"
            elif 'int' in prop_type:
                impl += f"        _{prop_name} = [dict[@\"{prop_name}\"] intValue];\n"
            elif 'BOOL' in prop_type:
                impl += f"        _{prop_name} = [dict[@\"{prop_name}\"] boolValue];\n"
            elif 'CGFloat' in prop_type or 'double' in prop_type or 'float' in prop_type:
                impl += f"        _{prop_name} = [dict[@\"{prop_name}\"] doubleValue];\n"
            elif 'NSTimeInterval' in prop_type:
                impl += f"        _{prop_name} = [dict[@\"{prop_name}\"] doubleValue];\n"
            elif 'NSArray' in prop_type or 'NSMutableArray' in prop_type:
                impl += f"        _{prop_name} = dict[@\"{prop_name}\"];\n"
            elif 'NSDictionary' in prop_type or 'NSMutableDictionary' in prop_type:
                impl += f"        _{prop_name} = dict[@\"{prop_name}\"];\n"
            else:
                impl += f"        _{prop_name} = dict[@\"{prop_name}\"];\n"
                
        impl += "    }\n"
        impl += "    return self;\n"
        impl += "}\n\n"
        
        impl += "+ (instancetype)modelWithDictionary:(NSDictionary *)dict {\n"
        impl += "    return [[self alloc] initWithDictionary:dict];\n"
        impl += "}\n\n"
        
        # 生成转换方法实现
        impl += "// Conversion Methods\n"
        impl += "- (NSDictionary *)toDictionary {\n"
        impl += "    NSMutableDictionary *dict = [NSMutableDictionary dictionary];\n"
        
        # 根据属性类型添加适当的转换
        for prop_type, prop_name, _ in self.current_properties:
            if 'NSString' in prop_type or 'NSArray' in prop_type or 'NSDictionary' in prop_type or 'NSSet' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            elif 'NSInteger' in prop_type or 'NSUInteger' in prop_type:
                impl += f"    dict[@\"{prop_name}\"] = @(self.{prop_name});\n"
            elif 'BOOL' in prop_type:
                impl += f"    dict[@\"{prop_name}\"] = @(self.{prop_name});\n"
            elif 'CGFloat' in prop_type or 'double' in prop_type or 'float' in prop_type:
                impl += f"    dict[@\"{prop_name}\"] = @(self.{prop_name});\n"
            elif 'NSTimeInterval' in prop_type:
                impl += f"    dict[@\"{prop_name}\"] = @(self.{prop_name});\n"
            elif 'int' in prop_type:
                impl += f"    dict[@\"{prop_name}\"] = @(self.{prop_name});\n"
            elif 'UIImage' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            elif 'NSData' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            elif 'NSDate' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            elif 'NSURL' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            elif 'NSNumber' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            elif 'NSAttributedString' in prop_type:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            else:
                impl += f"    if (self.{prop_name}) dict[@\"{prop_name}\"] = self.{prop_name};\n"
            
        impl += "    return dict;\n"
        impl += "}\n\n"
        
        # 生成描述方法
        impl += "- (NSString *)description {\n"
        impl += "    return [NSString stringWithFormat:@\"<%@: %p, %@>\",\n"
        impl += "            NSStringFromClass([self class]),\n"
        impl += "            self,\n"
        impl += "            [self toDictionary]];\n"
        impl += "}\n\n"
        
        impl += "@end"
        return impl 
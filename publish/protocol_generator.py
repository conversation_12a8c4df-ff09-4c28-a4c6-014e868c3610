import random
import string
from typing import List, Dict

class ProtocolGenerator:
    def __init__(self):
        self.method_templates = {
            'data_handling': [
                '- (void)process{Name}Data:(NSData *)data completion:(void(^)(BOOL success))completion;',
                '- (void)fetch{Name}WithParams:(NSDictionary *)params success:(void(^)(id result))success failure:(void(^)(NSError *error))failure;',
                '- (void)update{Name}Info:(id)info progress:(void(^)(float progress))progress;'
            ],
            'ui_related': [
                '- (void)configure{Name}View:(UIView *)view;',
                '- (void)update{Name}Layout:(CGRect)frame animated:(BOOL)animated;',
                '- (void)handle{Name}Action:(id)sender;'
            ],
            'lifecycle': [
                '- (void)did{Name}Setup;',
                '- (void)will{Name}Update;',
                '- (void)should{Name}Refresh:(BOOL)force;'
            ]
        }
        
        # 存储生成的方法列表
        self.generated_methods = []

    def generate_protocol(self, name: str) -> str:
        """生成协议内容"""
        self.generated_methods = []  # 重置方法列表
        
        protocol = f'#import <Foundation/Foundation.h>\n'
        protocol += f'#import <UIKit/UIKit.h>\n\n'
        protocol += f'@protocol {name}Protocol <NSObject>\n\n'
        
        # 从每个类别中随机选择一个方法
        for category in self.method_templates.values():
            method = random.choice(category)
            formatted_method = method.format(Name=name)
            self.generated_methods.append(formatted_method)
            protocol += formatted_method + '\n'
        
        protocol += '\n@end'
        return protocol

    def get_protocol_methods(self, name: str) -> list:
        """获取最近生成的协议方法列表"""
        if not self.generated_methods:
            # 如果方法列表为空，重新生成一次
            self.generate_protocol(name)
        return self.generated_methods 
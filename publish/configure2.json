{"componentUrls": {"baseComponent_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/BaseComponent.git", "middleComponent_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/MiddleComponent.git", "mainProject_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/MainProject.git", "addiction_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAAddiction.git", "ads_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAAds.git", "floatBall_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAFloatBall.git", "login_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MALogin.git", "notice_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MANotice.git", "onekeylogin_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAOneKeyLogin.git", "phonelogin_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAPhoneLogin.git", "push_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAPush.git", "realName_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MARealName.git", "register_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MARegister.git", "resetPassword_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAResetPassword.git", "router_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MARouter.git", "share_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAShare.git", "terms_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MATerms.git", "update_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/maupdate.git", "userCenter_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAUserCenter.git", "pay_url": "http://gitlab.lzgame.top:8929/inland/ios-masdk/modules/MAPay.git"}, "master": {"BaseComponent": {"url": "baseComponent_url", "branch": "master", "path": "AllOne/BaseComponent"}, "MiddleComponent": {"url": "middleComponent_url", "branch": "master", "path": "AllOne/MiddleComponent"}, "MainProject": {"url": "mainProject_url", "branch": "master", "path": "AllOne/MainProject"}, "Addiction": {"url": "addiction_url", "branch": "master", "path": "AllOne/Modules/MAAddiction"}, "Ads": {"url": "ads_url", "branch": "master", "path": "AllOne/Modules/MAAds"}, "FloatBall": {"url": "floatBall_url", "branch": "master", "path": "AllOne/Modules/MAFloatBall"}, "Login": {"url": "login_url", "branch": "master", "path": "AllOne/Modules/MALogin"}, "Notice": {"url": "notice_url", "branch": "master", "path": "AllOne/Modules/MANotice"}, "OneKeyLogin": {"url": "onekeylogin_url", "branch": "master", "path": "AllOne/Modules/MAOneKeyLogin"}, "PhoneLogin": {"url": "phonelogin_url", "branch": "master", "path": "AllOne/Modules/MAPhoneLogin"}, "Push": {"url": "push_url", "branch": "master", "path": "AllOne/Modules/MAPush"}, "RealName": {"url": "realName_url", "branch": "master", "path": "AllOne/Modules/MARealName"}, "Register": {"url": "register_url", "branch": "master", "path": "AllOne/Modules/MARegister"}, "ResetPassword": {"url": "resetPassword_url", "branch": "master", "path": "AllOne/Modules/MAResetPassword"}, "Router": {"url": "router_url", "branch": "master", "path": "AllOne/Modules/MARouter"}, "Share": {"url": "share_url", "branch": "master", "path": "AllOne/Modules/MAShare"}, "Terms": {"url": "terms_url", "branch": "master", "path": "AllOne/Modules/MATerms"}, "Update": {"url": "update_url", "branch": "master", "path": "AllOne/Modules/MAUpdate"}, "UserCenter": {"url": "userCenter_url", "branch": "master", "path": "AllOne/Modules/MAUserCenter"}, "Pay": {"url": "pay_url", "branch": "master", "path": "AllOne/Modules/MAPay"}}}
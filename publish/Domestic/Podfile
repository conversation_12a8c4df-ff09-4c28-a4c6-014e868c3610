source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
source 'https://cdn.cocoapods.org/'
# Uncomment the  ext line to define a global platform for your project
 platform :ios, '12.0'

target 'Domestic' do
  # Comment the next line if you don't want to use dynamic frameworks
  pod 'Toast', '~> 4.1.1'
  use_frameworks!

  # Pods for Domestic
end

target 'DomesticSDK' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for DomesticSDK
  pod 'AnyThinkiOS', '6.4.87'
  pod 'AnyThinkTTSDKAdapter', '6.4.87.1'
  pod 'AnyThinkGDTSDKAdapter', '6.4.87'
  pod 'WechatOpenSDK-XCFramework'
end

target 'DomesticSDKBundle' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for DomesticSDKBundle

end

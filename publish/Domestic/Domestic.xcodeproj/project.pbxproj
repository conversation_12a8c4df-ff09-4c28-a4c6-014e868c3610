// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		02646102BE0D02397291620B /* Pods_DomesticSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 693F2D4F6F939B2F4350DD89 /* Pods_DomesticSDK.framework */; };
		1C1F18322E41B72300AF28C2 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1C1F18252E41B72300AF28C2 /* AppDelegate.m */; };
		1C1F18332E41B72300AF28C2 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 1C1F182A2E41B72300AF28C2 /* main.m */; };
		1C1F18342E41B72300AF28C2 /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1C1F182E2E41B72300AF28C2 /* SceneDelegate.m */; };
		1C1F18352E41B72300AF28C2 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1C1F18302E41B72300AF28C2 /* ViewController.m */; };
		1C1F18362E41B72300AF28C2 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1C1F18262E41B72300AF28C2 /* Assets.xcassets */; };
		1C1F18382E41B72300AF28C2 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1C1F18292E41B72300AF28C2 /* LaunchScreen.storyboard */; };
		1C1F18392E41B72300AF28C2 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1C1F182C2E41B72300AF28C2 /* Main.storyboard */; };
		1C1F18452E41B73500AF28C2 /* DomesticSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1C1F183F2E41B73500AF28C2 /* DomesticSDK.framework */; };
		1C1F18462E41B73500AF28C2 /* DomesticSDK.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 1C1F183F2E41B73500AF28C2 /* DomesticSDK.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		1C1F19802E41E6E000AF28C2 /* FirstClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C1F197F2E41E6E000AF28C2 /* FirstClass.swift */; };
		1C1F1EB02E43674500AF28C2 /* DomesticSDKBundle.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 1C1F18532E41B75400AF28C2 /* DomesticSDKBundle.bundle */; };
		1C1F26EC2E443FA100AF28C2 /* MABridgeHeader.h in Headers */ = {isa = PBXBuildFile; fileRef = 1C1F26EB2E443FA100AF28C2 /* MABridgeHeader.h */; };
		BE75F31E60E5E28AC87F8DE4 /* Pods_Domestic.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DD7F0753C4409F9671E021E5 /* Pods_Domestic.framework */; };
		E328364833D72174CF661BC9 /* Pods_DomesticSDKBundle.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E2A2DCA6F8534484FB42DC04 /* Pods_DomesticSDKBundle.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1C1F18432E41B73500AF28C2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1C1F17FF2E41B6FA00AF28C2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1C1F183E2E41B73500AF28C2;
			remoteInfo = DomesticSDK;
		};
		1C1F1EAE2E43673600AF28C2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1C1F17FF2E41B6FA00AF28C2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1C1F18522E41B75400AF28C2;
			remoteInfo = DomesticSDKBundle;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		1C1F184B2E41B73500AF28C2 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				1C1F18462E41B73500AF28C2 /* DomesticSDK.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		11C2B2187E76C54450F88859 /* Pods-DomesticSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DomesticSDK.release.xcconfig"; path = "Target Support Files/Pods-DomesticSDK/Pods-DomesticSDK.release.xcconfig"; sourceTree = "<group>"; };
		1C1F18072E41B6FA00AF28C2 /* Domestic.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Domestic.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1C1F18242E41B72300AF28C2 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		1C1F18252E41B72300AF28C2 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		1C1F18262E41B72300AF28C2 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1C1F18272E41B72300AF28C2 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1C1F18282E41B72300AF28C2 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		1C1F182A2E41B72300AF28C2 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		1C1F182B2E41B72300AF28C2 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		1C1F182D2E41B72300AF28C2 /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		1C1F182E2E41B72300AF28C2 /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		1C1F182F2E41B72300AF28C2 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		1C1F18302E41B72300AF28C2 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		1C1F183F2E41B73500AF28C2 /* DomesticSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = DomesticSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1C1F18532E41B75400AF28C2 /* DomesticSDKBundle.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DomesticSDKBundle.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		1C1F197F2E41E6E000AF28C2 /* FirstClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstClass.swift; sourceTree = "<group>"; };
		1C1F26EB2E443FA100AF28C2 /* MABridgeHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABridgeHeader.h; sourceTree = "<group>"; };
		1E4293F85D9E2A14C8BBD247 /* Pods-Domestic.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Domestic.release.xcconfig"; path = "Target Support Files/Pods-Domestic/Pods-Domestic.release.xcconfig"; sourceTree = "<group>"; };
		299B6EA22DB5143D0E141E26 /* Pods-DomesticSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DomesticSDK.debug.xcconfig"; path = "Target Support Files/Pods-DomesticSDK/Pods-DomesticSDK.debug.xcconfig"; sourceTree = "<group>"; };
		693F2D4F6F939B2F4350DD89 /* Pods_DomesticSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_DomesticSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		80CF14653E9A672EA3B648FE /* Pods-DomesticSDKBundle.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DomesticSDKBundle.release.xcconfig"; path = "Target Support Files/Pods-DomesticSDKBundle/Pods-DomesticSDKBundle.release.xcconfig"; sourceTree = "<group>"; };
		DD7F0753C4409F9671E021E5 /* Pods_Domestic.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Domestic.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E2A2DCA6F8534484FB42DC04 /* Pods_DomesticSDKBundle.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_DomesticSDKBundle.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		EA93F51F91B950D85341758C /* Pods-DomesticSDKBundle.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DomesticSDKBundle.debug.xcconfig"; path = "Target Support Files/Pods-DomesticSDKBundle/Pods-DomesticSDKBundle.debug.xcconfig"; sourceTree = "<group>"; };
		F9ADA06AD5F58B34E5AC29CE /* Pods-Domestic.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Domestic.debug.xcconfig"; path = "Target Support Files/Pods-Domestic/Pods-Domestic.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1C1F18872E41C14B00AF28C2 /* Images */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Images;
			sourceTree = "<group>";
		};
		1C1F18892E41C14B00AF28C2 /* json */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = json;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1C1F18042E41B6FA00AF28C2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C1F18452E41B73500AF28C2 /* DomesticSDK.framework in Frameworks */,
				BE75F31E60E5E28AC87F8DE4 /* Pods_Domestic.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1C1F183C2E41B73500AF28C2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				02646102BE0D02397291620B /* Pods_DomesticSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1C1F18502E41B75400AF28C2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E328364833D72174CF661BC9 /* Pods_DomesticSDKBundle.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1C1F17FE2E41B6FA00AF28C2 = {
			isa = PBXGroup;
			children = (
				1C1F18312E41B72300AF28C2 /* Domestic */,
				1C1F184D2E41B73900AF28C2 /* DomesticSDK */,
				1C1F18572E41B77C00AF28C2 /* DomesticSDKBundle */,
				1C1F18082E41B6FA00AF28C2 /* Products */,
				BAED6EEEE7318678A85B44F9 /* Pods */,
				67EAA49ADD4E2E8E13E86D16 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1C1F18082E41B6FA00AF28C2 /* Products */ = {
			isa = PBXGroup;
			children = (
				1C1F18072E41B6FA00AF28C2 /* Domestic.app */,
				1C1F183F2E41B73500AF28C2 /* DomesticSDK.framework */,
				1C1F18532E41B75400AF28C2 /* DomesticSDKBundle.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1C1F18312E41B72300AF28C2 /* Domestic */ = {
			isa = PBXGroup;
			children = (
				1C1F18242E41B72300AF28C2 /* AppDelegate.h */,
				1C1F18252E41B72300AF28C2 /* AppDelegate.m */,
				1C1F18262E41B72300AF28C2 /* Assets.xcassets */,
				1C1F18272E41B72300AF28C2 /* Info.plist */,
				1C1F18292E41B72300AF28C2 /* LaunchScreen.storyboard */,
				1C1F182A2E41B72300AF28C2 /* main.m */,
				1C1F182C2E41B72300AF28C2 /* Main.storyboard */,
				1C1F182D2E41B72300AF28C2 /* SceneDelegate.h */,
				1C1F182E2E41B72300AF28C2 /* SceneDelegate.m */,
				1C1F182F2E41B72300AF28C2 /* ViewController.h */,
				1C1F18302E41B72300AF28C2 /* ViewController.m */,
			);
			path = Domestic;
			sourceTree = "<group>";
		};
		1C1F184D2E41B73900AF28C2 /* DomesticSDK */ = {
			isa = PBXGroup;
			children = (
				1C1F197F2E41E6E000AF28C2 /* FirstClass.swift */,
				1C1F26EB2E443FA100AF28C2 /* MABridgeHeader.h */,
			);
			path = DomesticSDK;
			sourceTree = "<group>";
		};
		1C1F18572E41B77C00AF28C2 /* DomesticSDKBundle */ = {
			isa = PBXGroup;
			children = (
				1C1F18872E41C14B00AF28C2 /* Images */,
				1C1F18892E41C14B00AF28C2 /* json */,
			);
			path = DomesticSDKBundle;
			sourceTree = "<group>";
		};
		67EAA49ADD4E2E8E13E86D16 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DD7F0753C4409F9671E021E5 /* Pods_Domestic.framework */,
				693F2D4F6F939B2F4350DD89 /* Pods_DomesticSDK.framework */,
				E2A2DCA6F8534484FB42DC04 /* Pods_DomesticSDKBundle.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		BAED6EEEE7318678A85B44F9 /* Pods */ = {
			isa = PBXGroup;
			children = (
				F9ADA06AD5F58B34E5AC29CE /* Pods-Domestic.debug.xcconfig */,
				1E4293F85D9E2A14C8BBD247 /* Pods-Domestic.release.xcconfig */,
				299B6EA22DB5143D0E141E26 /* Pods-DomesticSDK.debug.xcconfig */,
				11C2B2187E76C54450F88859 /* Pods-DomesticSDK.release.xcconfig */,
				EA93F51F91B950D85341758C /* Pods-DomesticSDKBundle.debug.xcconfig */,
				80CF14653E9A672EA3B648FE /* Pods-DomesticSDKBundle.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		1C1F183A2E41B73500AF28C2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C1F26EC2E443FA100AF28C2 /* MABridgeHeader.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		1C1F18062E41B6FA00AF28C2 /* Domestic */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1C1F181F2E41B6FB00AF28C2 /* Build configuration list for PBXNativeTarget "Domestic" */;
			buildPhases = (
				D9F47F1BFD650479BC9746A7 /* [CP] Check Pods Manifest.lock */,
				1C1F18032E41B6FA00AF28C2 /* Sources */,
				1C1F18042E41B6FA00AF28C2 /* Frameworks */,
				1C1F18052E41B6FA00AF28C2 /* Resources */,
				1C1F184B2E41B73500AF28C2 /* Embed Frameworks */,
				10B6C49C90C6CA8FF0D21380 /* [CP] Embed Pods Frameworks */,
				EF14FEAE5DB111FE88195389 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1C1F1EAF2E43673600AF28C2 /* PBXTargetDependency */,
				1C1F18442E41B73500AF28C2 /* PBXTargetDependency */,
			);
			name = Domestic;
			productName = Domestic;
			productReference = 1C1F18072E41B6FA00AF28C2 /* Domestic.app */;
			productType = "com.apple.product-type.application";
		};
		1C1F183E2E41B73500AF28C2 /* DomesticSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1C1F18482E41B73500AF28C2 /* Build configuration list for PBXNativeTarget "DomesticSDK" */;
			buildPhases = (
				64256D25B304EB3681399F43 /* [CP] Check Pods Manifest.lock */,
				1C1F183A2E41B73500AF28C2 /* Headers */,
				1C1F183B2E41B73500AF28C2 /* Sources */,
				1C1F183C2E41B73500AF28C2 /* Frameworks */,
				1C1F183D2E41B73500AF28C2 /* Resources */,
				1A0101D897F9734EB82372C7 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DomesticSDK;
			productName = DomesticSDK;
			productReference = 1C1F183F2E41B73500AF28C2 /* DomesticSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
		1C1F18522E41B75400AF28C2 /* DomesticSDKBundle */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1C1F18542E41B75400AF28C2 /* Build configuration list for PBXNativeTarget "DomesticSDKBundle" */;
			buildPhases = (
				D44DBB0EC99414EF975F24ED /* [CP] Check Pods Manifest.lock */,
				1C1F184F2E41B75400AF28C2 /* Sources */,
				1C1F18502E41B75400AF28C2 /* Frameworks */,
				1C1F18512E41B75400AF28C2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1C1F18872E41C14B00AF28C2 /* Images */,
				1C1F18892E41C14B00AF28C2 /* json */,
			);
			name = DomesticSDKBundle;
			productName = DomesticSDKBundle;
			productReference = 1C1F18532E41B75400AF28C2 /* DomesticSDKBundle.bundle */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1C1F17FF2E41B6FA00AF28C2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					1C1F18062E41B6FA00AF28C2 = {
						CreatedOnToolsVersion = 16.3;
					};
					1C1F183E2E41B73500AF28C2 = {
						CreatedOnToolsVersion = 16.3;
						LastSwiftMigration = 1630;
					};
					1C1F18522E41B75400AF28C2 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 1C1F18022E41B6FA00AF28C2 /* Build configuration list for PBXProject "Domestic" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1C1F17FE2E41B6FA00AF28C2;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1C1F18082E41B6FA00AF28C2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1C1F18062E41B6FA00AF28C2 /* Domestic */,
				1C1F183E2E41B73500AF28C2 /* DomesticSDK */,
				1C1F18522E41B75400AF28C2 /* DomesticSDKBundle */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1C1F18052E41B6FA00AF28C2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C1F1EB02E43674500AF28C2 /* DomesticSDKBundle.bundle in Resources */,
				1C1F18362E41B72300AF28C2 /* Assets.xcassets in Resources */,
				1C1F18382E41B72300AF28C2 /* LaunchScreen.storyboard in Resources */,
				1C1F18392E41B72300AF28C2 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1C1F183D2E41B73500AF28C2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1C1F18512E41B75400AF28C2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		10B6C49C90C6CA8FF0D21380 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Domestic/Pods-Domestic-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Domestic/Pods-Domestic-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Domestic/Pods-Domestic-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		1A0101D897F9734EB82372C7 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DomesticSDK/Pods-DomesticSDK-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DomesticSDK/Pods-DomesticSDK-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DomesticSDK/Pods-DomesticSDK-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		64256D25B304EB3681399F43 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DomesticSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D44DBB0EC99414EF975F24ED /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DomesticSDKBundle-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D9F47F1BFD650479BC9746A7 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Domestic-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		EF14FEAE5DB111FE88195389 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Domestic/Pods-Domestic-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Domestic/Pods-Domestic-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Domestic/Pods-Domestic-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1C1F18032E41B6FA00AF28C2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C1F18322E41B72300AF28C2 /* AppDelegate.m in Sources */,
				1C1F18332E41B72300AF28C2 /* main.m in Sources */,
				1C1F18342E41B72300AF28C2 /* SceneDelegate.m in Sources */,
				1C1F18352E41B72300AF28C2 /* ViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1C1F183B2E41B73500AF28C2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C1F19802E41E6E000AF28C2 /* FirstClass.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1C1F184F2E41B75400AF28C2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1C1F18442E41B73500AF28C2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1C1F183E2E41B73500AF28C2 /* DomesticSDK */;
			targetProxy = 1C1F18432E41B73500AF28C2 /* PBXContainerItemProxy */;
		};
		1C1F1EAF2E43673600AF28C2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1C1F18522E41B75400AF28C2 /* DomesticSDKBundle */;
			targetProxy = 1C1F1EAE2E43673600AF28C2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		1C1F18292E41B72300AF28C2 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1C1F18282E41B72300AF28C2 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		1C1F182C2E41B72300AF28C2 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1C1F182B2E41B72300AF28C2 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1C1F18202E41B6FB00AF28C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F9ADA06AD5F58B34E5AC29CE /* Pods-Domestic.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AT927X29RM;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Domestic/Info.plist;
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "允许我们使用广告追踪？";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wush.identifier;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1C1F18212E41B6FB00AF28C2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E4293F85D9E2A14C8BBD247 /* Pods-Domestic.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AT927X29RM;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Domestic/Info.plist;
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "允许我们使用广告追踪？";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wush.identifier;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1C1F18222E41B6FB00AF28C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = AT927X29RM;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		1C1F18232E41B6FB00AF28C2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = AT927X29RM;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1C1F18492E41B73500AF28C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 299B6EA22DB5143D0E141E26 /* Pods-DomesticSDK.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = AT927X29RM;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "允许使用广告追踪？";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.wush.DomesticSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/DomesticSDK/MABridgeHeader.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		1C1F184A2E41B73500AF28C2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 11C2B2187E76C54450F88859 /* Pods-DomesticSDK.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = AT927X29RM;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "允许使用广告追踪？";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.wush.DomesticSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/DomesticSDK/MABridgeHeader.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		1C1F18552E41B75400AF28C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EA93F51F91B950D85341758C /* Pods-DomesticSDKBundle.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AT927X29RM;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSPrincipalClass = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wush.DomesticSDKBundle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		1C1F18562E41B75400AF28C2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 80CF14653E9A672EA3B648FE /* Pods-DomesticSDKBundle.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AT927X29RM;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSPrincipalClass = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wush.DomesticSDKBundle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1C1F18022E41B6FA00AF28C2 /* Build configuration list for PBXProject "Domestic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1C1F18222E41B6FB00AF28C2 /* Debug */,
				1C1F18232E41B6FB00AF28C2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1C1F181F2E41B6FB00AF28C2 /* Build configuration list for PBXNativeTarget "Domestic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1C1F18202E41B6FB00AF28C2 /* Debug */,
				1C1F18212E41B6FB00AF28C2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1C1F18482E41B73500AF28C2 /* Build configuration list for PBXNativeTarget "DomesticSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1C1F18492E41B73500AF28C2 /* Debug */,
				1C1F184A2E41B73500AF28C2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1C1F18542E41B75400AF28C2 /* Build configuration list for PBXNativeTarget "DomesticSDKBundle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1C1F18552E41B75400AF28C2 /* Debug */,
				1C1F18562E41B75400AF28C2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1C1F17FF2E41B6FA00AF28C2 /* Project object */;
}

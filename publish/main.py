# This is a sample Python script.
import shutil
import argparse
import urllib.request
from pbxproj import XcodeProject
from pbxproj.pbxextensions import FileOptions
from pbxproj.pbxsections.PBXBuildFile import PBXBuildFile
from pbxproj.pbxsections.PBXFileReference import PBXFileReference
from git.repo import Repo
import os
import json

# 工程所在路径
project_path = os.path.join("Domestic")

# Target所在路径
sdk_path = os.path.join(project_path, "DomesticSDK")

# Bundle所在路径
bundle_path = os.path.join(project_path, "DomesticSDKBundle")

# 工程名
project_name = "Domestic"

# Target名
sdk_name = "DomesticSDK"

# bundle名
bundle_name = "DomesticSDKBundle"

template_url = "http://gitlab.lzgame.top:8929/inland/ios-masdk/Domestic.git"

class CloneDomestic:
    # 线上模块配置参数字典
    configure = {}
    download_path = os.path.join("Temp")
    modules_path = os.path.join(download_path, "Modules")

    def cloneCodeFromUrl(self, gitUrl, branch, path):
        code_path = os.path.join(self.download_path, path)
        print(f"path:{code_path}")
        result = Repo.clone_from(gitUrl, to_path=code_path, branch=branch)
        print(f"success: {result}")

    def cloneAllFromConfigure(self, mode):
        if self.configure == None:
            return

        component_urls = self.configure["componentUrls"]
        for key, value in self.configure.items():
            if key == mode:
                for _, module in value.items():
                    module_branch = module["branch"]
                    module_url = component_urls[module["url"]]
                    module_path = module["path"]
                    print(module_branch)
                    self.cloneCodeFromUrl(module_url, module_branch, module_path)
                    
    def cloneTemplate(self, branch):
        print('--- clone template proj ---')
        template_path = os.path.join(project_name)
        result = Repo.clone_from(template_url, to_path=template_path, branch=branch)
        print('----- end clone template -----')

class CreateDomestic:
    defineBundleFile = "Macro.h"
    oldBundleName = "MADomesticSDKBundle"
    iap_swift_file = "IAPManager.swift"
    target_pay_file = "Target_Pay.m"
    share_manage_file = "MAShareManage.m"
    def copy(self, src_path, target_path):
        file_list = os.listdir(src_path)

        for file in file_list:
            path_src = os.path.join(src_path, file)
            result = os.path.isdir(path_src)
            if result:
                path = os.path.join(target_path, file)
                os.mkdir(path)
                self.copy(path_src, path)
            else:
                if path_src.endswith(".md") or path_src.endswith(".DS_Store") or path_src.endswith(".gitkeep"):
                    continue
                with open(path_src, 'rb') as rstream:
                    continuer = rstream.read()
                    path_target = os.path.join(target_path, file)
                    # 特殊处理，替换MADomesticSDKBundle、IAPManager.swift、Target_Pay.m的内容，主要是解决工程编译报错的问题
                    with open(path_target, 'wb') as wstream:
                        if file.endswith(CreateDomestic.defineBundleFile):
                            continuer = continuer.replace(CreateDomestic.oldBundleName.encode("utf8"),
                                                          bundle_name.encode("utf8"))
                        elif file.endswith(CreateDomestic.iap_swift_file):
                            continuer = continuer.replace("import MiddleComponent".encode("utf8"),
                                                          "// import MiddleComponent".encode("utf8"))
                        elif file.endswith(CreateDomestic.target_pay_file):
                            continuer = continuer.replace("#import \"MAPay/MAPay-Swift.h\"".encode("utf8"),
                                                          "#import \"DomesticSDK/DomesticSDK-Swift.h\"".encode("utf8"))
                        elif file.endswith(CreateDomestic.share_manage_file):
                            continuer = continuer.replace("#import <WXApi.h>".encode("utf8"),
                                                          "#import <WechatOpenSDK/WXApi.h>".encode("utf8"))
                        wstream.write(continuer)

    def copyBaseComponentFiles(self):
        print("----start copy Base code----")

        baseComponent_origin_path = os.path.join("Temp/AllOne/BaseComponent/BaseComponent")
        base_path = os.path.join(sdk_path, "BaseComponent")
        print(f"baseComponent:{base_path}")
        if os.path.exists(base_path):
            shutil.rmtree(base_path)
        os.mkdir(base_path)

        self.copy(baseComponent_origin_path, base_path)
        print("----end copy Base code----")

    def copyMiddleComponentFiles(self):
        print("----start copy Middle code----")
        middleComponent_origin_path = os.path.join("Temp/AllOne/MiddleComponent/MiddleComponent")
        middle_path = os.path.join(sdk_path, "MiddleComponent")
        if os.path.exists(middle_path):
            shutil.rmtree(middle_path)
        os.mkdir(middle_path)

        self.copy(middleComponent_origin_path, middle_path)
        print("----end copy Middle code----")

    def copyModulesFiles(self):
        print("----start copy Modules code----")
        modules_origin_path = os.path.join("Temp/AllOne/Modules")
        modules_path = os.path.join(sdk_path, "Modules")
        if os.path.isdir(modules_path):
            shutil.rmtree(modules_path)
        os.mkdir(modules_path)

        for dir, root, files in os.walk(modules_origin_path):
            for fold in root:
                print(f"------modules:{fold}")
                fold_path = os.path.join(dir, fold)
                for sudir, subroot, subfiles in os.walk(fold_path):
                    if "Libs" in subroot:
                       lib_path = os.path.join(modules_path, "Libs")
                       print(f"------lib_path:{lib_path}")
                       lib_origin_path = os.path.join(fold_path, "Libs")
                       if not os.path.exists(lib_path):
                           os.mkdir(lib_path)
                       self.copy(lib_origin_path, lib_path)
                       break
                    else:
                        break
                module_origin_path = os.path.join(fold_path, fold)
                module_path = os.path.join(modules_path, fold)
                os.mkdir(module_path)
                self.copy(module_origin_path, module_path)
            break
        print("----end copy Modules code----")

    def copyMainProjectFiles(self):
        print("----start copy Main code----")
        mainProject_origin_path = os.path.join("Temp/AllOne/MainProject/MainProject")
        for dir, root, files in os.walk(mainProject_origin_path):
            for file in files:
                if file.startswith("MAPublicGameApi"):
                    main_origin_path = os.path.join(dir, file)
                    with open(main_origin_path, 'r') as rstream:
                        continuer = rstream.read()
                        path_target = os.path.join(sdk_path, file)
                        with open(path_target, 'w') as wstream:
                            wstream.write(continuer)
                else:
                    main_origin_path = os.path.join(dir, file)
                    with open(main_origin_path, 'r') as rstream:
                        continuer = rstream.read()
                        sub_project_path = os.path.join(project_path, project_path)
                        path_target = os.path.join(sub_project_path, file)
                        if os.path.exists(path_target):
                            os.remove(path_target)
                        with open(path_target, 'w') as wstream:
                            if file.startswith("AppDelegate") or file.startswith("ViewController"):
                                if continuer.count('#import "MAPublicGameApi.h"') > 0:
                                    changeStr = f'#import <{sdk_name}/MAPublicGameApi.h>'
                                    continuer = continuer.replace('#import "MAPublicGameApi.h"', changeStr)
                                    print("------- replace MAPublicGameApi.h ------")
                            wstream.write(continuer)
            break
        print("----end copy Main code----")

    def copyBundleFiles(self):
        print("----start copy Bundle files----")
        bundle_origin_path = os.path.join("Temp/AllOne/MainProject/MainProject/MADomesticSDKBundle")
        if os.path.exists(bundle_path):
            shutil.rmtree(bundle_path)
        os.mkdir(bundle_path)

        self.copy(bundle_origin_path, bundle_path)
        print("----end copy Bundle files----")

    def copyAllFiles(self):
        print("---start copy code from origin---")
        self.copyBaseComponentFiles()
        self.copyMiddleComponentFiles()
        self.copyModulesFiles()
        self.copyMainProjectFiles()
        self.copyBundleFiles()

        print("---end copy code---")


class LinkDomestic:
    public_files = ["MAPublicGameApi.h"]

    def linkFirework(self, project, currentName, currentPath, parentGroup, targetName):        
        for dir, root, files in os.walk(currentPath):
            fileOptions = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False)
            openOptions = FileOptions(create_build_files=True, weak=False, embed_framework=False, code_sign_on_copy=True, header_scope=u'Public')
            if (dir.endswith(".framework")) or (dir.endswith(".xcframework")) or dir.endswith(".bundle"):
                dir_paths = os.path.split(dir)
                dir_path = dir_paths[1]
                print(f"-----1.2---dir: {dir_path}")

                project.add_file(dir_path,
                                 parent=parentGroup,
                                 tree=u'<group>',
                                 force=False,
                                 file_options=fileOptions,
                                 target_name=targetName)
                project.save()
                return
            else:
                tempGroup = project.get_or_create_group(currentName, currentName, parentGroup)
                project.save()
                avoid_files = ["module.modulemap",".DS_Store"]
                for f in files:
                    if f not in avoid_files:
                        path_files = os.path.join(currentPath, f)
                        path_file = path_files.split("/", 1)[1]
                        print(f"-2---current_file: {path_file}")
                        if LinkDomestic.public_files.count(f):
                            project.add_file(f,
                                             parent=tempGroup,
                                             tree=u'<group>',
                                             force=False,
                                             file_options=openOptions,
                                             target_name=targetName)
                        else:
                            # 个别文件需要添加-fno-objc-arc
                            add_fno_objc_arc = ['NSDictionary+custom.m']
                            if add_fno_objc_arc.count(f):
                                file_list = project.add_file(f,
                                                        parent=tempGroup,
                                                        tree=u'<group>',
                                                        force=False,
                                                        file_options=fileOptions,
                                                        target_name=targetName)
                                if len(file_list) > 0:  
                                    build_file = file_list[0]
                                    build_file.add_compiler_flags(["-fno-objc-arc"])
                                    current_flags = build_file.get_compiler_flags()
                                    print(f"Current compiler flags: {current_flags}")
                                    file_list.clear()
                                    file_list.append(build_file)
                            else:    
                                file = project.add_file(f,
                                                        parent=tempGroup,
                                                        tree=u'<group>',
                                                        force=False,
                                                        file_options=fileOptions,
                                                        target_name=targetName)
                                # 添加BDASignal的搜索路径(特殊处理)
                                if "BDASignal" in path_file:
                                    d_paths = ['$(inherited)',
                                               '$(PROJECT_DIR)/DomesticSDK/Modules/Libs/BDASignal',
                                               '${PODS_XCFRAMEWORKS_BUILD_DIR}/WechatOpenSDK-XCFramework',
                                               '${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}',
                                               '/usr/lib/swift']
                                    project.add_library_search_paths(paths=d_paths,recursive=True,escape=False,target_name=targetName)
                                    print(f"------- add library search path: {currentPath} ------")

                        project.save()

                for fold in root:
                    path_fold = os.path.join(currentPath, fold)
                    self.linkFirework(project, fold, path_fold, tempGroup, targetName)
                return

    def linkBaseComponent(self, project, baseName):
        parentGroup = project.get_or_create_group(sdk_name)
        basePath = os.path.join(sdk_path, baseName)
        print(f"---------------1.basePath: {basePath}  ----------")
        baseComponent = project.get_or_create_group(baseName, baseName, parentGroup)
        project.save()
        for dir, root, _ in os.walk(basePath):
            for fold in root:
                path_fold = os.path.join(basePath, fold)
                print(f"----------1.1baseLink: {path_fold}----------")
                self.linkFirework(project, fold, path_fold, baseComponent, sdk_name)
            return

    def linkMiddleComponent(self, project, middleName):
        parentGroup = project.get_or_create_group(sdk_name)
        middlePath = os.path.join(sdk_path, middleName)
        middleComponent = project.get_or_create_group(middleName, middleName, parentGroup)
        project.save()
        for dir, root, _ in os.walk(middlePath):
            for fold in root:
                path_fold = os.path.join(middlePath, fold)
                print(f"middle path_fold: {path_fold}")
                self.linkFirework(project, fold, path_fold, middleComponent, sdk_name)
            return

    def linkModules(self, project, modulesName):
        parentGroup = project.get_or_create_group(sdk_name)
        modulesPath = os.path.join(sdk_path, modulesName)
        modules = project.get_or_create_group(modulesName, modulesName, parentGroup)
        project.save()
        for dir, root, _ in os.walk(modulesPath):
            for fold in root:
                fold_path = os.path.join(modulesPath, fold)
                print(f"modules fold_path: {fold_path}")
                self.linkFirework(project, fold, fold_path, modules, sdk_name)
            return

    def linkMainProject(self, project):
        parentGroup = project.get_or_create_group(sdk_name)
        for dir, root, files in os.walk(sdk_path):
            publicFileOptions = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False,
                                            header_scope=u'Public')
            fileOptions = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False, header_scope=u'')
            for file in files:
                path_file = os.path.join(sdk_name, file)
                if file.endswith(".h"):
                    project.add_file(file,
                                     parent=parentGroup,
                                     tree=u'<group>',
                                     force=False,
                                     file_options=publicFileOptions,
                                     target_name=sdk_name)
                elif file.endswith(".m"):
                    project.add_file(file,
                                     parent=parentGroup,
                                     tree=u'<group>',
                                     force=False,
                                     file_options=fileOptions,
                                     target_name=sdk_name)
            break
        project.save()

    def linkBundle(self, project, bundleName):
        project.remove_group_by_name(bundleName)
        project.save()
        parentGroup = project.get_or_create_group(bundleName)
        fileOptions = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False, create_build_files=True)
        print(f"---bundle_path:{bundle_path}")
        for dir, root, files in os.walk(bundle_path):
            for fold in root:
                path_fold = bundle_name + "/" + fold
                print(f"---path:{path_fold}")
                project.add_file(path_fold, parent=parentGroup, tree=u'SOURCE_ROOT', force=True,
                                 file_options=fileOptions, target_name=bundleName)
            for file in files:
                path_file = os.path.join(bundleName, file)
                print(f"---path:{path_file}")
                project.add_file(path_file, parent=parentGroup, tree=u'SOURCE_ROOT', force=False,
                                 file_options=FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False,
                                                          header_scope=u''), target_name=bundleName)
            break

        project.save()

    def linkAll(self):
        project = XcodeProject.load(f'{project_path}/{project_name}.xcodeproj/project.pbxproj')
        print(f'开始设置Xcode工程名字 {project_path}/{project_name}.xcodeproj')
        project.add_file("usr/lib/libc++.dylib",
                         tree='SDKROOT',
                         force=False,
                         file_options=FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False))
        project.add_other_ldflags('-ObjC')

        self.linkBaseComponent(project, "BaseComponent")
        self.linkMiddleComponent(project, "MiddleComponent")
        self.linkModules(project, "Modules")
        self.linkMainProject(project)
        self.linkBundle(project, bundle_name)
        project.save()

# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    print('Welcome to CloneDomestic!')
    print('----- clear Last Temp Files ----')
    if os.path.exists("../%s" % project_name):
        shutil.rmtree("../%s" % project_name)
    parser = argparse.ArgumentParser(description='Manual to this python script')
    parser.add_argument('-i', type=str, default='', help='branch_name')
    parser.add_argument('-j', type=str, default='', help='job_name')
    args = parser.parse_args()
    mode = args.i
    job_name = args.j
    print('----- clear Last Temp Files ----')
    if os.path.exists(project_name):
        shutil.rmtree(project_name)
    cloneDomestic = CloneDomestic()
    # url = 'http://**************:9095/api/obconfig/sdkmodconf?version=%s' % job_name
    # response = urllib.request.urlopen(url)
    # config_data = response.read().decode('utf-8')
    with open('configure2.json', 'r') as f:
        config_data = f.read()
    cloneDomestic.configure = json.loads(config_data)
    
    
    #优先下载壳工程
    cloneDomestic.cloneTemplate('master')
    
    temp_path = os.path.join("Temp")
    if os.path.exists(temp_path):
        shutil.rmtree(temp_path)
    
    # 优先下载AllOne壳工程进行代码拼接
    cloneDomestic.cloneAllFromConfigure(mode)

    # 复制源代码
    createDomestic = CreateDomestic()
    createDomestic.copyAllFiles()

    # 关联所有的代码
    print(f"-----begin:{os.getcwd()}")
    linkDomestic = LinkDomestic()
    linkDomestic.linkAll()
    print(f"-----end:{os.getcwd()}")
    print('----- clear Temp Files ----')
    shutil.rmtree(temp_path)
    os.chdir(project_path)
    os.system("pod install")
    os.chdir("../")
    print('----- clear Temp Files ----')
    print('The END')
    
    shutil.move(project_name, "../%s" % project_name)

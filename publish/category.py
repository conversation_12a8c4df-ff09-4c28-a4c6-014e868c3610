# This is a sample Python script.

# Press ⌃R to execute it or replace it with your code.
# Press Double ⇧ to search everywhere for classes, files, tool windows, actions, and settings.
import re
import json
import os
import random
import CSGString
from pbxproj import XcodeProject
from pbxproj.pbxextensions import FileOptions
# 解析Objc文件的类
class AnalysisObjcFile:
    # 解析.h文件，暂时使用不到
    # 1、后期可将部分public的属性解析用于分类
    # 2、解析方法声明，用于区分筛选不加入分类
    def pase_head_file(self, file_path):
        with open(file_path, 'r') as file:
            content = file.read()
            # Extract import
            import_files = re.findall("#import\s*([^\s]*)\n", content)

            # Extract class names
            class_names = re.findall(r'@interface\s+(\w+)', content)

            # Extract properties
            properties = re.findall(r'@property\s*\(([^)]*)\)\s*(\w+)\s*([^;]*)', content)

            # Extract methods
            methods = re.findall(r'[-+]\s*\(([^)]*)\)\s*([^;]+)', content)  # re.findall(r'[-+]\s*\(([^)]*)\)\s*(\w+){', content)
            return import_files, class_names, properties, methods

    def remove_objc_comments(self, code):
        # 单行注释 
        single_line_comments_re = re.compile(r'//.*?\n')
        # 多行注释
        multi_line_comments_re = re.compile(r'/\*.*?\*/', re.DOTALL)

        code_no_single_comments = re.sub(single_line_comments_re, '\n', code)
        code_no_comments = re.sub(multi_line_comments_re, '', code_no_single_comments)
        return code_no_comments

    # 解析.m文件
    # 获取.m文件的引用头、类名、协议、属性、方法
    def parse_objc_file(self, file_path):
        # 先去除注释
        with open(file_path, 'r') as rstream:
            continuer = rstream.read()
            with open(file_path, 'w') as wstream:
                continuer = self.remove_objc_comments(continuer)
                wstream.write(continuer)

        # 在查找所需信息
        with open(file_path, 'r') as file:
            content = file.read()
            # Extract import
            imports = re.findall("#import\s*([^\s]*)\n", content)

            # Extract define
            defines = re.findall("#define\s+([^\n]*)", content)

            # Extract class names
            class_names = re.findall(r'@implementation\s*(\w+)', content)

            # Extract protocols
            protocols = re.findall("@interface\s*\w*\s*\(\s*\)\s*<(.*)>", content)

            # Extract properties
            properties = re.findall("[^//]\s*(@property\s*.*);", content)#re.findall(r'@property\s*\(([^)]*)\)\s*(\w+)\s*([^;]*)', content)

            # Extract methods
            methods = re.findall(r'\s+[-+]\s*\(([^)]*)\)\s*([^\{;]+)', content)#re.findall(r'[-+]\s*\(([^)]*)\)\s*(\w+){', content)

            if len(protocols) > 0:
                protocols = protocols[0].split(',')
            return imports, defines, class_names, protocols, properties, methods

    # 获取属性
    def getPropertyContent(self, fileName):
        properties = []
        with open(fileName, 'r') as file:
            content = file.read()
            properties = re.findall(r'@property\s*\(([^)]*)\)\s*([^;]*)', content)#re.findall(r'@property\s*\(([^)]*)\)\s*(\w+)\s*([^;]*)', content)
            # for line in file.readlines():
            #     if line.count('@property') and line.count(propertyName):
            #         return line
            # return None
        result = []
        for property in properties:
            value = []
            value.append(property[0])
            if property[1].count('*'):
                temps = property[1].rsplit('*', 1)
                value.append(temps[0])
                value.append(f'*{temps[1].strip()}')
            else:
                temps = property[1].split(' ', 1)
                value.append(temps[0])
                value.append(temps[1])
            result.append(value)

        return result

    # 切片.m文件的方法代码
    def getMethodContent(self, fileName, methodName, returnType):
        with open(fileName, 'r') as file:
            content = file.read()
            methods = re.findall(r'([-+]\s*\([^)]*\)\s*[^{]+)', content)
            file_method_end = content.rfind('}')
            method_start = content.find(f'({returnType}){methodName}')
            for method in methods:
                methodInfos = re.findall(r'[-+]\s*\(([^)]*)\)\s*([^\{]+)', method)
                if methodInfos[0][0] == returnType and methodInfos[0][1].strip() == methodName:
                    method_start = content.find(method)
                    break

            # method_start = content.rfind('-', method_start - 3, method_start)
            # method_start = content.rfind('\n', method_start - 5, method_start) + 1
            # 用入栈与出栈的方式找到方法
            method_end = content.find('}', method_start) + 1
            left_count = content.count('{', method_start, method_end)
            right_count = content.count('}', method_start, method_end)
            while left_count != right_count:
                if method_end > file_method_end:
                    method_end = content.find('-', method_start+1)
                    method_end = content.rfind('}', method_start, method_end)+1
                    break
                method_end = content.find('}', method_end) + 1
                left_count = content.count('{', method_start, method_end)
                right_count = content.count('}', method_start, method_end)

            sliced_method = content[method_start:method_end]
            return sliced_method

# 生成分类：主要作用就是读取主类的属性，生成关联对象方法。
class GenerateCategeryFile:
    # 转化基本类型为NSNumber
    def switchNumberType(self, type, value):
        switch = {
            'char': f'[NSNumber numberWithChar:{value}]',
            'unsigned char': f'[NSNumber numberWithUnsignedChar:{value}]',
            'short': f'[NSNumber numberWithShort:{value}]',
            'unsigned short': f'[NSNumber numberWithUnsignedShort:{value}]',
            'int': f'[NSNumber numberWithInt:{value}]',
            'unsigned int': f'[NSNumber numberWithUnsignedInt:{value}]',
            'long': f'[NSNumber numberWithLong:{value}]',
            'unsigned long': f'[NSNumber numberWithUnsignedLong:{value}]',
            'long long': f'[NSNumber numberWithLongLong:{value}]',
            'unsigned long long': f'[NSNumber numberWithUnsignedLongLong:{value}]',
            'float': f'[NSNumber numberWithFloat:{value}]',
            'double': f'[NSNumber numberWithDouble:{value}]',
            'BOOL': f'[NSNumber numberWithBool:{value}]',
            'NSInteger': f'[NSNumber numberWithInteger:{value}]',
            'NSUInteger': f'[NSNumber numberWithUnsignedInteger:{value}]',
            
            'CGPoint': f'[NSValue valueWithCGPoint:{value}]',
            'CGSize': f'[NSValue valueWithCGSize:{value}]',
            'CGRect': f'[NSValue valueWithCGRect:{value}]',
            'CGFloat': f'[NSNumber numberWithDouble:{value}]',# iOS: Double, mac: Float
            'dispatch_source_t': f'{value}'
        }
        return switch.get(type, None)

    # 将NSNumber转化为基本类型
    def switchNumberValue(self, type, value):
        switch = {
            'char': f'[{value} charValue]',
            'unsigned char': f'[{value} unsignedCharValue]',
            'short': f'[{value} shortValue]',
            'unsigned short': f'[{value} unsignedShortValue]',
            'int': f'[{value} intValue]',
            'unsigned int': f'[{value} unsignedIntValue]',
            'long': f'[{value} longValue]',
            'unsigned long': f'[{value} unsignedLongValue]',
            'long long': f'[{value} longLongValue]',
            'unsigned long long': f'[{value} unsignedLongLongValue]',
            'float': f'[{value} floatValue]',
            'double': f'[{value} doubleValue]',
            'BOOL': f'[{value} boolValue]',
            'NSInteger': f'[{value} integerValue]',
            'NSUInteger': f'[{value} unsignedIntegerValue]',
            
            'CGPoint': f'[{value} CGPointValue]',
            'CGSize': f'[{value} CGSizeValue]',
            'CGRect': f'[{value} CGRectValue]',
            'CGFloat': f'[{value} doubleValue]',
            'dispatch_source_t': f'{value}'
        }
        return switch.get(type, None)

    # 生属性的关联对象的setter与getter
    def generatePropertySetter(self, policies, type, name):
        funName = name
        typeName = type
        policyName = 'OBJC_ASSOCIATION_RETAIN_NONATOMIC'
        setterParamName = name
        getterParamName = name
        if name.count('*'):
            funName = funName[1:]
            typeName = f'{type} *'
            setterParamName = funName
            getterParamName = f'objc_getAssociatedObject(self, _cmd)'
        else:
            typeName = f'{type}'
            setterParamName = self.switchNumberType(type, name)
            getterParamName = self.switchNumberValue(type, f'objc_getAssociatedObject(self, _cmd)')

        if policies.count('nonatomic') and policies.count('strong'):
            policyName = 'OBJC_ASSOCIATION_RETAIN_NONATOMIC'
        elif policies.count('nonatomic') and policies.count('copy'):
            policyName = 'OBJC_ASSOCIATION_COPY_NONATOMIC'
        elif policies.count('strong'):
            policyName = 'OBJC_ASSOCIATION_RETAIN'
        elif policies.count('copy'):
            policyName = 'OBJC_ASSOCIATION_COPY'
        setterName = f'set{funName[0].upper() + funName[1:]}'

        setterLine = f'- (void){setterName}:({typeName}){funName} {{\n\tobjc_setAssociatedObject(self, @selector({funName}), {setterParamName}, {policyName});\n }}'

        getterLine = f'- ({typeName}){funName} {{\n\treturn {getterParamName};\n}}\n'
        return f'#pragma mark setter:{setterName}\n' + setterLine + f'\n#pragma mark: {funName}\n' + getterLine

    # 生成分类的.h文件，将类扩展的私有属性变成分类公开属性
    def generateCategaryHeaderFile(self, project, file_path, class_name, categery_name, imports, defines, protocols, properties, funcs):
        proj_name = project.rsplit('/', 1)[-1]
        pbxproj_path = project + '/' + proj_name + '.xcodeproj'
        project =XcodeProject.load(f'{pbxproj_path}/project.pbxproj')
        files = file_path.rsplit('/',1)
        group_base_name = os.path.basename(f'{files[0]}')
        main_group = project.get_groups_by_name(group_base_name)
        parentGroup = project.get_or_create_group(f'{files[1]}', path=f'{files[1]}', parent=main_group[0])
        with open(f'{file_path}/{class_name}+{categery_name}.h','w') as file:
            file.write(f'//\n//  {class_name}+{categery_name}.h\n//  QetrieRassSDK\n//\n\n')
            for imp in imports:
                file.write(f'#import {imp}\n')

            for define in defines:
                file.write(f'#define {define}\n')

            file.write('\nNS_ASSUME_NONNULL_BEGIN\n')
            file.write(f'@interface {class_name} ({categery_name})')

            if protocols:
                protocol = ','.join(protocols)
                file.write(f'<{protocol}>\n')

            file.write('\n')
            for property in properties:
                file.write(f'{property};\n')
            file.write('\n')
            for func in funcs:
                file.write(f'{func};\n')
            file.write('@end')
            file.write('\nNS_ASSUME_NONNULL_END\n')
        project.add_file(f'{class_name}+{categery_name}.h',
                         parent=parentGroup,
                         tree=u'<group>',
                         force=False,
                         file_options= FileOptions(weak=False, embed_framework=True, code_sign_on_copy=False),
                         target_name='%sSDK' % proj_name)
        project.save()

    # 生成分类的.m文件，具体将关联对象的属性方法与主类方法写入
    def generateCategaryContentFile(self, project, file_path, class_name, categery_name, associtions, funcs):
        proj_name = project.rsplit('/', 1)[-1]
        pbxproj_path = project + '/' + proj_name + '.xcodeproj' 
        project = XcodeProject.load(f'{pbxproj_path}/project.pbxproj')
        files = file_path.rsplit('/', 1)
        group_base_name = os.path.basename(f'{files[0]}')
        main_group = project.get_groups_by_name(group_base_name)
        parentGroup = project.get_or_create_group(f'{files[1]}', path=f'{files[1]}', parent=main_group[0])
        # parentGroup = project.get_or_create_group(f'{files[1]}')
        with open(f'{file_path}/{class_name}+{categery_name}.m', 'w') as file:
            file.write(f'//\n//  {class_name}+{categery_name}.m\n//  QetrieRassSDK\n//\n\n')
            file.write(f'#import "{class_name}+{categery_name}.h"\n')
            file.write('#import <objc/runtime.h>\n')

            file.write(f'@implementation {class_name} ({categery_name})\n')
            for ao in associtions:
                file.write(f'{ao}\n')
            for fun in funcs:
                file.write(f'{fun}\n\n')
            file.write('@end\n')
        fileOptions = FileOptions(weak=False, embed_framework=False, code_sign_on_copy=False, header_scope=u'')
        project.add_file(f'{class_name}+{categery_name}.m',
                         parent=parentGroup,
                         tree=u'<group>',
                         force=False,
                         file_options= fileOptions,
                         target_name='%sSDK' % proj_name)
        project.save()

# 修改类的结构: 主要是屏蔽旧有代码
class confirmClassFile:
    # 屏蔽扩展的属性
    def confirmProperties(self, file_path, properties):
        with open(file_path) as rstream:
            lines = rstream.readlines()
            with open(file_path, 'w') as wstream:
                for line in lines:
                    for property in properties:
                        if line.count('@property') and line.count(property):
                            line = '//' + line
                    wstream.write(line)
    # 屏蔽方法
    def confirmMethods(self, file_path, methods):
        with open(file_path, 'r') as rstream:
            continuer = rstream.read()
            with open(file_path, 'w') as wstream:
                for method in methods:
                    if continuer.count(method):
                        replace = '/*\n' + method + '\n*/\n'
                        continuer = continuer.replace(method, replace)
                wstream.write(continuer)

# 链接分类与替代分类引用
class LinkProject:
    # 链接当前文件，当前路径，父路径，target
    def linkFile(self, project, currentName, currentPath, parentGroup, targetName):
        temp_path = os.path.join(currentPath, currentName)
        if not os.path.exists(temp_path):
            fileOptions = FileOptions(weak=False, embed_framework=True, code_sign_on_copy=False)
            project.addfile(currentPath,
                            parent=parentGroup,
                            tree=u'<group>',
                            force=False,
                            file_options=fileOptions,
                            target_name=targetName)
            project.save()

    # 将主类替代为分类的引用
    def addImportCategery(self, currentPath, className, categeryName):
        for dir, root, files in os.walk(currentPath):
            for f in files:
                if (f.endswith(".m") or f.endswith(".h")) and (f.count(className) == 0) and (f.count(categeryName) == 0):
                    filePath = os.path.join(dir, f)
                    with open(filePath, 'r') as rstream:
                        continuer = rstream.read()
                        with open(filePath,'w') as wstream:
                            if continuer.count(f'#import "{className}.h"') > 0:
                                changeStr = f'#import "{className}+{categeryName}.h"'
                                continuer = continuer.replace(f'#import "{className}.h"', changeStr)
                            wstream.write(continuer)

# 总工具
class CategeryTool:
    notCategeryFuns = ['shareInstance', 'sharedInstance', 'sharedInsatnce', 'load', 'manager', 'shareMFSInstance']
    def initProject(self, project, class_name):
        project_path = project.rsplit('/', 1)[0]
        # project_name = project_path.rsplit('/',1)[1]

        for dir, root, files in os.walk(project_path):
            for f in files:
                if (f.endswith(".m")) and (f.count(class_name) != 0):
                    filePath = os.path.join(dir, f)
                    # 1. 解析出.m文件
                    analysis = AnalysisObjcFile()
                    imports, defines, class_names, protocols, properties, methods = analysis.parse_objc_file(filePath)
                    print("类名：", class_names)
                    print("引入：", imports)
                    print("定义：", defines)
                    print("协议：", protocols)
                    print("属性：", properties)
                    print("方法：", methods)

                    methodDefineCodes = []
                    for method in methods:
                        if CategeryTool.notCategeryFuns.count(method[1].strip()):
                            continue
                        methodCode = analysis.getMethodContent(filePath, method[1].strip(), method[0])
                        methodDefineCodes.append(methodCode)

                    # 2. 修改
                    confirm = confirmClassFile()
                    confirm.confirmMethods(filePath, methodDefineCodes)
                    confirm.confirmProperties(filePath, properties)

                    # 3.生成分类
                    categery = GenerateCategeryFile()
                    associationCodes = []
                    for propertyContent in analysis.getPropertyContent(filePath):
                        associationCode = categery.generatePropertySetter(propertyContent[0], propertyContent[1], propertyContent[2])
                        associationCodes.append(associationCode)

                    
                    prefix_str = CSGString.getRandomStr()
                    categery.generateCategaryHeaderFile(project, dir, class_name,prefix_str,imports,defines,protocols,properties,[])
                    categery.generateCategaryContentFile(project, dir, class_name,prefix_str,associationCodes, methodDefineCodes)

                    # 4.替换分类链接
                    link = LinkProject()
                    link.addImportCategery(project_path, class_name, prefix_str)
                    print(f"----Finish Class: {class_name}")
                    
def do_category_with_param(all_dirs, numbers, path, new_black_classes):
    # 随机文件夹
    white_dirs = random.sample(all_dirs, numbers)
    for dir, root, files in os.walk(path):
        for f in files:
            if (f.endswith('.m')) and (f.count('+') == 0) \
                    and (dir.count('.bundle') == 0) and (dir.count('.xcframework') == 0) and (dir.count('.framework') == 0):
                for white in white_dirs:
                    if dir.count(white) > 0:
                        fileName = f.split('.')[0]
                        if not new_black_classes.count(fileName):
                            tool = CategeryTool()
                            tool.initProject(path, fileName)                    

# 国内手游SDK生成类别
def do_category_path_by_gn(path):
    obfuse_map_path = '../config/__obfuscator.json'
    obfuse_map = {}
    if os.path.exists(obfuse_map_path):
        with open(obfuse_map_path, 'r') as f:
            obfuse_map = json.load(f)
    
    # 不可分类的黑名单
    black_classes = ['viewcontroller', 'MFSGameCore', 'MFSGameParam',
                     'MFSVerifyAccountView', 'MFSIdfaTipsOption',
                     'MFSMainCore', 'MFSOrnCore',
                     'InAppPurchase', 'HWRouter',
                     'MFSUserCenterView', 'MFSCommentStarView',
                     'MFSNetSesstion','MFSPopupManager', 'Configure', 'MFSPopManage',
                     'MFSAppleManage',
                     'MFSLoginView','CustomDownBoxViewCell','MFSLoginView']

    new_black_classes = []
    for item in black_classes:
        if obfuse_map.get(item) != None:
            value = obfuse_map.get(item)
            new_black_classes.append(value)
        else:
            new_black_classes.append(item)
    
    # 百分比
    percent = 0.8
    # 执行分类的文件白名单
    #  'DataCap', "Util", "Networking", "Controller", "DataService", "View"
    all_dirs = ['GNFloatBall', 'GNRealName', 'GNComment', 'GNBindPhone', 'GNLogin', 'GNPlatFormCoin' ,'GNResetPassword', 'GNUserCenter' ,
                  'GNPay', 'GNFastRegister', 'GNUpdate', 'GNHome', 'GNTerms', 'GNLogout']
    numbers = int(len(all_dirs) * percent)
    
    do_category_with_param(all_dirs, numbers, path, new_black_classes)





# Press the green button in the gutter to run the script.
# 海外手游SDK生成类别
def do_category_path(path):
    obfuse_map_path = '../config/__obfuscator.json'
    obfuse_map = {}
    if os.path.exists(obfuse_map_path):
        with open(obfuse_map_path, 'r') as f:
            obfuse_map = json.load(f)
    
    # 不可分类的黑名单
    black_classes = ['viewcontroller', 'MFSGameCore', 'MFSGameParam',
                     'MFSVerifyAccountView', 'MFSIdfaTipsOption',
                     'MFSMainCore', 'MFSOrnCore',
                     'InAppPurchase', 'HWRouter',
                     'MFSUserCenterView', 'MFSCommentStarView',
                     'MFSNetSesstion','MFSPopupManager', 'Configure', 'MFSPopManage',
                     'MFSAppleManage',
                     'MFSLoginView','CustomDownBoxViewCell','MFSTPNorLoginView','MFSHaManger','MFSLoginManage']

    new_black_classes = []
    for item in black_classes:
        if obfuse_map.get(item) != None:
            value = obfuse_map.get(item)
            new_black_classes.append(value)
        else:
            new_black_classes.append(item)
    
    # 百分比
    percent = 0.8
    # 执行分类的文件白名单
    all_dirs = ['HWAccountLogout', 'HWAdLovin', 'HWAdMob', 'HWBindThird', 'HWBuoy', 'HWComment', 'HWHW' ,'HWLogin', 'HWPay' ,
                  'HWRealName', 'HWRegister', 'HWTerms', 'HWTK', 'HWUpdate', 'HWUserCenter']
    numbers = int(len(all_dirs) * percent)
    
    do_category_with_param(all_dirs, numbers, path, new_black_classes)


if __name__ == '__main__':
    do_category_path_by_gn('../Domestic')




# See PyCharm help at https://www.jetbrains.com/help/pycharm/

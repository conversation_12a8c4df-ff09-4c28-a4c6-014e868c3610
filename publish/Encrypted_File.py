# -*- coding: utf8 -*-

import json
import os
import gtools.util.file as file_util
import CSGFile
import random
import CSGString
from Crypto.Cipher import AES
from Crypto.Cipher import DES3
import logging
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

def pad(s, block_size):
    return s + (block_size - len(s) % block_size) * bytes([block_size - len(s) % block_size])

def unpad(s):
    return s[:-ord(s[len(s) - 1:])]

def decrypt_AES(input_file, output_file, key, iv):
    try:
        backend = default_backend()
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=backend)
        decryptor = cipher.decryptor()

        with open(input_file, 'rb') as f_in, open(output_file, 'wb') as f_out:
            while True:
                chunk = f_in.read(16 * 1024)
                if len(chunk) == 0:
                    break
                decrypted_chunk = decryptor.update(chunk)
                f_out.write(decrypted_chunk)
    except IOError as e:
        logging.error(f"文件操作失败: {e}")
        raise
    except Exception as e:
        logging.error(f"解密过程中出现错误: {e}")
        raise

def encrypt_AES(input_file, output_file, key, iv):
    # 使用安全的方法生成key和iv
    if not key or not iv:
        logging.warning("Key and IV were not provided, generated randomly.")
        return
    
    try:
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()

        with open(input_file, 'rb') as f_in, open(output_file, 'wb') as f_out:
            while True:
                chunk = f_in.read(16 * 1024)
                if len(chunk) == 0:
                    break
                padded_chunk = pad(chunk, AES.block_size)
                encrypted_chunk = encryptor.update(padded_chunk)
                f_out.write(encrypted_chunk)
    except IOError as e:
        logging.error(f"文件操作失败: {e}")
        raise
    except Exception as e:
        logging.error(f"加密过程中出现错误: {e}")
        raise

def encrupt_by_3DES(input_file, output_file, key, iv):
    cipher = DES3.new(key, DES3.MODE_CBC, iv)
    chunk_size = 16 * 1024  # 16 KB chunks for large files
    
    with open(input_file, 'rb') as f_in, open(output_file, 'wb') as f_out:
        while True:
            chunk = f_in.read(chunk_size)
            if len(chunk) == 0:
                break
            elif len(chunk) % 16 != 0:  # Padding to multiple of 16 bytes
                chunk += b' ' * (16 - len(chunk) % 16)
            encrypted_chunk = cipher.encrypt(chunk)
            f_out.write(encrypted_chunk)



# 解密文件夹中的数据
def decode_dir_files(input_folder, output_folder, json_map_path):
    if not os.path.exists(input_folder):
        return
    
    if not os.path.exists(json_map_path):
        return
    
    config_data = file_util.read_file(json_map_path)
    file_path_dic = json.loads(config_data)
    
    key = b'this_is_32_bytes_long_for_AES256'
    iv = b'this_is_16_bytes'
    
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        
    for k, v in file_path_dic.items():
        print('key:%s, value:%s' %(k ,v))
        target_path = k
        ori_path = input_folder + v
        dir_path = CSGFile.get_file_dir(target_path)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        decrypt_AES(ori_path, target_path, key, iv)





# 加密文件夹中的文件
def encode_dir_files(input_folder, output_folder):
    AESKey = CSGString.generate_random_str(24).lower()
    IVKey = CSGString.generate_random_str(8).lower()
    # AESKey = 'this_is_32_bytes_long_for_AES256'
    # IVKey = 'this_is_16_bytes'
    key = AESKey.encode('utf-8')
    iv = IVKey.encode('utf-8')
    
    # 路径隐射表
    file_path_dic = {}
    
    
    local_file_list = []
    CSGFile.get_files_in_dir(local_file_list, input_folder)
    
    # 生成若干个文件夹路径  1000个
    random_file_path_list = []
    random_paths = len(local_file_list)
    for i in range(0, random_paths):
        random_dir_path = CSGString.generate_random_str(4).lower()
        if random_dir_path not in random_file_path_list:
            # 文件夹层级
            dir_path_count = random.randint(2, 5)
            append_dir_path = ''
            for j in range(0, dir_path_count):
                dir_path = CSGString.generate_random_str(4).lower()
                append_dir_path += '/' + dir_path
                if j + 1 == dir_path_count:
                    append_dir_path = append_dir_path + random_dir_path + '/'
            random_file_path_list.append(append_dir_path)
        else:
            continue
    
    if len(local_file_list) > 0 and len(random_file_path_list) > 0:
        index = 0
        for path_item in local_file_list:
            random_dir_path = random_file_path_list[index]
            new_file_name = CSGString.generate_random_str(4) + CSGString.getRandomStr() + '.data'
            file_path_dic[path_item] = random_dir_path + new_file_name
            index += 1

    print(file_path_dic)
    
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        
    for item in file_path_dic.keys():
        print('key:%s' %item)
        target_path = file_path_dic[item]
        append_path = output_folder + target_path
        dir_path = CSGFile.get_file_dir(append_path)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        encrupt_by_3DES(item, append_path, key, iv)
    
    print('生成配置文件...')
    file_util.write_file(output_folder + "/__obfuscator.json",json.dumps(file_path_dic, indent=4, ensure_ascii=False))
    
    # 配置文件加密
    config_file_path = output_folder + "/__obfuscator.json"
    config_dir_path = CSGString.generate_random_str(6).lower()
    # config_dir_path = "config"
    target_config_dir = output_folder + '/' + config_dir_path
    if not os.path.exists(target_config_dir):
        os.makedirs(target_config_dir)
    config_data_path = config_dir_path + '/' + CSGString.generate_random_str(6).lower() + '.data'
    # config_data_path = config_dir_path + '/' + "config.data"
    target_config_file_path = output_folder + '/' + config_data_path
    encrupt_by_3DES(config_file_path, target_config_file_path, key, iv)
    os.remove(config_file_path)
    
    return AESKey, IVKey, config_data_path


if __name__ == "__main__":
    input_folder = "Encode_dir"
    output_folder = "Encode_dir"
    json_path = "Encode_dir/__obfuscator.json"
    decode_dir_files(input_folder, output_folder, json_path)
    # encode_dir_files(input_folder, output_folder)

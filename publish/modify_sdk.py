# -*- coding: utf8 -*-
import CSGString
import CSGFile
import CSG<PERSON>CTools
import os
import gtools.util.file as file_util
import json

# 修改Xcode工程里target名称
def replaceKey<PERSON>ord(original_name, proj_path, replace_name):
    for main_dir, sub_dir, file_name_list in os.walk(proj_path):
        for file_name in file_name_list:
            file_name = os.path.join(main_dir, file_name)
            if file_name.endswith('.xcscheme'):
                with open(file_name, 'r') as f1, open('%s.bak' % file_name, 'w') as f2:
                    for line in f1:
                        if original_name in line:
                            line = line.replace(original_name, replace_name)
                        f2.write(line)
                os.remove(file_name)
                os.rename('%s.bak' % file_name, file_name)
                new_file_name = file_name.replace(original_name, replace_name)
                os.rename(file_name, new_file_name)

        for dir_name in sub_dir:
            temp_path = main_dir + "\\" + dir_name
            replace<PERSON><PERSON><PERSON><PERSON>(original_name, temp_path, replace_name)


# 找到目标文件路径
def find_finalpath(param, proj_path, result_path):
    for main_dir, sub_dir, file_name_list in os.walk(proj_path):
        for file_name in file_name_list:
            if file_name.endswith(param):
                result_path = os.path.join(main_dir, file_name)
                return result_path
        for dir_name in sub_dir:
            if dir_name.endswith(param):
                result_path = os.path.join(main_dir, dir_name)
                return result_path
            temp_path = main_dir + "\\" + dir_name
            find_finalpath(param, temp_path, result_path)


# 找到目标文件路径
def modify_sdk_path(sdk_prefix_name, proj_path, replace_sdk_name):
    for main_dir, sub_dir, file_name_list in os.walk(proj_path):
        if 'Demo' in main_dir:
            continue
        for item_file in file_name_list:
            if sdk_prefix_name in item_file:
                currentTargetPath = os.path.join(main_dir, item_file)
                new_dir_name = currentTargetPath.replace(sdk_prefix_name, replace_sdk_name)
                if os.path.exists(currentTargetPath) and os.path.isfile(currentTargetPath):
                    os.renames(currentTargetPath, new_dir_name)
                
            
        for dir_name in sub_dir:
            currentTargetPath = os.path.join(main_dir, dir_name)
            if sdk_prefix_name in dir_name:
                new_dir_name = currentTargetPath.replace(sdk_prefix_name, replace_sdk_name)
                if os.path.exists(currentTargetPath) and os.path.isdir(currentTargetPath):
                    os.renames(currentTargetPath, new_dir_name)
                    currentTargetPath = new_dir_name
            modify_sdk_path(sdk_prefix_name, currentTargetPath, replace_sdk_name)


#修改proj中所有有关current_pro_path的值和修改本地路径名中带有current_pro_path的值， 并最终返回修改后的路径
def modify_projname_pathname(current_pro_path, proj_name, new_proj_name):
    proj_content_file = find_finalpath('.xcodeproj', current_pro_path, '')
    proj_content_file = os.path.join(proj_content_file, 'project.pbxproj')
    with open(proj_content_file, 'r') as f1, open('%s.bak' % proj_content_file, 'w') as f2:
        for line in f1:
            if proj_name in line:
                line = line.replace(proj_name, new_proj_name)
            f2.write(line)
    os.remove(proj_content_file)
    os.rename('%s.bak' % proj_content_file, proj_content_file)
    modify_sdk_path(proj_name, current_pro_path, new_proj_name)
    current_proj_content_file = current_pro_path.replace(proj_name, new_proj_name)
    return current_proj_content_file


# 修改SDK代码中带有current_pro_path的值
def modify_code_content(proj_content_path, origin_prefix_name, new_prefix_name):
    files = []
    dict = {}
    CSGFile.files_in_dir(files, proj_content_path, [".h", ".m"])
    dict[origin_prefix_name] = new_prefix_name
    for path in files:
        CSGXCTools.replace_code_in_file(path, dict)    
    


# 修改工程名
def modify_current_projname(old_proj_name, replace_sdk_name):
    # 拼接工程路径
    proj_path = '../Mock/%s' % old_proj_name
    current_proj_content_file = modify_projname_pathname(proj_path, old_proj_name, replace_sdk_name)
    proj_content_file = find_finalpath('.xcodeproj', current_proj_content_file, '')
    # 修改scheme
    scheme_path = os.path.join(proj_content_file, 'xcshareddata/xcschemes')
    
    replaceKeyWord(old_proj_name, scheme_path, replace_sdk_name)
    # 修改代码中带有old_proj_name
    modify_code_content(current_proj_content_file, old_proj_name, replace_sdk_name)


# 修改文档
def modify_doc(doc_path,config_path,new_sdk_name, job_name):
    original_list = []
    tareget_dic = {}
    if job_name == "iOS_vST":
        tareget_dic = {"StandardGameSDK":new_sdk_name}
        original_list = ["SDPublicGameApi", "shareGameSDKApi","initializeSDKWithGameID","showGameSDKUI",
                     "logoutSDKFromGame","setupServerId","setttingServerName","settingRoleID","settingRoleName",
                     "logWithEnable","activedPlayerGameActivity","submitPlayerRoleInfo","makeDealWithAppStore"]
    elif job_name == "iOS_vGN":
        tareget_dic = {"DomesticSDK":new_sdk_name}
        original_list = [
                    'GNPublicGameApi','shareGameSDKApi','application','applicationDidBecomeActive',
                    'logWithEnable','initializeSDKWithGameID','INFORMATIONLANDLORD_LOGIN_RESULT','INFORMATIONLANDLORD_LOGOUT_RESULT',
                    'INFORMATIONLANDLORD_SERVER_STATUS','showGameSDKUI','setupServerId','settingRoleName','settingRoleID',
                    'setttingServerName','setttingServerType','settingRoleLevel','submitPlayerRoleInfo','activedPlayerGameActivity',
                    'makeDealWithAppStore','logoutSDKFromGame','updateBehaviorCap','reportPayCdkey','upgradeRoleInfo']
    elif job_name == "iOS_vTD":
        tareget_dic = {"OverseaSDK":new_sdk_name}
        original_list = ["MFSGameCore", "showLog", "runAwayStripmine", "shakingVpo","matiealingMesantoin","goAwayLocky",
                     "goTogetherLowwage","makeitTelecommerce","warmningLyf","flitpNightglass","quitingDispersal",
                     "showLoginUI","flySeventhlarge","jumpDownBronx","makeitKennelly",
                     "showComment","showMFSRewardAD","MFSGameParam","MFSReportRoleInfo","thirdAccountBindComplete",
                     "bindEmailComplete","enterUserCenter"]
    elif job_name == "iOS_vSP":
        tareget_dic = {"CreatorSDK":new_sdk_name}
        original_list = ["KMCreatorPublicApi", "shareCreatorApi","showCreatorLoginUI","setupRoleData",
                     "makeDealWithCreatorIAP","quitGameNow"]
    if os.path.exists(config_path):
        obfuse_config_data = file_util.read_file(config_path)
        obfuse_pool = json.loads(obfuse_config_data)
        for k, v in obfuse_pool.items():
            for item in original_list:
                if k == item:
                    tareget_dic[item] = v
        CSGXCTools.replace_code_in_file(doc_path, tareget_dic)
    
# 修改Demo
# def modify_demo(demo_path, obfuse_json_path, new_sdk_name):
#     return ""
    
# if __name__ == '__main__':
    # modify_doc('../doc/手游SDK_v1.x接入文档.md', '../config/__obfuscator.json',"LeiTingZNSDK", "iOS_vST")
        
import random
import os
from typing import List
import CSGString

class SwiftGenerator:
    def __init__(self):
        self.class_prefixes = [
            "NS", "UI", "Core", "Swift", "Data", "Cloud", "App", "Base",
            "Common", "Utils", "Service", "Manager", "Helper", "Handler"
        ]
        
        self.class_suffixes = [
            "Controller", "Manager", "Service", "Helper", "Handler", "Provider",
            "Factory", "Builder", "Processor", "Engine", "Center", "Utils"
        ]
        
        self.property_types = [
            ("String", "\"\""),
            ("Int", "0"),
            ("Double", "0.0"),
            ("Bool", "false"),
            ("Float", "0.0"),
            ("Date", "Date()"),
            ("[String]", "[]"),
            ("[Int]", "[]"),
            ("[String: Any]", "[:]"),
            ("TimeInterval", "0"),
            ("URL?", "nil"),
            ("Data?", "nil")
        ]
        
        self.method_prefixes = [
            "handle", "process", "update", "fetch", "get", "set",
            "configure", "setup", "create", "delete", "validate"
        ]
        
        self.method_suffixes = [
            "Data", "Content", "Result", "Response", "Request",
            "Configuration", "Settings", "Parameters", "Options"
        ]

    def generate_class_name(self) -> str:
        """生成类名"""
        prefix = random.choice(self.class_prefixes)
        middle = CSGString.getRandomStr().capitalize()
        suffix = random.choice(self.class_suffixes)
        return f"{prefix}{middle}{suffix}"

    def generate_property_name(self) -> str:
        """生成属性名"""
        words = [
            "user", "data", "config", "info", "result", "content",
            "status", "type", "mode", "state", "value", "count"
        ]
        return random.choice(words)

    def generate_method_name(self) -> str:
        """生成方法名"""
        prefix = random.choice(self.method_prefixes)
        suffix = random.choice(self.method_suffixes)
        return f"{prefix}{suffix}"

    def generate_swift_class(self) -> str:
        """生成 Swift 类"""
        class_name = self.generate_class_name()
        
        # 开始生成类
        swift_code = f"import Foundation\n\n"
        swift_code += f"@objcMembers\nclass {class_name}: NSObject {{\n\n"
        
        # 生成属性
        swift_code += "    // MARK: - Properties\n"
        num_properties = random.randint(4, 8)
        used_property_names = set()  # 用于跟踪已使用的属性名
        
        # 生成属性
        properties = []
        while len(properties) < num_properties:
            prop_type, default_value = random.choice(self.property_types)
            prop_name = self.generate_property_name()
            
            # 如果属性名已存在，继续尝试生成新的
            if prop_name in used_property_names:
                continue
            
            used_property_names.add(prop_name)
            properties.append((prop_name, prop_type, default_value))
        
        # 写入属性
        for prop_name, prop_type, default_value in properties:
            swift_code += f"    dynamic var {prop_name}: {prop_type} = {default_value}\n"
        swift_code += "\n"
        
        # 生成初始化方法
        swift_code += "    // MARK: - Initialization\n"
        swift_code += "    override init() {\n"
        swift_code += "        super.init()\n"
        swift_code += "        setupDefaults()\n"
        swift_code += "    }\n\n"
        
        # 生成 setupDefaults 方法
        swift_code += "    private func setupDefaults() {\n"
        swift_code += "        // Setup default values\n"
        swift_code += "    }\n\n"
        
        # 生成其他方法
        swift_code += "    // MARK: - Methods\n"
        num_methods = random.randint(3, 6)
        used_method_names = set()  # 用于跟踪已使用的方法名
        
        for _ in range(num_methods):
            method_name = self.generate_method_name()
            
            # 如果方法名已存在，继续尝试生成新的
            while method_name in used_method_names:
                method_name = self.generate_method_name()
            
            used_method_names.add(method_name)
            has_completion = random.choice([True, False])
            
            if has_completion:
                swift_code += f"    func {method_name}(completion: @escaping (Bool) -> Void) {{\n"
                swift_code += "        // Implementation\n"
                swift_code += "        completion(true)\n"
                swift_code += "    }\n\n"
            else:
                swift_code += f"    func {method_name}() {{\n"
                swift_code += "        // Implementation\n"
                swift_code += "    }\n\n"
        
        swift_code += "}\n"
        return swift_code, class_name

    def generate_files(self, target_path: str, count: int = 5) -> List[str]:
        """生成多个 Swift 文件"""
        generated_files = []
        check_is_exist_list = []
        for _ in range(count):
            swift_code, class_name = self.generate_swift_class()
            file_name = f"{class_name}.swift"
            file_path = os.path.join(target_path, file_name)
            if file_name not in check_is_exist_list:
                check_is_exist_list.append(file_name)
            else:
                continue
            # 确保目录存在
            os.makedirs(target_path, exist_ok=True)
            
            # 写入文件
            with open(file_path, 'w') as f:
                f.write(swift_code)
            
            generated_files.append(file_name)
            
        return generated_files 